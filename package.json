{"name": "vue-antd-pro", "version": "3.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "serve:stage": "vue-cli-service serve --mode staging", "serve:uat": "vue-cli-service serve --mode uat", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "build:preview": "vue-cli-service build --mode preview", "build:stage": "vue-cli-service build --mode staging", "build:dev": "vue-cli-service build --mode development", "build:uat": "vue-cli-service build --mode uat", "build:prod": "vue-cli-service build", "lint:nofix": "vue-cli-service lint --no-fix", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "clean": "rm -rf ./dist"}, "dependencies": {"@ant-design-vue/pro-layout": "^1.0.7", "@antv/data-set": "^0.10.2", "@form-create/ant-design-vue": "^2.5.21", "ant-design-vue": "^1.7.5", "axios": "^0.19.0", "core-js": "^3.1.2", "enquire.js": "^2.1.6", "highlight.js": "^9.12.0", "lodash": "^4.17.19", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "mathjs": "^11.1.0", "md5": "^2.2.1", "mockjs2": "1.0.8", "moment": "^2.24.0", "nprogress": "^0.2.0", "print-js": "^1.6.0", "store": "^2.0.12", "t-ui": "^1.1.1", "viser-vue": "^2.4.6", "vue": "^2.6.10", "vue-clipboard2": "^0.2.1", "vue-cropper": "0.4.9", "vue-highlight.js": "^2.2.0", "vue-i18n": "^8.17.4", "vue-quill-editor": "^3.0.6", "vue-router": "^3.1.2", "vue-svg-component-runtime": "^1.0.1", "vuex": "^3.1.1", "wangeditor": "^3.1.1"}, "devDependencies": {"@ant-design/colors": "^3.2.1", "@vue/cli-plugin-babel": "^4.0.4", "@vue/cli-plugin-eslint": "^4.0.4", "@vue/cli-plugin-router": "^4.0.4", "@vue/cli-plugin-unit-jest": "^4.0.4", "@vue/cli-plugin-vuex": "^4.0.4", "@vue/cli-service": "^4.0.4", "@vue/eslint-config-prettier": "^4.0.1", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "^1.0.0-beta.29", "babel-eslint": "^10.0.1", "babel-plugin-import": "^1.12.2", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^5.16.0", "eslint-plugin-html": "^5.0.0", "eslint-plugin-vue": "^5.2.3", "git-revision-webpack-plugin": "^3.0.6", "highlight.js": "^9.18.5", "less": "^3.0.4", "less-loader": "^5.0.0", "lint-staged": "^8.1.5", "opencollective": "^1.0.3", "opencollective-postinstall": "^2.0.2", "vue-echarts": "^3.0.7", "vue-svg-icon-loader": "^2.1.1", "vue-template-compiler": "^2.6.10", "vuepress": "^1.8.0", "vuepress-plugin-demo-container": "^0.2.0", "webpack-theme-color-replacer": "^1.3.12"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,vue}": ["vue-cli-service lint", "git add"]}}