2025-05-30 15:17:22,908 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-05-30 15:17:22,911 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-05-30 15:17:23,186 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-30 15:17:45,311 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-05-30 15:17:45,313 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-05-30 15:17:45,561 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-30 15:17:49,473 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-05-30 15:17:49,476 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-05-30 15:17:51,142 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [spring.cloud.inetutils] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.net.Inet6AddressImpl.getHostByAddr(Native Method)
 java.net.InetAddress$2.getHostByAddr(InetAddress.java:871)
 java.net.InetAddress.getHostFromNameService(InetAddress.java:620)
 java.net.InetAddress.getHostName(InetAddress.java:562)
 java.net.InetAddress.getHostName(InetAddress.java:534)
 org.springframework.cloud.commons.util.InetUtils$$Lambda$44/534906248.call(Unknown Source)
 java.util.concurrent.FutureTask.run(FutureTask.java:266)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:17:51,142 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.naming.client.listener] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2083)
 java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
 com.alibaba.nacos.client.naming.core.EventDispatcher$Notifier.run(EventDispatcher.java:118)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:17:51,142 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.client.naming.updater] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2044)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1088)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:17:51,143 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.client.naming.updater] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2083)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:17:51,143 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.naming.failover] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2083)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:17:51,143 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.naming.push.receiver] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.net.PlainDatagramSocketImpl.receive0(Native Method)
 java.net.AbstractPlainDatagramSocketImpl.receive(AbstractPlainDatagramSocketImpl.java:143)
 java.net.DatagramSocket.receive(DatagramSocket.java:822)
 com.alibaba.nacos.client.naming.core.PushReceiver.run(PushReceiver.java:73)
 java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
 java.util.concurrent.FutureTask.run(FutureTask.java:266)
 java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
 java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:17:51,143 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [logback-2] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2044)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1081)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:21:00,881 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-05-30 15:21:00,883 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-05-30 15:21:01,129 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-30 15:21:02,790 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [spring.cloud.inetutils] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.net.Inet6AddressImpl.getHostByAddr(Native Method)
 java.net.InetAddress$2.getHostByAddr(InetAddress.java:871)
 java.net.InetAddress.getHostFromNameService(InetAddress.java:620)
 java.net.InetAddress.getHostName(InetAddress.java:562)
 java.net.InetAddress.getHostName(InetAddress.java:534)
 org.springframework.cloud.commons.util.InetUtils$$Lambda$44/1224347463.call(Unknown Source)
 java.util.concurrent.FutureTask.run(FutureTask.java:266)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:21:02,790 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.naming.client.listener] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2083)
 java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
 com.alibaba.nacos.client.naming.core.EventDispatcher$Notifier.run(EventDispatcher.java:118)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:21:02,790 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.client.naming.updater] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2044)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1088)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:21:02,790 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.client.naming.updater] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2083)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:21:02,791 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.naming.failover] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2083)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:21:02,791 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.naming.push.receiver] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.net.PlainDatagramSocketImpl.receive0(Native Method)
 java.net.AbstractPlainDatagramSocketImpl.receive(AbstractPlainDatagramSocketImpl.java:143)
 java.net.DatagramSocket.receive(DatagramSocket.java:822)
 com.alibaba.nacos.client.naming.core.PushReceiver.run(PushReceiver.java:73)
 java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
 java.util.concurrent.FutureTask.run(FutureTask.java:266)
 java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
 java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-05-30 15:21:02,792 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-05-30 15:25:31,963 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-05-30 15:25:31,965 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-05-30 15:25:32,233 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-30 15:25:36,030 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-05-30 15:25:36,032 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-05-30 15:30:38,643 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-05-30 15:30:38,646 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-05-30 15:30:38,896 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-30 15:30:42,710 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-05-30 15:30:42,712 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-05-30 16:30:07,977 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-05-30 16:30:07,980 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-05-30 16:30:08,243 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-30 16:30:11,399 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-05-30 16:30:11,403 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
