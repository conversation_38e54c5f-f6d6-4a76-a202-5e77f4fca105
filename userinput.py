#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户输入获取脚本
"""

def get_user_input():
    """获取用户输入"""
    try:
        user_input = input("请输入您的命令或问题: ")
        return user_input.strip()
    except KeyboardInterrupt:
        print("\n用户取消输入")
        return ""
    except Exception as e:
        print(f"输入错误: {e}")
        return ""

if __name__ == "__main__":
    result = get_user_input()
    if result:
        print(f"用户输入: {result}")
    else:
        print("未获取到有效输入")
