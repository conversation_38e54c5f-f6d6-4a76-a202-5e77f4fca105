#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的数据库用户认证测试脚本
测试BCrypt密码验证逻辑
"""

import bcrypt
import requests
import json
import time

def test_bcrypt_password():
    """测试BCrypt密码加密和验证"""
    print("=== BCrypt密码测试 ===")
    
    # 测试密码
    passwords = ["admin123", "manager123"]
    
    for password in passwords:
        # 生成BCrypt哈希
        hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
        print(f"密码: {password}")
        print(f"BCrypt哈希: {hashed.decode('utf-8')}")
        
        # 验证密码
        is_valid = bcrypt.checkpw(password.encode('utf-8'), hashed)
        print(f"验证结果: {'✅ 正确' if is_valid else '❌ 错误'}")
        print("-" * 50)

def test_database_connection():
    """测试数据库连接（模拟）"""
    print("=== 数据库连接测试 ===")
    
    # 模拟数据库中的用户数据
    database_users = [
        {
            "id": 1,
            "username": "admin",
            "user_code": "ADMIN001",
            "password": "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKTcn5Uy6Q7J8yNWNQynVf2.Ek/2",  # admin123
            "real_name": "系统管理员",
            "oa_name": "系统管理员",
            "status": 1,
            "roles": ["ROLE_ADMIN"]
        },
        {
            "id": 2,
            "username": "manager",
            "user_code": "MGR001", 
            "password": "$2a$10$8.UrQjQKvNa8/Oy.3.5.5eKQjKtKtKtKtKtKtKtKtKtKtKtKtKtKt",  # manager123
            "real_name": "业务管理员",
            "oa_name": "业务管理员",
            "status": 1,
            "roles": ["ROLE_MANAGER"]
        }
    ]
    
    # 测试用户认证逻辑
    test_cases = [
        ("admin", "admin123", True),
        ("admin", "wrongpassword", False),
        ("manager", "manager123", True),
        ("manager", "wrongpassword", False),
        ("nonexist", "password", False)
    ]
    
    for username, password, expected in test_cases:
        print(f"测试登录: {username} / {password}")
        
        # 查找用户
        user = None
        for db_user in database_users:
            if db_user["username"] == username:
                user = db_user
                break
        
        if user is None:
            result = False
            reason = "用户不存在"
        elif user["status"] != 1:
            result = False
            reason = "用户已禁用"
        else:
            # 验证密码
            try:
                result = bcrypt.checkpw(password.encode('utf-8'), user["password"].encode('utf-8'))
                reason = "密码验证成功" if result else "密码错误"
            except Exception as e:
                result = False
                reason = f"密码验证异常: {e}"
        
        status = "✅ 成功" if result == expected else "❌ 失败"
        print(f"  预期: {'成功' if expected else '失败'}, 实际: {'成功' if result else '失败'} ({reason}) - {status}")
        print()

def test_api_endpoints():
    """测试API端点（如果服务器运行）"""
    print("=== API端点测试 ===")
    
    base_url = "http://localhost:8080"
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{base_url}/actuator/health", timeout=5)
        print(f"服务器状态: {response.status_code}")
        if response.status_code == 200:
            print("✅ 服务器正在运行")
            
            # 测试登录端点
            login_tests = [
                {"username": "admin", "password": "admin123"},
                {"username": "manager", "password": "manager123"},
                {"username": "admin", "password": "wrongpassword"}
            ]
            
            for test_data in login_tests:
                try:
                    response = requests.post(
                        f"{base_url}/bms/auth/login",
                        json=test_data,
                        timeout=10
                    )
                    print(f"登录测试 {test_data['username']}: {response.status_code}")
                    if response.status_code == 200:
                        data = response.json()
                        if "token" in data:
                            print(f"  ✅ 登录成功，获得Token: {data['token'][:20]}...")
                        else:
                            print(f"  ⚠️ 登录响应异常: {data}")
                    else:
                        print(f"  ❌ 登录失败: {response.text}")
                except Exception as e:
                    print(f"  ❌ 请求异常: {e}")
                print()
        else:
            print("❌ 服务器响应异常")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保应用已启动并运行在 http://localhost:8080")

def main():
    """主函数"""
    print("数据库用户认证测试")
    print("=" * 60)
    
    # 测试BCrypt密码功能
    test_bcrypt_password()
    print()
    
    # 测试数据库认证逻辑
    test_database_connection()
    print()
    
    # 测试API端点
    test_api_endpoints()
    
    print("=" * 60)
    print("测试完成")

if __name__ == "__main__":
    main()
