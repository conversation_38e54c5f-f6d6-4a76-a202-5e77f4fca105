#!/bin/bash

# 数据库用户认证API测试脚本
# 详细测试登录接口

echo "=== 数据库用户认证API测试 ==="

# 设置服务器地址
SERVER_URL="http://localhost:8080"

echo "服务器地址: $SERVER_URL"
echo ""

# 测试健康检查接口
echo "1. 检查服务器健康状态"
HEALTH_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" "${SERVER_URL}/actuator/health" 2>/dev/null)
HTTP_CODE=$(echo "$HEALTH_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$HEALTH_RESPONSE" | sed '/HTTP_CODE:/d')

echo "健康检查状态码: $HTTP_CODE"
echo "健康检查响应: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 服务器健康状态正常"
else
    echo "⚠️ 服务器健康状态异常"
fi

echo ""
echo "2. 测试admin用户登录"
echo "请求数据: {\"username\": \"admin\", \"password\": \"admin123\"}"

# 测试admin用户登录 - 详细输出
ADMIN_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}\nTIME_TOTAL:%{time_total}" -X POST \
  "${SERVER_URL}/bms/auth/login" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }' 2>/dev/null)

# 分离响应信息
HTTP_CODE=$(echo "$ADMIN_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
TIME_TOTAL=$(echo "$ADMIN_RESPONSE" | grep "TIME_TOTAL:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$ADMIN_RESPONSE" | sed '/HTTP_CODE:/d' | sed '/TIME_TOTAL:/d')

echo "HTTP状态码: $HTTP_CODE"
echo "响应时间: ${TIME_TOTAL}s"
echo "响应内容: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Admin登录成功"
    
    # 尝试提取token
    TOKEN=$(echo "$RESPONSE_BODY" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    if [ ! -z "$TOKEN" ]; then
        echo "✅ 获取到Token: ${TOKEN:0:30}..."
        
        echo ""
        echo "3. 使用Token访问受保护接口"
        
        # 测试访问用户列表接口
        USER_LIST_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X GET \
          "${SERVER_URL}/bms/system/user/enabled" \
          -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/json" 2>/dev/null)
        
        HTTP_CODE=$(echo "$USER_LIST_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
        RESPONSE_BODY=$(echo "$USER_LIST_RESPONSE" | sed '/HTTP_CODE:/d')
        
        echo "用户列表接口状态码: $HTTP_CODE"
        echo "用户列表响应: $RESPONSE_BODY"
        
        if [ "$HTTP_CODE" = "200" ]; then
            echo "✅ Token验证成功，可以访问受保护接口"
        else
            echo "❌ Token验证失败"
        fi
    else
        echo "⚠️ 响应中没有找到token字段"
    fi
else
    echo "❌ Admin登录失败"
    
    # 分析失败原因
    if echo "$RESPONSE_BODY" | grep -q "用户名或密码错误"; then
        echo "   失败原因: 用户名或密码错误"
    elif echo "$RESPONSE_BODY" | grep -q "用户不存在"; then
        echo "   失败原因: 用户不存在"
    elif echo "$RESPONSE_BODY" | grep -q "账户被锁定"; then
        echo "   失败原因: 账户被锁定"
    else
        echo "   失败原因: 未知错误"
    fi
fi

echo ""
echo "4. 测试manager用户登录"
echo "请求数据: {\"username\": \"manager\", \"password\": \"manager123\"}"

# 测试manager用户登录
MANAGER_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST \
  "${SERVER_URL}/bms/auth/login" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "username": "manager",
    "password": "manager123"
  }' 2>/dev/null)

HTTP_CODE=$(echo "$MANAGER_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$MANAGER_RESPONSE" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Manager登录成功"
else
    echo "❌ Manager登录失败"
fi

echo ""
echo "5. 测试错误密码"
echo "请求数据: {\"username\": \"admin\", \"password\": \"wrongpassword\"}"

# 测试错误密码
ERROR_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST \
  "${SERVER_URL}/bms/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "wrongpassword"
  }' 2>/dev/null)

HTTP_CODE=$(echo "$ERROR_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$ERROR_RESPONSE" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容: $RESPONSE_BODY"

if [ "$HTTP_CODE" != "200" ]; then
    echo "✅ 错误密码正确被拒绝"
else
    echo "❌ 错误密码被错误接受"
fi

echo ""
echo "6. 测试不存在的用户"
echo "请求数据: {\"username\": \"nonexist\", \"password\": \"password\"}"

# 测试不存在的用户
NOTFOUND_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST \
  "${SERVER_URL}/bms/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "nonexist",
    "password": "password"
  }' 2>/dev/null)

HTTP_CODE=$(echo "$NOTFOUND_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$NOTFOUND_RESPONSE" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容: $RESPONSE_BODY"

if [ "$HTTP_CODE" != "200" ]; then
    echo "✅ 不存在用户正确被拒绝"
else
    echo "❌ 不存在用户被错误接受"
fi

echo ""
echo "7. 检查应用日志（如果可能）"
echo "查找可能的错误信息..."

# 尝试查找应用日志
if [ -f "logs/application.log" ]; then
    echo "发现应用日志文件，查看最近的错误："
    tail -20 logs/application.log | grep -i error
elif [ -f "cathayfuture-opm-adapter/logs/application.log" ]; then
    echo "发现应用日志文件，查看最近的错误："
    tail -20 cathayfuture-opm-adapter/logs/application.log | grep -i error
else
    echo "未找到应用日志文件"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "总结："
echo "- 如果所有登录都失败，可能的原因："
echo "  1. 数据库中没有用户数据"
echo "  2. 密码加密方式不匹配"
echo "  3. DatabaseUserDetailsService配置问题"
echo "  4. 数据库连接问题"
echo ""
echo "建议检查："
echo "- 数据库连接是否正常"
echo "- 用户表中是否有数据"
echo "- 密码是否正确加密"
echo "- Spring Security配置是否正确"
