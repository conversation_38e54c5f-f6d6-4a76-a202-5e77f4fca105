# 密码管理指南

## 概述

本项目使用BCrypt算法对用户密码进行加密存储，确保密码安全。本文档提供密码管理的最佳实践和操作指南。

## 密码编码工具

### 工具位置
`cathayfuture-opm-adapter/src/main/java/cathayfuture/opm/adapter/utils/PasswordEncoderUtil.java`

### 使用方式

#### 方式1：命令行运行
```bash
# 在项目根目录下编译
mvn clean compile

# 运行密码编码工具
java -cp cathayfuture-opm-adapter/target/classes cathayfuture.opm.adapter.utils.PasswordEncoderUtil "your_password"

# 示例
java -cp cathayfuture-opm-adapter/target/classes cathayfuture.opm.adapter.utils.PasswordEncoderUtil "admin123"
```

#### 方式2：IDE运行
1. 在IDE中打开 `PasswordEncoderUtil.java`
2. 运行main方法，在程序参数中输入密码
3. 或直接修改args数组进行测试（仅限本地开发）

#### 方式3：代码调用
```java
// 加密密码
String encodedPassword = PasswordEncoderUtil.encode("your_password");

// 验证密码
boolean isValid = PasswordEncoderUtil.matches("your_password", encodedPassword);
```

## 密码策略

### 当前要求
- 最小长度：6位（建议8位以上）
- 支持字母、数字、特殊字符

### 建议强化策略（待实现）
- 最小长度：8位
- 必须包含大小写字母
- 必须包含数字
- 必须包含特殊字符
- 不能使用常见弱密码
- 不能与历史密码重复

## 配置文件密码管理

### 开发环境
在 `application-dev.yml` 中：
```yaml
cathay:
  auth:
    users:
      - username: admin
        password: "$2a$10$encoded_password_hash"  # 使用工具生成的BCrypt哈希
        role: ROLE_ADMIN
```

### 生产环境
**强烈建议**：
1. 使用环境变量管理敏感配置
2. 不要在配置文件中硬编码密码
3. 使用密钥管理服务（如AWS KMS、Azure Key Vault等）

### 环境变量示例
```bash
export CATHAY_ADMIN_PASSWORD="$2a$10$encoded_password_hash"
export CATHAY_MANAGER_PASSWORD="$2a$10$encoded_password_hash"
```

配置文件引用：
```yaml
cathay:
  auth:
    users:
      - username: admin
        password: "${CATHAY_ADMIN_PASSWORD}"
        role: ROLE_ADMIN
```

## 数据库用户管理

### 创建新用户
1. 使用密码编码工具生成BCrypt哈希
2. 将哈希存储到数据库的 `sys_user` 表
3. 分配适当的角色权限

### SQL示例
```sql
-- 插入新用户（密码为admin123的BCrypt哈希）
INSERT INTO sys_user (username, password, real_name, status, create_time) 
VALUES ('newuser', '$2a$10$encoded_password_hash', '新用户', 1, NOW());

-- 分配角色
INSERT INTO sys_user_role (user_id, role_id) 
VALUES (1, 2);
```

## 安全最佳实践

### 开发环境
1. ✅ 使用BCrypt加密存储密码
2. ✅ 提供独立的密码编码工具
3. ✅ 不在代码中硬编码密码
4. ⚠️ 定期更新开发环境密码

### 生产环境
1. ✅ 使用强密码策略
2. ❌ 实现密码过期机制（待开发）
3. ❌ 实现账户锁定策略（待开发）
4. ❌ 实现登录审计日志（待开发）
5. ❌ 使用多因素认证（待开发）

### 运维管理
1. 定期审查用户账户
2. 及时删除无效用户
3. 监控异常登录行为
4. 定期备份用户数据

## 常见问题

### Q: 忘记管理员密码怎么办？
A: 
1. 使用PasswordEncoderUtil生成新的密码哈希
2. 直接在数据库中更新密码字段
3. 或通过配置文件的固定用户登录

### Q: 如何批量重置密码？
A: 
1. 编写数据迁移脚本
2. 使用PasswordEncoderUtil工具生成哈希
3. 批量更新数据库记录

### Q: 密码验证失败怎么排查？
A: 
1. 检查BCrypt哈希格式是否正确
2. 确认密码编码使用相同的BCrypt实现
3. 查看应用日志中的认证详细信息

## 更新历史

- 2025-08-13: 初始版本，移除硬编码密码，创建独立工具类
- 待定: 实现密码强度验证
- 待定: 实现密码过期策略
- 待定: 添加多因素认证支持