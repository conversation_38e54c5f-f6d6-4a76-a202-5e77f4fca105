# Button按钮
### 按钮用于开始一个即时操作
---

<Common-Democode title="基本用法" description="基础的按钮用法">
  <demo-demo1></demo-demo1>
  <highlight-code slot="codeText" lang="vue">
    <template>
      <div class="demo-button">
        <div>
          <dt-button>默认按钮</dt-button>
          <dt-button type="primary">主要按钮</dt-button>
          <dt-button type="success">成功按钮</dt-button>
          <dt-button type="info">信息按钮</dt-button>
          <dt-button type="warning">警告按钮</dt-button>
          <dt-button type="danger">危险按钮</dt-button>
        </div>
      </div>
    </template>
  </highlight-code>
</Common-Democode>

<Common-Democode title="不同尺寸" description="不同尺寸用法">
  <demo-demo2></demo-demo2>
  <highlight-code slot="codeText" lang="vue">
    <template>
      <div class="demo-button">
        <div>
          <dt-button>默认按钮</dt-button>
          <dt-button type="primary" size="mini" round="">主要按钮</dt-button>
          <dt-button type="success" size="small">成功按钮</dt-button>
          <dt-button type="info" size="medium">信息按钮</dt-button>
          <dt-button type="warning">警告按钮</dt-button>
          <dt-button type="danger" :disabled="true">危险按钮</dt-button>
        </div>
      </div>
    </template>
  </highlight-code>
</Common-Democode>


### Attributes
| 参数      | 说明    | 类型      | 可选值       | 默认值   |
|---------- |-------- |---------- |-------------  |-------- |
| size     | 尺寸   | string  |   medium / small / mini            |    —     |
| type     | 类型   | string    |   primary / success / warning / danger / info / text |     —    |
| plain     | 是否朴素按钮   | boolean    | — | false   |
| round     | 是否圆角按钮   | boolean    | — | false   |
| disabled  | 是否禁用状态    | boolean   | —   | false   |
| icon  | 图标类名 | string   |  —  |  —  |
| autofocus  | 是否默认聚焦 | boolean   |  —  |  false  |
| native-type | 原生 type 属性 | string | button / submit / reset | button |

### Events
| 事件名称      | 说明    | 回调参数      
|---------- |-------- |---------- |
| click     | 点击按钮时的回调   | (event) => void  |