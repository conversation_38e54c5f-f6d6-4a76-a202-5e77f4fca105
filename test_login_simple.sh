#!/bin/bash

# 简单的登录测试脚本
# 测试数据库用户认证功能

echo "=== 数据库用户认证测试 ==="

# 设置服务器地址
SERVER_URL="http://localhost:8080"

echo "检查服务器状态..."

# 检查服务器是否运行
if curl -s --connect-timeout 5 "${SERVER_URL}/actuator/health" > /dev/null 2>&1; then
    echo "✅ 服务器正在运行"
    
    echo ""
    echo "1. 测试admin用户登录（用户名: admin, 密码: admin123）"
    
    # 测试admin用户登录
    ADMIN_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST \
      "${SERVER_URL}/bms/auth/login" \
      -H "Content-Type: application/json" \
      -d '{
        "username": "admin",
        "password": "admin123"
      }' 2>/dev/null)
    
    # 分离响应体和状态码
    HTTP_CODE=$(echo "$ADMIN_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
    RESPONSE_BODY=$(echo "$ADMIN_RESPONSE" | sed '/HTTP_CODE:/d')
    
    echo "HTTP状态码: $HTTP_CODE"
    echo "响应内容: $RESPONSE_BODY"
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ Admin登录成功"
        
        # 尝试提取token（简单方式）
        if echo "$RESPONSE_BODY" | grep -q "token"; then
            echo "✅ 响应包含token"
        else
            echo "⚠️ 响应不包含token"
        fi
    else
        echo "❌ Admin登录失败，状态码: $HTTP_CODE"
    fi
    
    echo ""
    echo "2. 测试manager用户登录（用户名: manager, 密码: manager123）"
    
    # 测试manager用户登录
    MANAGER_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST \
      "${SERVER_URL}/bms/auth/login" \
      -H "Content-Type: application/json" \
      -d '{
        "username": "manager",
        "password": "manager123"
      }' 2>/dev/null)
    
    # 分离响应体和状态码
    HTTP_CODE=$(echo "$MANAGER_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
    RESPONSE_BODY=$(echo "$MANAGER_RESPONSE" | sed '/HTTP_CODE:/d')
    
    echo "HTTP状态码: $HTTP_CODE"
    echo "响应内容: $RESPONSE_BODY"
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ Manager登录成功"
        
        if echo "$RESPONSE_BODY" | grep -q "token"; then
            echo "✅ 响应包含token"
        else
            echo "⚠️ 响应不包含token"
        fi
    else
        echo "❌ Manager登录失败，状态码: $HTTP_CODE"
    fi
    
    echo ""
    echo "3. 测试错误密码登录"
    
    # 测试错误密码
    ERROR_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST \
      "${SERVER_URL}/bms/auth/login" \
      -H "Content-Type: application/json" \
      -d '{
        "username": "admin",
        "password": "wrongpassword"
      }' 2>/dev/null)
    
    # 分离响应体和状态码
    HTTP_CODE=$(echo "$ERROR_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
    RESPONSE_BODY=$(echo "$ERROR_RESPONSE" | sed '/HTTP_CODE:/d')
    
    echo "HTTP状态码: $HTTP_CODE"
    echo "响应内容: $RESPONSE_BODY"
    
    if [ "$HTTP_CODE" != "200" ]; then
        echo "✅ 错误密码正确被拒绝，状态码: $HTTP_CODE"
    else
        echo "❌ 错误密码被错误接受"
    fi
    
    echo ""
    echo "4. 测试不存在的用户"
    
    # 测试不存在的用户
    NOTFOUND_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST \
      "${SERVER_URL}/bms/auth/login" \
      -H "Content-Type: application/json" \
      -d '{
        "username": "nonexistuser",
        "password": "password"
      }' 2>/dev/null)
    
    # 分离响应体和状态码
    HTTP_CODE=$(echo "$NOTFOUND_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
    RESPONSE_BODY=$(echo "$NOTFOUND_RESPONSE" | sed '/HTTP_CODE:/d')
    
    echo "HTTP状态码: $HTTP_CODE"
    echo "响应内容: $RESPONSE_BODY"
    
    if [ "$HTTP_CODE" != "200" ]; then
        echo "✅ 不存在用户正确被拒绝，状态码: $HTTP_CODE"
    else
        echo "❌ 不存在用户被错误接受"
    fi
    
else
    echo "❌ 无法连接到服务器 $SERVER_URL"
    echo "请确保应用已启动并运行在端口8080"
    
    # 检查Java进程
    echo ""
    echo "检查Java进程..."
    if pgrep -f "cathayfuture" > /dev/null; then
        echo "✅ 发现cathayfuture相关Java进程"
        pgrep -f "cathayfuture" | head -5
    else
        echo "❌ 未发现cathayfuture相关Java进程"
    fi
fi

echo ""
echo "=== 测试完成 ==="
