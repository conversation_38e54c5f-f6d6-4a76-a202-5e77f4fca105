#FROM harbor.dtyunxi.cn/library/frontbase:0.7.0
#FROM registry.cn-hangzhou.aliyuncs.com/yx-repo/frontbase:0.7.0
FROM registry.cn-hangzhou.aliyuncs.com/yx-repo/yxfrontbase:0.1.0-openresty 
ENV PRO_LOCAL false
RUN echo "Asia/shanghai" > /etc/timezone;
ADD dist/ /usr/local/openresty/nginx/html/

ADD enterpoint.sh /usr/share/nginx/html/enterpoint.sh
RUN chmod +x /usr/share/nginx/html/enterpoint.sh

WORKDIR /usr/local/openresty/nginx/html/

ENTRYPOINT ["/usr/share/nginx/html/enterpoint.sh"]

CMD ["nginx", "-g", "daemon off;"]

EXPOSE 80