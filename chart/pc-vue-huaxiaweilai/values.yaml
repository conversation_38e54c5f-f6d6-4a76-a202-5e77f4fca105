# Default values for choerodon-front.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: c7n-registry.dtyunxi.cn:443/idp/pc-vue-huaxiaweilai
  pullPolicy: Always

logs:
  parser: nginx

service:
  enabled: false
  port: 80
  type: ClusterIP
  name: pc-vue-huaxiaweilai

ingress:
  enabled: false
  host: y.dtyunxi.cn

env:
  open:
    PRO_API_HOST: y-api.dtyunxi.cn
    PRO_DEVOPS_HOST: y-devops.dtyunxi.cn
    PRO_AGILE_HOST: http://y-devops.dtyunxi.cn/agile-service/
    PRO_CLIENT_ID: choerodon
    PRO_COOKIE_SERVER: y.dtyunxi.cn
    PRO_HTTP: http
    PRO_FILE_SERVER: http://y-minio.dtyunxi.cn
    PRO_WEBSOCKET_SERVER: ws://y-notify.dtyunxi.cn
    PRO_LOCAL: false
    FRONTENV: dev
    PRIVATECONFIG: '{BACKEND_BASE: "",FRONT_END: "",PREVIEW: ""}'

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources,such as Minikube. If you do want to specify resources,uncomment the following
  # lines,adjust them as necessary,and remove the curly braces after 'resources:'.
  limits:
    # cpu: 100m
    memory: 500Mi
  requests:
    # cpu: 100m
    memory: 256Mi
