#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Spring Security BCrypt兼容性
"""

import bcrypt
import requests
import json

def test_spring_bcrypt_compatibility():
    """测试Spring Security BCrypt兼容性"""
    print("=== Spring Security BCrypt兼容性测试 ===")
    
    # 测试不同的BCrypt格式
    passwords = [
        {
            "password": "admin123",
            "formats": [
                "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKTcn5Uy6Q7J8yNWNQynVf2.Ek/2",  # 原始错误的
                "$2b$12$MVx18fHaQf7TQeE83VfdkufS6FtsSByhksl1p4dcsNCJUQhbG5tZa",  # 新生成的
            ]
        }
    ]
    
    for pwd_info in passwords:
        password = pwd_info["password"]
        print(f"测试密码: {password}")
        
        for i, hash_value in enumerate(pwd_info["formats"]):
            try:
                is_valid = bcrypt.checkpw(password.encode('utf-8'), hash_value.encode('utf-8'))
                status = "✅ 正确" if is_valid else "❌ 错误"
                format_type = "原始" if i == 0 else "新生成"
                print(f"  {format_type}哈希验证: {status}")
                print(f"    哈希: {hash_value}")
            except Exception as e:
                print(f"  验证异常: {e}")
        print()

def test_direct_login_with_correct_hash():
    """使用正确的哈希测试登录"""
    print("=== 直接登录测试（假设数据库已更新） ===")
    
    base_url = "http://localhost:8080"
    login_url = f"{base_url}/bms/auth/login"
    
    # 测试用例
    test_cases = [
        {"username": "admin", "password": "admin123"},
        {"username": "manager", "password": "manager123"},
    ]
    
    for test_case in test_cases:
        print(f"测试用户: {test_case['username']}")
        
        try:
            response = requests.post(
                login_url,
                json=test_case,
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                },
                timeout=10
            )
            
            print(f"  HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("  ✅ 登录成功")
                try:
                    data = response.json()
                    if data.get("token"):
                        print(f"  Token: {data['token'][:30]}...")
                    if data.get("username"):
                        print(f"  用户名: {data['username']}")
                    if data.get("authorities"):
                        print(f"  权限: {data['authorities']}")
                except:
                    print(f"  响应内容: {response.text}")
            else:
                print("  ❌ 登录失败")
                try:
                    data = response.json()
                    if data.get("error"):
                        print(f"  错误信息: {data['error']}")
                except:
                    print(f"  响应内容: {response.text}")
                    
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
        
        print()

def check_swagger_api():
    """检查Swagger API文档"""
    print("=== 检查Swagger API文档 ===")
    
    swagger_url = "http://localhost:8080/swagger-ui.html"
    
    try:
        response = requests.get(swagger_url, timeout=5)
        if response.status_code == 200:
            print(f"✅ Swagger文档可访问: {swagger_url}")
            print("建议：")
            print("1. 打开浏览器访问Swagger文档")
            print("2. 查找认证相关的API端点")
            print("3. 直接在Swagger中测试登录接口")
        else:
            print(f"❌ Swagger文档不可访问，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 无法访问Swagger文档: {e}")

def main():
    """主函数"""
    print("Spring Security数据库认证测试")
    print("=" * 50)
    
    # 测试BCrypt兼容性
    test_spring_bcrypt_compatibility()
    
    # 测试登录
    test_direct_login_with_correct_hash()
    
    # 检查Swagger
    check_swagger_api()
    
    print("=" * 50)
    print("下一步建议：")
    print("1. 如果数据库中的密码哈希还是旧的，请执行 fix_user_passwords.sql")
    print("2. 重启应用以确保配置生效")
    print("3. 检查应用日志中是否有数据库连接错误")
    print("4. 确认DatabaseUserDetailsService是否被正确注入")

if __name__ == "__main__":
    main()
