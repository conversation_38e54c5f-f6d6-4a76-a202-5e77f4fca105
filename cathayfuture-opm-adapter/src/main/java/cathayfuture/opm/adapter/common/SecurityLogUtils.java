package cathayfuture.opm.adapter.common;

import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 安全日志工具类
 * 用于敏感信息脱敏处理，避免日志中泄露敏感数据
 * 
 * <AUTHOR> Code
 * @date 2025-08-13
 */
public class SecurityLogUtils {

    private SecurityLogUtils() {
        // 工具类不允许实例化
    }

    /**
     * 用户名脱敏
     * 保留前2位和后1位，中间用*代替
     * 
     * @param username 原始用户名
     * @return 脱敏后的用户名
     */
    public static String maskUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return "***";
        }
        
        if (username.length() <= 3) {
            return "***";
        }
        
        // 只显示前2位和后1位，中间用*代替
        return username.substring(0, 2) + "***" + username.substring(username.length() - 1);
    }

    /**
     * 真实姓名脱敏
     * 中文姓名：显示姓氏，名字用*代替
     * 英文姓名：显示首字母和最后一个字母
     * 
     * @param realName 真实姓名
     * @return 脱敏后的姓名
     */
    public static String maskRealName(String realName) {
        if (!StringUtils.hasText(realName)) {
            return "***";
        }
        
        if (realName.length() == 1) {
            return "*";
        } else if (realName.length() == 2) {
            return realName.charAt(0) + "*";
        } else {
            // 保留第一个字符，其余用*代替
            return realName.charAt(0) + "**";
        }
    }

    /**
     * 邮箱地址脱敏
     * 保留@符号前的前2位和@符号后的域名，中间用*代替
     * 
     * @param email 邮箱地址
     * @return 脱敏后的邮箱
     */
    public static String maskEmail(String email) {
        if (!StringUtils.hasText(email) || !email.contains("@")) {
            return "***@***.com";
        }
        
        String[] parts = email.split("@");
        if (parts.length != 2) {
            return "***@***.com";
        }
        
        String localPart = parts[0];
        String domain = parts[1];
        
        String maskedLocalPart;
        if (localPart.length() <= 2) {
            maskedLocalPart = "***";
        } else {
            maskedLocalPart = localPart.substring(0, 2) + "***";
        }
        
        return maskedLocalPart + "@" + domain;
    }

    /**
     * 手机号脱敏
     * 保留前3位和后4位，中间用*代替
     * 
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return "***";
        }
        
        if (phone.length() < 7) {
            return "***";
        }
        
        if (phone.length() == 11) {
            // 中国大陆手机号格式
            return phone.substring(0, 3) + "****" + phone.substring(7);
        } else {
            // 其他格式
            return phone.substring(0, 3) + "***";
        }
    }

    /**
     * IP地址脱敏
     * IPv4：保留前两段，后两段用*代替
     * IPv6：保留前4位，其余用*代替
     * 
     * @param ip IP地址
     * @return 脱敏后的IP地址
     */
    public static String maskIpAddress(String ip) {
        if (!StringUtils.hasText(ip)) {
            return "***";
        }
        
        // IPv4脱敏
        if (ip.contains(".")) {
            String[] parts = ip.split("\\.");
            if (parts.length >= 4) {
                return parts[0] + "." + parts[1] + ".***.***.";
            }
        }
        
        // IPv6或其他格式的简单脱敏
        if (ip.length() > 4) {
            return ip.substring(0, 4) + "***";
        } else {
            return "***";
        }
    }

    /**
     * 用户ID脱敏
     * 对于较大的ID，保留前2位，其余用*代替
     * 
     * @param userId 用户ID
     * @return 脱敏后的用户ID字符串
     */
    public static String maskUserId(Object userId) {
        if (userId == null) {
            return "***";
        }
        
        String userIdStr = userId.toString();
        if (userIdStr.length() <= 3) {
            return "***";
        }
        
        return userIdStr.substring(0, 2) + "***";
    }

    /**
     * 权限列表脱敏
     * 对敏感权限进行脱敏处理
     * 
     * @param authorities 权限列表
     * @return 脱敏后的权限列表
     */
    public static List<String> maskAuthorities(List<String> authorities) {
        if (authorities == null || authorities.isEmpty()) {
            return authorities;
        }
        
        return authorities.stream()
                .map(SecurityLogUtils::maskSingleAuthority)
                .collect(Collectors.toList());
    }

    /**
     * 单个权限脱敏
     */
    private static String maskSingleAuthority(String authority) {
        if (!StringUtils.hasText(authority)) {
            return authority;
        }
        
        // 对于包含敏感词的权限进行脱敏
        String lowerCase = authority.toLowerCase();
        if (lowerCase.contains("admin") || lowerCase.contains("super") || 
            lowerCase.contains("root") || lowerCase.contains("system")) {
            return authority.substring(0, Math.min(4, authority.length())) + "***";
        }
        
        return authority;
    }

    /**
     * 密码信息脱敏
     * 永远不记录密码原文，只记录是否存在和长度
     * 
     * @param password 密码
     * @return 安全的密码描述信息
     */
    public static String maskPassword(String password) {
        if (!StringUtils.hasText(password)) {
            return "未设置";
        }
        
        return "已设置(" + password.length() + "位)";
    }

    /**
     * 密码哈希脱敏
     * 只记录哈希的前缀和长度，不记录完整哈希
     * 
     * @param passwordHash 密码哈希
     * @return 脱敏后的哈希信息
     */
    public static String maskPasswordHash(String passwordHash) {
        if (!StringUtils.hasText(passwordHash)) {
            return "未设置";
        }
        
        // 只显示前6位和长度信息
        String prefix = passwordHash.length() > 6 ? passwordHash.substring(0, 6) : passwordHash;
        return prefix + "***(" + passwordHash.length() + "位哈希)";
    }

    /**
     * Token脱敏
     * 只显示token的前缀和后缀，中间用*代替
     * 
     * @param token JWT token或其他token
     * @return 脱敏后的token
     */
    public static String maskToken(String token) {
        if (!StringUtils.hasText(token)) {
            return "***";
        }
        
        if (token.length() < 16) {
            return "***";
        }
        
        // 显示前8位和后4位
        return token.substring(0, 8) + "***" + token.substring(token.length() - 4);
    }

    /**
     * 通用敏感字符串脱敏
     * 根据字符串长度进行合理的脱敏处理
     * 
     * @param sensitiveData 敏感数据
     * @return 脱敏后的数据
     */
    public static String maskSensitiveData(String sensitiveData) {
        if (!StringUtils.hasText(sensitiveData)) {
            return "***";
        }
        
        int length = sensitiveData.length();
        if (length <= 3) {
            return "***";
        } else if (length <= 8) {
            return sensitiveData.charAt(0) + "***" + sensitiveData.charAt(length - 1);
        } else {
            return sensitiveData.substring(0, 3) + "***" + sensitiveData.substring(length - 2);
        }
    }
}