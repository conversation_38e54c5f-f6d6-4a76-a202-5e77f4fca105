package cathayfuture.opm.adapter.common;

import cathayfuture.opm.client.common.Result;
import cathayfuture.opm.infra.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;

/**
 * 全局异常处理器
 * 统一处理各种异常并返回标准格式的错误响应
 * 
 * <AUTHOR> Code
 * @date 2025-08-13
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BizException.class)
    public ResponseEntity<Result<Void>> handleBizException(BizException e, HttpServletRequest request) {
        log.warn("业务异常: {} - 请求路径: {}", e.getMessage(), request.getRequestURI());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(Result.badRequest(e.getMessage()));
    }

    /**
     * 处理认证异常
     */
    @ExceptionHandler({AuthenticationException.class, BadCredentialsException.class})
    public ResponseEntity<Result<Void>> handleAuthenticationException(AuthenticationException e, HttpServletRequest request) {
        log.warn("认证异常: {} - 请求路径: {}", e.getMessage(), request.getRequestURI());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(Result.unauthorized("认证失败"));
    }

    /**
     * 处理参数校验异常 - @Valid注解触发的异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Result<Void>> handleValidationException(MethodArgumentNotValidException e, HttpServletRequest request) {
        log.warn("参数校验失败 - 请求路径: {}", request.getRequestURI());
        
        StringBuilder errorMsg = new StringBuilder("参数校验失败: ");
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            errorMsg.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
        }
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(Result.badRequest(errorMsg.toString()));
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<Result<Void>> handleBindException(BindException e, HttpServletRequest request) {
        log.warn("参数绑定失败 - 请求路径: {}", request.getRequestURI());
        
        StringBuilder errorMsg = new StringBuilder("参数绑定失败: ");
        for (FieldError error : e.getFieldErrors()) {
            errorMsg.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
        }
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(Result.badRequest(errorMsg.toString()));
    }

    /**
     * 处理约束校验异常 - 直接在参数上使用@NotNull等注解触发的异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Result<Void>> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        log.warn("约束校验失败 - 请求路径: {}", request.getRequestURI());
        
        StringBuilder errorMsg = new StringBuilder("参数校验失败: ");
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            errorMsg.append(violation.getPropertyPath()).append(" ").append(violation.getMessage()).append("; ");
        }
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(Result.badRequest(errorMsg.toString()));
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Result<Void>> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        log.warn("非法参数异常: {} - 请求路径: {}", e.getMessage(), request.getRequestURI());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(Result.badRequest("参数错误: " + e.getMessage()));
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<Result<Void>> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        log.error("运行时异常: {} - 请求路径: {}", e.getMessage(), request.getRequestURI(), e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(Result.error("系统内部错误"));
    }

    /**
     * 处理其他所有异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Result<Void>> handleException(Exception e, HttpServletRequest request) {
        log.error("未处理异常: {} - 请求路径: {}", e.getMessage(), request.getRequestURI(), e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(Result.error("系统内部错误"));
    }
}