package cathayfuture.opm.adapter.utils;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 密码编码工具类
 * 用于生成BCrypt加密密码，不包含任何硬编码密码
 * 
 * 使用方式：
 * 1. 命令行运行：java cathayfuture.opm.adapter.utils.PasswordEncoderUtil [password]
 * 2. 或在IDE中运行main方法，输入密码作为参数
 * 
 * 安全说明：
 * - 此工具不包含任何硬编码密码
 * - 密码通过命令行参数传入
 * - 建议在本地环境使用，不要在生产环境运行
 * 
 * <AUTHOR> Code
 * @since 2025-08-13
 */
public class PasswordEncoderUtil {

    private static final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    /**
     * 加密密码
     * 
     * @param rawPassword 原始密码
     * @return BCrypt加密后的密码
     */
    public static String encode(String rawPassword) {
        if (rawPassword == null || rawPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        return passwordEncoder.encode(rawPassword);
    }

    /**
     * 验证密码
     * 
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    public static boolean matches(String rawPassword, String encodedPassword) {
        if (rawPassword == null || encodedPassword == null) {
            return false;
        }
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 命令行工具入口
     * 用于生成BCrypt密码哈希
     * 
     * 使用方式：
     * java cathayfuture.opm.adapter.utils.PasswordEncoderUtil [password]
     * 
     * 示例：
     * java cathayfuture.opm.adapter.utils.PasswordEncoderUtil admin123
     */
    public static void main(String[] args) {
        if (args.length != 1) {
            System.err.println("使用方式: java PasswordEncoderUtil <password>");
            System.err.println("示例: java PasswordEncoderUtil admin123");
            System.exit(1);
        }

        String password = args[0];
        
        // 基本密码安全检查
        if (password.length() < 6) {
            System.err.println("警告: 密码长度少于6位，建议使用更强的密码");
        }
        
        try {
            String encodedPassword = encode(password);
            System.out.println("=== BCrypt密码编码结果 ===");
            System.out.println("原始密码长度: " + password.length() + " 位");
            System.out.println("加密后密码: " + encodedPassword);
            System.out.println("========================");
            
            // 验证编码结果
            boolean isValid = matches(password, encodedPassword);
            System.out.println("验证结果: " + (isValid ? "✓ 正确" : "✗ 错误"));
            
        } catch (Exception e) {
            System.err.println("密码编码失败: " + e.getMessage());
            System.exit(1);
        }
    }
}