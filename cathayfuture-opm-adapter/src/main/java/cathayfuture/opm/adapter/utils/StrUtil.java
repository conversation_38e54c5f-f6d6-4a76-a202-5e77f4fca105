package cathayfuture.opm.adapter.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 9/6/22
 */
public class StrUtil {

    private static final String REGEX = "^[\\s\\p{Zs}]+|[\\s\\p{Zs}]+$";
    private static final Pattern INTEGER_PATTERN = Pattern.compile("^[-\\+]?[\\d]*$");

    private static final Pattern PERCENTAGE_PATTERN = Pattern.compile("^\\+?[1-9]\\d*(\\.\\d*)?%|\\+?0\\.\\d*[1-9]\\d*%|0(.0+)?%$");

    private StrUtil() {
    }

    /**
     * 过滤字符串前后的全角，半角空白符号
     *
     * @param str
     * @return
     */
    public static String trimPlus(String str) {
        if (StringUtils.isNotBlank(str)) {
            return str.replaceAll(REGEX, "");
        }
        return "";
    }

    /**
     * 判断是否为整数
     * @param str
     * @return
     */
    public static boolean isInteger(String str) {
        return INTEGER_PATTERN.matcher(str).matches();
    }

    /**
     * 判断是否为正数+百分号的形式
     * @param str
     * @return
     */
    public static boolean isPercentage(String str) {
        return PERCENTAGE_PATTERN.matcher(str).matches();
    }
}
