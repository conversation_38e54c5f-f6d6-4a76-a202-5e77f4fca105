package cathayfuture.opm.adapter.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.format.FastDateFormat;

import java.util.Date;
import java.util.Objects;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/26/22
 */
public class DateUtil {

    public static String normalFormat(Date date) {
        FastDateFormat normalDateTimeFormator = DatePattern.NORM_DATETIME_FORMAT;
        if (Objects.nonNull(date)) {
            return normalDateTimeFormator.format(date);
        }
        return "";
    }
}
