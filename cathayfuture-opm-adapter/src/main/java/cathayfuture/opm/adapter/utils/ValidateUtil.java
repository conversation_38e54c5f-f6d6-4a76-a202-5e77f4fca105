package cathayfuture.opm.adapter.utils;

import cathayfuture.opm.client.AppException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.HibernateValidator;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.List;
import java.util.Set;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 4/21/22
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ValidateUtil {
    private static final Validator VALIDATOR = Validation.byProvider(HibernateValidator.class)
            .configure()
            .failFast(true)
            .buildValidatorFactory()
            .getValidator();

    /**
     * 实体校验
     *
     * @param obj 对象
     * @throws AppException 校验异常
     */
    public static <T> void validate(T obj) throws AppException {
        Set<ConstraintViolation<T>> constraintViolations = VALIDATOR.validate(obj);
        if (!constraintViolations.isEmpty()) {
            ConstraintViolation<T> validateInfo = constraintViolations.iterator().next();
            // validateInfo.getMessage() 校验不通过时的信息，即message对应的值
            log.error(validateInfo.getMessage());
            throw new AppException(validateInfo.getMessage());
        }
    }

    /**
     * 校验数组是否不为空，并且选择是否抛出异常信息
     *
     * @param list            集合
     * @param continueProcess 是否继续处理
     */
    public static <T> boolean isNotEmptyList(List<T> list, boolean continueProcess, String customMessage) {
        String errorMsg = StringUtils.isEmpty(customMessage) ? "参数数组为空" : customMessage;
        if (CollectionUtils.isEmpty(list)) {
            log.error(errorMsg);
            if (!continueProcess) {
                throw new AppException(errorMsg);
            }
            return false;
        }
        return true;
    }

    /**
     * 分组校验
     *
     * @param validateDataList 待校验集合
     * @param validateGroup    校验分组
     * @param <T>              类型
     */
    public static <T> void validateParams(List<T> validateDataList, Class... validateGroup) {
        if (CollectionUtils.isEmpty(validateDataList)) {
            throw new AppException("参数校验失败");
        }
        for (T validateData : validateDataList) {
            validateParam(validateData, validateGroup);
        }
    }

    public static <T> void validateParam(T validateData, Class... validateGroup) {
        if (validateData == null) {
            throw new AppException("参数校验失败");
        }

        Set<ConstraintViolation<T>> validate = VALIDATOR.validate(validateData, validateGroup);
        if (!CollectionUtils.isEmpty(validate)) {
            validate.stream().findFirst().ifPresent(error -> {
                log.error("validator参数校验不通过,报错信息:{}", error.getMessage());
                throw new AppException(error.getMessage());
            });
        }
    }
}
