package cathayfuture.opm.adapter.bms.student.dto.response;

import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import cathayfuture.opm.domain.order.enums.BusinessUnitEnum;
import cathayfuture.opm.domain.student.enums.GenderEnum;
import cathayfuture.opm.domain.student.enums.RegisterStatusEnum;
import cathayfuture.opm.domain.student.enums.StudentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 9/2/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(name = "BmsStudentRespDTO", description = "学生信息返回体")
public class BmsStudentRespDTO extends StudentRespDTO {

    @Schema(description = "序号")
    private Integer orderNumber;
    @Schema(description = "入园时间字符串")
    private String admissionDateStr;
    @Schema(description = "业态描述")
    private String businessUnitStr;
    @Schema(description = "性别描述")
    private String genderStr;
    @Schema(description = "学生类型描述")
    private String studentTypeStr;
    @Schema(description = "拼接后的家庭住址")
    private String wholeHomeAddress;

    public String getAdmissionDateStr() {
        return this.getAdmissionDate();
    }

    public String getBusinessUnitStr() {
        return Optional.ofNullable(this.getBusinessUnit()).map(BusinessUnitEnum::getEnumDescription).orElse("");
    }

    public static BmsStudentRespDTO convertFrom(StudentRespDTO studentRespDTO) {
        BmsStudentRespDTO bmsStudentRespDTO = new BmsStudentRespDTO();
        BeanUtils.copyProperties(studentRespDTO, bmsStudentRespDTO);
        bmsStudentRespDTO.setBirthday(Optional.ofNullable(studentRespDTO.getBirthday()).map(d -> LocalDate.parse(studentRespDTO.getBirthday(), DateTimeFormatter.ISO_LOCAL_DATE)).orElse(null));
        bmsStudentRespDTO.setAdmissionDate(Optional.ofNullable(studentRespDTO.getAdmissionDate()).map(d -> LocalDate.parse(studentRespDTO.getAdmissionDate(), DateTimeFormatter.ISO_LOCAL_DATE)).orElse(null));
        return bmsStudentRespDTO;
    }

    public String getGenderStr() {
        return Optional.ofNullable(this.getGender()).map(GenderEnum::getEnumDescription).orElse("未知");
    }

    public String getStudentTypeStr() {
        return Optional.ofNullable(this.getStudentType()).map(StudentTypeEnum::getEnumDescription).orElse("未知");
    }

    public String getBirthdayStr() {
        return this.getBirthday();
    }

    public String getWholeHomeAddress() {
        return String.join("", Optional.ofNullable(this.getProvinceName()).orElse(""),
                String.join(StringUtils.isNotBlank(this.getHomeAddress()) ? "-" : "", Optional.ofNullable(this.getRegionName()).orElse(""), Optional.ofNullable(this.getHomeAddress()).orElse("")));
    }

    public String getRegisterStatusStr(){
        return Optional.ofNullable(this.getRegisterStatus()).map(RegisterStatusEnum::getEnumDescription).orElse("未知");
    }
}
