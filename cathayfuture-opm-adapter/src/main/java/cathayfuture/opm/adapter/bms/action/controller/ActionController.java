package cathayfuture.opm.adapter.bms.action.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.adapter.bms.action.dto.response.BmsActionRespDTO;
import cathayfuture.opm.adapter.bms.order.dto.response.BmsOrderRespDTO;
import cathayfuture.opm.client.action.api.ActionAppService;
import cathayfuture.opm.client.action.dto.response.ActionRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * <AUTHOR>
 * @since 10/14/22
 */
@Slf4j
@RestController(value = "BmsActionController")
@Tag(name = "ActionController", description = "BMS操作历史相关接口")
@RequestMapping("/bms/actions")
public class ActionController {

    private final ActionAppService actionAppService;


    public ActionController(ActionAppService actionAppService) {
        this.actionAppService = actionAppService;
    }

    @GetMapping("/actionList")
    @Operation(summary = "操作历史", description = "操作历史",
            parameters = {@Parameter(name = "module", description = "模块", content = @Content(schema = @Schema(anyOf = {String.class}))),
                    @Parameter(name = "businessId", description = "业务Id", content = @Content(schema = @Schema(anyOf = {Integer.class})))},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {ActionRespDTO.class})))})
    public CommonResponseDTO<List<BmsActionRespDTO>> actionList(@RequestParam("module") String module,
                                                @RequestParam("businessId") Integer businessId) {
        List<ActionRespDTO> actionRespDTOList = actionAppService.actionList(module, businessId);
        List<BmsActionRespDTO> result = actionRespDTOList
                .stream()
                .map(BmsActionRespDTO::convertFrom)
                .collect(Collectors.toList());
        return CommonResponseDTO.success(result);
    }

}
