package cathayfuture.opm.adapter.bms.order.exporter;

import cathayfuture.opm.adapter.bms.order.dto.response.BmsOrderRespDTO;
import com.taslyware.framework.exceltools.excel.enums.ExcelColumn;
import com.taslyware.framework.exceltools.excel.utils.ListUtils;
import com.taslyware.framework.exceltools.excel.utils.lambda.LambdaUtils;
import com.taslyware.framework.exceltools.excel.writer.ExcelWriter;
import com.taslyware.framework.exceltools.exporter.handler.IExporterHandler;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 9/5/22
 */
@Order(3)
@Component
public class DsfOrderExporterHandler implements IOrderExporterHandler {
    @Override
    public <T> void handleExport(List<T> list, Object data, ExcelWriter writer) {
        writer.setTitle("代收费订单导出文件");
        writer.addList(ExcelColumn.A,
                1,
                "序号,订单编号,学生姓名,学生学号,班级,学生联系方式,收费项目,收费金额,订单创建时间,付款时间,付款方式,支付状态,订单状态,明细备注,备注,作废原因,作废时间",
                ListUtils.list2Dot(
                        ListUtils.arrayToList(
                                LambdaUtils.property(BmsOrderRespDTO::getOrderNumber),
                                LambdaUtils.property(BmsOrderRespDTO::getCode),
                                LambdaUtils.property(BmsOrderRespDTO::getStudentName),
                                LambdaUtils.property(BmsOrderRespDTO::getStudentCode),
                                LambdaUtils.property(BmsOrderRespDTO::getGrade),
                                LambdaUtils.property(BmsOrderRespDTO::getContactInfo),
                                LambdaUtils.property(BmsOrderRespDTO::getChargeItem),
                                LambdaUtils.property(BmsOrderRespDTO::getChargeAmount),
                                LambdaUtils.property(BmsOrderRespDTO::getCreateTimeStr),
                                LambdaUtils.property(BmsOrderRespDTO::getPaymentTimeStr),
                                LambdaUtils.property(BmsOrderRespDTO::getPaymentModeName),
                                LambdaUtils.property(BmsOrderRespDTO::getPaymentStatusName),
                                LambdaUtils.property(BmsOrderRespDTO::getStatusName),
                                LambdaUtils.property(BmsOrderRespDTO::getDetailRemark),
                                LambdaUtils.property(BmsOrderRespDTO::getRemark),
                                LambdaUtils.property(BmsOrderRespDTO::getDeleteReason),
                                LambdaUtils.property(BmsOrderRespDTO::getDeleteTimeStr)
                        )
                ),
                list
        );
    }
}
