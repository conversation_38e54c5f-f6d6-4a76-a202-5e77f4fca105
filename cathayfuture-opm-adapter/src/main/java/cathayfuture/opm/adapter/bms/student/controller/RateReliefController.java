package cathayfuture.opm.adapter.bms.student.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.adapter.bms.student.dto.response.BmsStudentRespDTO;
import cathayfuture.opm.adapter.utils.PageUtil;
import cathayfuture.opm.client.attendance.dto.request.AskedForLeaveReqDto;
import cathayfuture.opm.client.attendance.dto.request.AskedForLeaveSaveReqDto;
import cathayfuture.opm.client.attendance.dto.response.AskedForLeaveRespDto;
import cathayfuture.opm.client.student.api.RateReliefAppService;
import cathayfuture.opm.client.student.api.query.RateReliefQueryAppService;
import cathayfuture.opm.client.student.dto.request.RateReliefPageReqDTO;
import cathayfuture.opm.client.student.dto.request.ReteReliefAddReqDto;
import cathayfuture.opm.client.student.dto.response.RateReliefPageRespDTO;
import cathayfuture.opm.client.student.dto.response.RateReliefQueryStudentInfoByNameRespDto;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.GsonBuilder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 费用减免
 * @date 2023/3/31 10:04
 */

@Slf4j
@RestController(value = "RateReliefController")
@Tag(name = "RateReliefController", description = "费用减免api")
@RequestMapping("/bms/rateRelief")
public class RateReliefController {

    private RateReliefQueryAppService rateReliefQueryAppService;

    private RateReliefAppService rateReliefAppService;

    public RateReliefController(RateReliefQueryAppService rateReliefQueryAppService, RateReliefAppService rateReliefAppService) {
        this.rateReliefQueryAppService = rateReliefQueryAppService;
        this.rateReliefAppService = rateReliefAppService;
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询", description = "分页查询",
            parameters = {@Parameter(name = "filter", description = "过滤条件Json字符串", content = @Content(schema = @Schema(anyOf = {RateReliefPageReqDTO.class}))),
                    @Parameter(name = "pageNum", description = "页码(默认1)"),
                    @Parameter(name = "pageSize", description = "页数(默认10)")},
            responses = {@ApiResponse(description = "分页查询结果", content = @Content(schema = @Schema(anyOf = RateReliefPageRespDTO.class)))})
    public CommonResponseDTO<IPage<RateReliefPageRespDTO>> pageOrder(@RequestParam("filter") String filter,
                                                                    @RequestParam(defaultValue = "1") Integer pageNum,
                                                                    @RequestParam(defaultValue = "10") Integer pageSize) {
        RateReliefPageReqDTO reqDto = new GsonBuilder().create().fromJson(filter, RateReliefPageReqDTO.class);
        IPage<RateReliefPageRespDTO> result = rateReliefQueryAppService.page(reqDto, PageUtil.makePageInfo(pageNum,pageSize));
        return CommonResponseDTO.success(result);
    }

    @GetMapping("/queryStudentInfoListByStudentName")
    @Operation(summary = "根据学生姓名查询学生详情和费用标准", description = "根据学生姓名查询学生详情和费用标准",
            parameters = {@Parameter(name = "studentName", description = "studentName")},
            responses = {@ApiResponse(description = "学生详情", content = @Content(schema = @Schema(anyOf = {RateReliefQueryStudentInfoByNameRespDto.class})))})
    public CommonResponseDTO<List<RateReliefQueryStudentInfoByNameRespDto>> queryListByStudentName(@RequestParam("studentName") String studentName){
        return CommonResponseDTO.success(rateReliefQueryAppService.queryListByStudentName(studentName));
    }

    @PostMapping("/save")
    @Operation(summary = "新增", description = "新增",
            parameters = {@Parameter(name = "ReteReliefAddReqDto", description = "ReteReliefAddReqDto")},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {String.class})))})
    public CommonResponseDTO save(@RequestBody @Valid ReteReliefAddReqDto reqDto){
        rateReliefAppService.save(reqDto);
        return CommonResponseDTO.success();
    }
    @PostMapping("/delete/{id}")
    @Operation(summary = "删除操作", description = "删除操作",
            parameters = {@Parameter(name = "id", description = "id")},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {Boolean.class})))})
    public CommonResponseDTO<String> delete(@PathVariable("id") Integer id) {
        rateReliefAppService.delete(id);
        return CommonResponseDTO.success("删除操作成功");
    }


}
