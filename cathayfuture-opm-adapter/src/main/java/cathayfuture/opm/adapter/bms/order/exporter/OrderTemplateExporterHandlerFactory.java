package cathayfuture.opm.adapter.bms.order.exporter;

import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 9/5/22
 */
@Component
public class OrderTemplateExporterHandlerFactory {
    private final List<IOrderTemplateExporterHandler> orderTemplateExporterHandlerList;


    public OrderTemplateExporterHandlerFactory(List<IOrderTemplateExporterHandler> orderTemplateExporterHandlerList) {
        this.orderTemplateExporterHandlerList = orderTemplateExporterHandlerList;
    }

    public List<IOrderTemplateExporterHandler> getOrderTemplateExporterHandler() {
        return this.orderTemplateExporterHandlerList;
    }
}
