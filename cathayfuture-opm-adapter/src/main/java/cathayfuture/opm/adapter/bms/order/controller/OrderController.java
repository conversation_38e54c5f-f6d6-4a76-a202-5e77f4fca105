package cathayfuture.opm.adapter.bms.order.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.adapter.bms.order.dto.request.BmsOrderReqDTO;
import cathayfuture.opm.adapter.bms.order.dto.response.BmsOrderRespDTO;
import cathayfuture.opm.adapter.bms.order.exporter.IOrderExporterHandler;
import cathayfuture.opm.adapter.bms.order.exporter.IOrderTemplateExporterHandler;
import cathayfuture.opm.adapter.bms.order.exporter.OrderExporterHandlerFactory;
import cathayfuture.opm.adapter.bms.order.exporter.OrderTemplateExporterHandlerFactory;
import cathayfuture.opm.client.AppException;
import cathayfuture.opm.client.order.api.OrderAppService;
import cathayfuture.opm.client.order.api.query.OrderQueryAppService;
import cathayfuture.opm.client.order.dto.request.OrderReqDTO;
import cathayfuture.opm.client.order.dto.response.OrderRespDTO;
import cathayfuture.opm.client.order.dto.response.PageRespDTO;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import com.google.gson.GsonBuilder;
import com.taslyware.framework.exceltools.excel.writer.ExcelWriter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 描述:
 *
 * <AUTHOR>
 * @since 8/25/22
 */
@Slf4j
@RestController(value = "BmsOrderController")
@Tag(name = "OrderController", description = "BMS订单相关接口")
@RequestMapping("/bms/orders")
public class OrderController {

    private final OrderQueryAppService orderQueryAppService;

    private final OrderAppService orderAppService;

    private final OrderExporterHandlerFactory exporterHandlerFactory;

    private final OrderTemplateExporterHandlerFactory templateExporterHandlerFactory;


    public OrderController(OrderQueryAppService orderQueryAppService,
                           OrderAppService orderAppService, OrderExporterHandlerFactory exporterHandlerFactory, OrderTemplateExporterHandlerFactory templateExporterHandlerFactory) {
        this.orderQueryAppService = orderQueryAppService;
        this.orderAppService = orderAppService;
        this.exporterHandlerFactory = exporterHandlerFactory;
        this.templateExporterHandlerFactory = templateExporterHandlerFactory;
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询", description = "分页查询",
            parameters = {@Parameter(name = "filter", description = "过滤条件(Str)", content = @Content(schema = @Schema(anyOf = {OrderReqDTO.class}))),
                    @Parameter(name = "pageNum", description = "页码"), @Parameter(name = "pageSize", description = "一页大小")},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {BmsOrderRespDTO.class})))})
    CommonResponseDTO<PageRespDTO<BmsOrderRespDTO>> pageOrder(@RequestParam("filter") String filter,
                                                              @RequestParam(defaultValue = "1") Integer pageNum,
                                                              @RequestParam(defaultValue = "10") Integer pageSize) {

        BmsOrderReqDTO bmsOrderReqDTO = new GsonBuilder().create().fromJson(filter, BmsOrderReqDTO.class);
        PageRespDTO<OrderRespDTO> orderPageResult = orderQueryAppService.pageOrder(bmsOrderReqDTO, pageNum, pageSize);
        List<BmsOrderRespDTO> records = orderPageResult
                .getRecords()
                .stream()
                .map(BmsOrderRespDTO::convertFrom)
                .collect(Collectors.toList());
        PageRespDTO<BmsOrderRespDTO> result = new PageRespDTO<>();
        IntStream.range(0, records.size()).forEachOrdered(i -> records.get(i).setOrderNumber(i + 1));
        result.setPageNum(orderPageResult.getPageNum())
                .setPageSize(orderPageResult.getPageSize())
                .setTotal(orderPageResult.getTotal()).setRecords(records);
        return CommonResponseDTO.success(result);
    }


    @GetMapping("/export/{type}")
    @Operation(summary = "订单导出", description = "订单导出",
            parameters = {@Parameter(name = "filter", description = "过滤条件(Str)", content = @Content(schema = @Schema(anyOf = {OrderReqDTO.class}))),},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {BmsOrderRespDTO.class})))})
    void exportOrder(@PathVariable String type, @RequestParam("filter") String filter, HttpServletResponse response) {
        setDownloadFormat(response);
        IOrderExporterHandler iOrderExporterHandler = exporterHandlerFactory.getOrderExporterHandlersList().get(OrderTypeEnum.getEnumKey(type.toUpperCase()));
        BmsOrderReqDTO bmsOrderReqDTO = new GsonBuilder().create().fromJson(filter, BmsOrderReqDTO.class);
        bmsOrderReqDTO.setType(OrderTypeEnum.getEnumKey(type.toUpperCase()));
        List<OrderRespDTO> orderRespDTOList = orderQueryAppService.listOrder(bmsOrderReqDTO);
        List<BmsOrderRespDTO> records = orderRespDTOList
                .stream()
                .map(BmsOrderRespDTO::convertFrom)
                .collect(Collectors.toList());
        IntStream.range(0, records.size()).forEachOrdered(i -> records.get(i).setOrderNumber(i + 1));
        try {
            ExcelWriter excel = ExcelWriter.createExcel("导出");
            iOrderExporterHandler.handleExport(records, null, excel);
            excel.writeResponse(response);
        } catch (Exception e) {
            log.info("订单数据导出失败, 失败原因是:[{}]", e.getMessage(), e);
            throw new AppException("订单数据导出失败");
        }
    }

    private void setDownloadFormat(HttpServletResponse response) {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''exportFile.xlsx");
    }

    @GetMapping("/export/template/{type}")
    @Operation(summary = "订单导出", description = "订单导出",
            parameters = {@Parameter(name = "filter", description = "过滤条件(Str)", content = @Content(schema = @Schema(anyOf = {OrderReqDTO.class}))),},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {BmsOrderRespDTO.class})))})
    public void exportOrderTemplate(@PathVariable String type, @RequestParam("filter") String filter, HttpServletResponse response) {
        IOrderTemplateExporterHandler orderTemplateExporterHandler = templateExporterHandlerFactory.getOrderTemplateExporterHandler().stream().filter(handler -> Objects.equals(handler.getOrderType().getCode(), type.toUpperCase())).findFirst().orElseThrow(NoSuchElementException::new);
        orderTemplateExporterHandler.handleTemplate(response);
    }

    @GetMapping("/{orderId}")
    @Operation(summary = "查询订单详情", description = "查询订单详情",
            parameters = {@Parameter(name = "orderId", description = "orderId")},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {BmsOrderRespDTO.class})))})
    public CommonResponseDTO<BmsOrderRespDTO> queryOrder(@PathVariable("orderId") Integer orderId) {
        return CommonResponseDTO.success(BmsOrderRespDTO.convertFrom(orderQueryAppService.queryOrder(orderId)));
    }

    @PutMapping("/deleteOrder")
    @Operation(summary = "作废订单", description = "作废订单",
            parameters = {@Parameter(name = "orderReqDTO", description = "orderReqDTO", content = @Content(schema = @Schema(anyOf = {BmsOrderReqDTO.class})))},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {Boolean.class})))})
    public CommonResponseDTO<String> deleteOrder(@RequestBody BmsOrderReqDTO bmsOrderReqDTO) {
        orderAppService.deleteOrder(bmsOrderReqDTO.getId(), bmsOrderReqDTO.getDeleteReason());
        return CommonResponseDTO.success("作废订单成功!");
    }

    @PutMapping("payOrderOffline")
    @Operation(summary = "订单线下支付", description = "订单线下支付",
            parameters = {@Parameter(name = "orderReqDTO", description = "支付请求体", content = @Content(schema = @Schema(anyOf = {BmsOrderReqDTO.class})))},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {Boolean.class})))})
    public CommonResponseDTO<String> payOrderOffline(@RequestBody BmsOrderReqDTO orderReqDTO) {
        orderAppService.payOffline(orderReqDTO);
        return CommonResponseDTO.success("线下支付成功!");

    }

    @PutMapping("/refund")
    @Operation(summary = "订单退款", description = "订单退款",
            parameters = {@Parameter(name = "orderReqDTO", description = "orderReqDTO", content = @Content(schema = @Schema(anyOf = {BmsOrderReqDTO.class})))},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {Boolean.class})))})
    public CommonResponseDTO<String> refundOrder(@RequestBody BmsOrderReqDTO bmsOrderReqDTO) {
        Boolean result = orderAppService.refund(bmsOrderReqDTO.getId(), bmsOrderReqDTO.getRefundTime(), bmsOrderReqDTO.getRefundReason());
        if (result) {
            return CommonResponseDTO.success("订单退款成功!");
        } else {
            return CommonResponseDTO.bussinessError("订单退款失败，请检查订单支付状态");
        }
    }
}
