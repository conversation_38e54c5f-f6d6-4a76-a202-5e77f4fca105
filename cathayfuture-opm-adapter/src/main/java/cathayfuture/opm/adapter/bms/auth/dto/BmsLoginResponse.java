package cathayfuture.opm.adapter.bms.auth.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class BmsLoginResponse {
    private String token;
    private String refreshToken;
    private String username;
    private List<String> authorities;
    private String error;
    
    public static BmsLoginResponse error(String error) {
        return BmsLoginResponse.builder().error(error).build();
    }
}