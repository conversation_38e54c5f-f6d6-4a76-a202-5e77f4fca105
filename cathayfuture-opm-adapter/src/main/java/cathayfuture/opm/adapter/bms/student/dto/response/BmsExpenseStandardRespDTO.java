package cathayfuture.opm.adapter.bms.student.dto.response;

import cathayfuture.opm.client.student.dto.response.ExpenseStandardRespDTO;
import cathayfuture.opm.domain.order.enums.BusinessUnitEnum;
import cathayfuture.opm.domain.student.enums.GenderEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 20230331
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(name = "BmsExpenseStandardRespDTO", description = "费用标准信息返回体")
public class BmsExpenseStandardRespDTO extends ExpenseStandardRespDTO {

//    @Schema(description = "序号")
//    private Integer orderNumber;
    @Schema(description = "入园时间字符串")
    private String admissionDateStr;
    @Schema(description = "业态描述")
    private String businessUnitStr;
    @Schema(description = "性别描述")
    private String genderStr;

    public String getBusinessUnitStr() {
        return Optional.ofNullable(this.getBusinessUnit()).map(BusinessUnitEnum::getEnumDescription).orElse("");
    }

    public static BmsExpenseStandardRespDTO convertFrom(ExpenseStandardRespDTO expenseStandardRespDTO) {
        BmsExpenseStandardRespDTO bmsExpenseStandardRespDTO = new BmsExpenseStandardRespDTO();
        BeanUtils.copyProperties(expenseStandardRespDTO, bmsExpenseStandardRespDTO);
        bmsExpenseStandardRespDTO.setAdmissionDateStr(Optional.ofNullable(expenseStandardRespDTO.getAdmissionDate()).map(d -> d.format(DateTimeFormatter.ISO_LOCAL_DATE)).orElse(null));
        return bmsExpenseStandardRespDTO;
    }
//
    public String getGenderStr() {
        return Optional.ofNullable(this.getGender()).map(GenderEnum::getEnumDescription).orElse("未知");
    }
    @Override
    public String getExpenseStandardValue() {
        return Optional.ofNullable(super.getExpenseStandardValue()).orElse("-");
    }


}
