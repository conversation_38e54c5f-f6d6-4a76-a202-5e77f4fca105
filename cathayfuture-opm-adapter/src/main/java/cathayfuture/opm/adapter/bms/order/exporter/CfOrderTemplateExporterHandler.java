package cathayfuture.opm.adapter.bms.order.exporter;

import cathayfuture.opm.client.AppException;
import cathayfuture.opm.client.order.api.query.OrderQueryAppService;
import cathayfuture.opm.client.order.dto.response.OrderRespDTO;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import com.taslyware.framework.exceltools.excel.enums.ExcelColumn;
import com.taslyware.framework.exceltools.excel.utils.ExcelClassPathUtils;
import com.taslyware.framework.exceltools.excel.utils.lambda.LambdaArray;
import com.taslyware.framework.exceltools.excel.writer.ExcelWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 9/5/22
 */
@Slf4j
@Component
public class CfOrderTemplateExporterHandler implements IOrderTemplateExporterHandler{

    private OrderQueryAppService orderQueryAppService;

    public CfOrderTemplateExporterHandler(OrderQueryAppService orderQueryAppService) {
        this.orderQueryAppService = orderQueryAppService;
    }

    @Override
    public OrderTypeEnum getOrderType() {
        return OrderTypeEnum.DINNER;
    }

    @Override
    public void handleTemplate(HttpServletResponse response) {
        List<OrderRespDTO> orderRespDTOList = orderQueryAppService.queryOrderTemplate(getOrderType().getKey());
        String dataStr = LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        try {
            ExcelWriter.createExcel(ExcelClassPathUtils.getClassPathInputStream("template/excel/cf_order_import_template.xlsx"), "育儿订单导入模板" + dataStr)
                    .addList(
                            ExcelColumn.A,
                            3,
                            null,
                            LambdaArray.as(
                                    OrderRespDTO::getStudentName,
                                    OrderRespDTO::getStudentCode,
                                    OrderRespDTO::getGrade,
                                    OrderRespDTO::getContactInfo,
                                    OrderRespDTO::getChargingStandardValue,
                                    OrderRespDTO::getChargeItem,
                                    OrderRespDTO::getPreviousRemainingSumValue,
                                    OrderRespDTO::getPreviousAttendanceDaysValue,
                                    OrderRespDTO::getPreviousPayableChargeValue,
                                    OrderRespDTO::getNextAttendanceDaysValue,
                                    OrderRespDTO::getNextPayableChargeValue,
                                    OrderRespDTO::getChargeAmount,
                                    OrderRespDTO::getRemark
                            ).getNamesDot(),
                            orderRespDTOList
                    ).write(response.getOutputStream());
        } catch (Exception e) {
            log.info("餐费订单导入模板下载失败, 失败原因是:[{}]", e.getMessage(), e);
            throw new AppException("餐费订单导入模板下载失败");
        }
    }
}
