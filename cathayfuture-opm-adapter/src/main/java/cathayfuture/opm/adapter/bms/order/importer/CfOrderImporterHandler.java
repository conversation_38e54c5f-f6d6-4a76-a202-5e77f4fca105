package cathayfuture.opm.adapter.bms.order.importer;

import cathayfuture.opm.adapter.bms.order.dto.request.BmsOrderReqDTO;
import cathayfuture.opm.adapter.bms.order.dto.response.OrderImportedRespDTO;
import cathayfuture.opm.adapter.utils.StrUtil;
import cathayfuture.opm.client.order.api.OrderAppService;
import cathayfuture.opm.client.order.dto.request.ExcelUploadReqDTO;
import cathayfuture.opm.client.order.dto.request.OrderReqDTO;
import cathayfuture.opm.client.order.exception.ExcelUploadException;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.NumberUtil;
import com.google.gson.GsonBuilder;
import com.taslyware.framework.exceltools.excel.enums.ExcelColumn;
import com.taslyware.framework.exceltools.excel.reader.ExcelReader;
import com.taslyware.framework.exceltools.importer.AbstractExcelImporter;
import com.taslyware.framework.exceltools.importer.annotation.ExcelImporter;
import com.taslyware.framework.exceltools.importer.annotation.ImporterAutowired;
import com.taslyware.framework.exceltools.importer.trans.FeignQueryManger;
import com.taslyware.framework.exceltools.importer.trans.FeignQueryRef;
import com.taslyware.framework.exceltools.importer.trans.Transer;
import com.taslyware.framework.exceltools.importer.validate.Validator;
import com.taslyware.framework.exceltools.importer.vo.ExcelData;
import com.taslyware.framework.exceltools.importer.vo.ExcelIniter;
import com.taslyware.framework.exceltools.importer.vo.ImportResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 9/5/22
 */
@Slf4j
@ExcelImporter("CF_ORDER")
public class CfOrderImporterHandler extends AbstractExcelImporter<BmsOrderReqDTO> {

    public static final int MAX_LENGTH = 24;
    public static final int INT_SIZE = 21;
    public static final int DECIMAL_SCALE = 2;
    @ImporterAutowired
    StudentQueryAppService studentQueryAppService;
    @ImporterAutowired
    OrderAppService orderAppService;

    private static final FeignQueryRef<StudentRespDTO> QUERYER_QUERY_STUDENT_BY_NAME = FeignQueryRef.newRef("queryStudentByName", StudentRespDTO.class);
    private static final FeignQueryRef<StudentRespDTO> QUERYER_QUERY_STUDENT_BY_CODE = FeignQueryRef.newRef("queryStudentByCode", StudentRespDTO.class);
    private static final FeignQueryRef<StudentRespDTO> QUERYER_QUERY_STUDENT_BY_CONTACT = FeignQueryRef.newRef("queryStudentByContact", StudentRespDTO.class);
    private static final FeignQueryRef<StudentRespDTO> QUERYER_QUERY_STUDENT_BY_GRADE = FeignQueryRef.newRef("queryStudentByGrade", StudentRespDTO.class);

    private Integer importFlag = 0;

    @Override
    public ExcelIniter init(String requestParams) {
        ExcelUploadReqDTO excelUploadReqDTO = new GsonBuilder().create().fromJson(requestParams, ExcelUploadReqDTO.class);
        importFlag = Optional.ofNullable(excelUploadReqDTO.getImportFlag()).orElse(0);
        return ExcelIniter.newIniter(
                ExcelColumn.A,
                3,
                BmsOrderReqDTO::getStudentName,
                BmsOrderReqDTO::getStudentCode,
                BmsOrderReqDTO::getGrade,
                BmsOrderReqDTO::getContactInfo,
                BmsOrderReqDTO::getChargingStandard,
                BmsOrderReqDTO::getChargeItem,
                BmsOrderReqDTO::getPreviousRemainingSum,
                BmsOrderReqDTO::getPreviousAttendanceDays,
                BmsOrderReqDTO::getPreviousPayableCharge,
                BmsOrderReqDTO::getNextAttendanceDays,
                BmsOrderReqDTO::getNextPayableCharge,
                BmsOrderReqDTO::getChargeAmount,
                BmsOrderReqDTO::getRemark);
    }

    @Override
    public boolean checkTemplate(ExcelReader reader) {
        String firstLineHeader = reader.getRowData(1).stream().map(StrUtil::trimPlus).findFirst().get();
        String secondLineHeaders = reader.getRowData(2).stream().map(StrUtil::trimPlus).collect(Collectors.joining(",")).trim();
        String templateHeaders = "学员姓名,学员学号,班级,学员联系方式,每日餐费标准,收费项目,上期预收餐费余额,上期出勤天数,上期应收餐费,下期应出勤天数,下期应预收餐费,预收餐费金额,备注";
        if (Objects.equals(firstLineHeader, "餐费订单导入模板") && Objects.equals(secondLineHeaders, templateHeaders)) {
            return true;
        }
        return false;
    }

    @Override
    public void feignQueryer(FeignQueryManger feignQueryManger) {
        feignQueryManger.addQueryer(QUERYER_QUERY_STUDENT_BY_NAME, list -> studentQueryAppService.list(new StudentReqDTO()), StudentRespDTO::getStudentName);
        feignQueryManger.addQueryer(QUERYER_QUERY_STUDENT_BY_CODE, list -> studentQueryAppService.list(new StudentReqDTO()), StudentRespDTO::getStudentNo);
        feignQueryManger.addQueryer(QUERYER_QUERY_STUDENT_BY_CONTACT, list -> studentQueryAppService.list(new StudentReqDTO()), StudentRespDTO::getContactPhoneNumber);
        feignQueryManger.addQueryer(QUERYER_QUERY_STUDENT_BY_GRADE, list -> studentQueryAppService.list(new StudentReqDTO()), StudentRespDTO::getStudentClass);
    }

    @Override
    public void validate(Validator<BmsOrderReqDTO> validator) {
        validator.valid(BmsOrderReqDTO::getStudentName)
                .notNull()
                .maxlength(100)
                .feignExist(QUERYER_QUERY_STUDENT_BY_NAME)
                .custom(params -> {
                    String cellValue = StrUtil.trimPlus(params.getCellValue());
                    if (StringUtils.isBlank(cellValue)) {
                        return "学员姓名不能为空";
                    }
                    return null;
                });

        validator.valid(BmsOrderReqDTO::getStudentCode)
                .notNull()
                .maxlength(100)
                .feignExist(QUERYER_QUERY_STUDENT_BY_CODE)
                .custom(params -> {
                    String cellValue = StrUtil.trimPlus(params.getCellValue());
                    if (StringUtils.isBlank(cellValue)) {
                        return "学员学号不能为空";
                    }
                    return null;
                });

        validator.valid(BmsOrderReqDTO::getContactInfo)
                .notNull()
                .maxlength(100)
                .feignExist(QUERYER_QUERY_STUDENT_BY_CONTACT)
                .custom(params -> {
                    String cellValue = StrUtil.trimPlus(params.getCellValue());
                    if (StringUtils.isBlank(cellValue)) {
                        return "学员联系方式不能为空";
                    }
                    return null;
                });

        validator.valid(BmsOrderReqDTO::getGrade)
                .notNull()
                .maxlength(100)
                .feignExist(QUERYER_QUERY_STUDENT_BY_GRADE)
                .custom(params -> {
                    String cellValue = StrUtil.trimPlus(params.getCellValue());
                    if (StringUtils.isBlank(cellValue)) {
                        return "班级不能为空";
                    }
                    return null;
                });

        validator.valid(BmsOrderReqDTO::getStudentCode)
                .feignCustom(QUERYER_QUERY_STUDENT_BY_CODE, validFeignParams -> {
                    StudentRespDTO feignResult = (StudentRespDTO) validFeignParams.getFeignResult();
                    BmsOrderReqDTO excelData = (BmsOrderReqDTO) validFeignParams.getData();

                    if (StringUtils.isNotBlank(excelData.getStudentCode())
                            && StringUtils.isNotBlank(excelData.getStudentName())
                            && Objects.nonNull(feignResult)
                            && Objects.equals(feignResult.getStudentNo().trim(), StrUtil.trimPlus(excelData.getStudentCode().trim()))
                            && !Objects.equals(feignResult.getStudentName().trim(), StrUtil.trimPlus(excelData.getStudentName().trim()))) {
                        log.info("学生学号和学生名称错误，excel文件中学生学号和学生名称不对应");
                        return "学生学号和学生名称错误，excel文件中学生学号和学生名称不对应";
                    }
                    return null;
                }).errorMsgProperty(BmsOrderReqDTO::getStudentName);

        validator.valid(BmsOrderReqDTO::getStudentCode)
                .feignCustom(QUERYER_QUERY_STUDENT_BY_CODE, validFeignParams -> {
                    StudentRespDTO feignResult = (StudentRespDTO) validFeignParams.getFeignResult();
                    BmsOrderReqDTO excelData = (BmsOrderReqDTO) validFeignParams.getData();

                    if (StringUtils.isNotBlank(excelData.getStudentCode())
                            && StringUtils.isNotBlank(excelData.getContactInfo())
                            && Objects.nonNull(feignResult)
                            && Objects.equals(feignResult.getStudentNo().trim(), StrUtil.trimPlus(excelData.getStudentCode().trim()))
                            && !Objects.equals(feignResult.getContactPhoneNumber().trim(), StrUtil.trimPlus(excelData.getContactInfo().trim()))) {
                        log.info("学生学号和联系方式错误，excel文件中学生学号和联系方式不对应");
                        return "学生学号和联系方式错误，excel文件中学生学号和联系方式不对应";
                    }
                    return null;
                }).errorMsgProperty(BmsOrderReqDTO::getContactInfo);

        validator.valid(BmsOrderReqDTO::getStudentCode)
                .feignCustom(QUERYER_QUERY_STUDENT_BY_CODE, validFeignParams -> {
                    StudentRespDTO feignResult = (StudentRespDTO) validFeignParams.getFeignResult();
                    BmsOrderReqDTO excelData = (BmsOrderReqDTO) validFeignParams.getData();

                    if (StringUtils.isNotBlank(excelData.getStudentCode())
                            && StringUtils.isNotBlank(excelData.getGrade())
                            && Objects.nonNull(feignResult)
                            && Objects.equals(feignResult.getStudentNo().trim(), StrUtil.trimPlus(excelData.getStudentCode().trim()))
                            && !Objects.equals(feignResult.getStudentClass().trim(), StrUtil.trimPlus(excelData.getGrade().trim()))) {
                        log.info("学生学号和班级错误，excel文件中学生学号和班级不对应");
                        return "学生学号和班级错误，excel文件中学生学号和班级不对应";
                    }
                    return null;
                }).errorMsgProperty(BmsOrderReqDTO::getGrade);

        validator.valid(BmsOrderReqDTO::getStudentCode)
                .unique(validateParams -> String.join("_", validateParams.getCellValue(),
                                validateParams.getOtherCellValue(BmsOrderReqDTO::getStudentName),
                                validateParams.getOtherCellValue(BmsOrderReqDTO::getContactInfo),
                                validateParams.getOtherCellValue(BmsOrderReqDTO::getGrade),
                                validateParams.getOtherCellValue(BmsOrderReqDTO::getChargeItem)),
                        "excel中存在多个相同的学生信息和收费项目的数据");

        validator.valid(BmsOrderReqDTO::getChargingStandard)
                .notNull()
                .maxlength(MAX_LENGTH)
                .isFloating()
                .bigdecimalSize(INT_SIZE, DECIMAL_SCALE);
        validator.valid(BmsOrderReqDTO::getChargeItem)
                .notNull()
                .maxlength(50)
                .custom(params -> {
                    String cellValue = StrUtil.trimPlus(params.getCellValue());
                    if (StringUtils.isBlank(cellValue)) {
                        return "餐费收费项目不能为空";
                    }
                    return null;
                });
        validator.valid(BmsOrderReqDTO::getPreviousRemainingSum)
                .notNull()
                .maxlength(MAX_LENGTH)
                .isNumber()
                .bigdecimalSize(INT_SIZE, DECIMAL_SCALE);
        validator.valid(BmsOrderReqDTO::getPreviousAttendanceDays)
                .notNull()
                .isNumber()
                .maxlength(100)
                .custom(param -> {
                    if (StringUtils.isNotBlank(param.getCellValue()) && NumberUtil.isInteger(param.getCellValue())) {
                        int cellValue = Integer.parseInt(param.getCellValue());
                        if (cellValue < 0) {
                            return "上期出勤天数是大于等于0的整数";
                        } else {
                            return null;
                        }
                    } else {
                        return "上期出勤天数是大于等于0的整数";
                    }
                });
        validator.valid(BmsOrderReqDTO::getPreviousPayableCharge)
                .notNull()
                .maxlength(MAX_LENGTH)
                .isFloating()
                .bigdecimalSize(INT_SIZE, DECIMAL_SCALE);
        validator.valid(BmsOrderReqDTO::getNextAttendanceDays)
                .notNull()
                .isNumber()
                .maxlength(100)
                .custom(param -> {
                    if (StringUtils.isNotBlank(param.getCellValue()) && NumberUtil.isInteger(param.getCellValue())) {
                        int cellValue = Integer.parseInt(param.getCellValue());
                        if (cellValue < 0) {
                            return "下期出勤天数是大于等于0的整数";
                        } else {
                            return null;
                        }
                    } else {
                        return "下期出勤天数是大于等于0的整数";
                    }
                });
        validator.valid(BmsOrderReqDTO::getNextPayableCharge)
                .notNull()
                .maxlength(MAX_LENGTH)
                .isFloating()
                .bigdecimalSize(INT_SIZE, DECIMAL_SCALE);
        validator.valid(BmsOrderReqDTO::getChargeAmount)
                .notNull()
                .maxlength(MAX_LENGTH)
                .isFloating()
                .bigdecimalSize(INT_SIZE, DECIMAL_SCALE)
                .custom(param -> {
                    if (StringUtils.isNotBlank(param.getCellValue()) && NumberUtil.isNumber(param.getCellValue())) {
                        BigDecimal cellValue = new BigDecimal(param.getCellValue());
                        if (cellValue.compareTo(BigDecimal.ZERO) <= 0) {
                            return "预收餐费金额不能小于等于0";
                        } else {
                            return null;
                        }
                    } else {
                        return "预收餐费金额是大于0的数字";
                    }
                });
        validator.valid(BmsOrderReqDTO::getRemark)
                .maxlength(100);
    }

    @Override
    public void transform(Transer<BmsOrderReqDTO> transer) {

    }

    @Override
    public ImportResponse onSuccess(List<BmsOrderReqDTO> data) {
        int importedCount = data.size();
        BigDecimal chargeAmountSum = data.stream().map(BmsOrderReqDTO::getChargeAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        OrderImportedRespDTO orderImportedRespDTO = new OrderImportedRespDTO();
        orderImportedRespDTO.setImportedCount(importedCount).setChargedAmountSum(chargeAmountSum);
        List<StudentRespDTO> allStudents = studentQueryAppService.list(new StudentReqDTO());
        String batchId = UUID.fastUUID().toString(true);
        List<OrderReqDTO> reqDTOList = data.stream().map(item -> {
            OrderReqDTO reqDTO = new OrderReqDTO();
            BeanUtils.copyProperties(item, reqDTO);
            reqDTO.setType(OrderTypeEnum.DINNER.getKey());
            allStudents
                    .stream()
                    .filter(student -> sameStudentFilter(item, student))
                    .findFirst()
                    .ifPresent(student -> reqDTO.setStudentId(student.getId()));
            reqDTO.setBatchCode(batchId);
            return reqDTO;
        }).collect(Collectors.toList());

        if (Objects.equals(importFlag, 1)) {
            Boolean result = orderAppService.importOrders(reqDTOList);
            if (Boolean.TRUE.equals(result)) {
                return ImportResponse.responseBody(orderImportedRespDTO, "导入成功");
            } else {
                return ImportResponse.error("导入失败");
            }
        }
        return ImportResponse.responseBody(orderImportedRespDTO, "导入成功");
    }

    @Override
    public ImportResponse onFail(HttpServletResponse response, ExcelData data) {
        try {
            defaultDownloadError(data).writeResponse(response);
            return ImportResponse.download();
        } catch (IOException e) {
            throw new ExcelUploadException(e.getMessage());
        }
    }

    private static boolean sameStudentFilter(BmsOrderReqDTO item, StudentRespDTO student) {
        return Objects.equals(student.getStudentNo(), item.getStudentCode()) && Objects.equals(student.getContactPhoneNumber(), item.getContactInfo()) && Objects.equals(student.getStudentClass(), item.getGrade()) && Objects.equals(student.getStudentName(), item.getStudentName());
    }
}
