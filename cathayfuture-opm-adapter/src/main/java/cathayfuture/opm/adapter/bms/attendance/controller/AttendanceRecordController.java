package cathayfuture.opm.adapter.bms.attendance.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.adapter.utils.PageUtil;
import cathayfuture.opm.client.attendance.api.AttendanceRecordAppService;
import cathayfuture.opm.client.attendance.api.query.AttendanceRecordQueryAppService;
import cathayfuture.opm.client.attendance.dto.request.AttendanceRecordPageReqDto;
import cathayfuture.opm.client.attendance.dto.request.AttendanceRecordSaveReqDto;
import cathayfuture.opm.client.attendance.dto.response.AttendanceRecordDataIntegrityByClassRespDto;
import cathayfuture.opm.client.attendance.dto.response.AttendanceRecordPageRespDTO;
import cathayfuture.opm.client.attendance.dto.response.StudentInfoAndLongTermRespDTO;
import cathayfuture.opm.infra.common.utils.ValidatorUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.GsonBuilder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 考勤记录
 * @date 2023/3/31 17:21
 */

@Slf4j
@RestController(value = "AttendanceRecordController")
@Tag(name = "AttendanceRecordController", description = "BMS考勤相关接口")
@RequestMapping("/bms/attendanceRecord")
public class AttendanceRecordController {

    private AttendanceRecordQueryAppService attendanceRecordQueryAppService;

    private AttendanceRecordAppService attendanceRecordAppService;

    public AttendanceRecordController(AttendanceRecordQueryAppService attendanceRecordQueryAppService, AttendanceRecordAppService attendanceRecordAppService) {
        this.attendanceRecordQueryAppService = attendanceRecordQueryAppService;
        this.attendanceRecordAppService = attendanceRecordAppService;
    }


    @GetMapping("/page")
    @Operation(summary = "分页查询请假信息", description = "分页查询请假信息",
            parameters = {@Parameter(name = "filter", description = "过滤条件Json字符串", content = @Content(schema = @Schema(anyOf = {AttendanceRecordPageReqDto.class}))),
                    @Parameter(name = "pageNum", description = "页码(默认1)"),
                    @Parameter(name = "pageSize", description = "页数(默认10)")},
            responses = {@ApiResponse(description = "分页查询结果", content = @Content(schema = @Schema(anyOf = AttendanceRecordPageRespDTO.class)))})
    public CommonResponseDTO<IPage<AttendanceRecordPageRespDTO>> pageOrder(@RequestParam("filter") String filter,
                                                                           @RequestParam(defaultValue = "1") Integer pageNum,
                                                                           @RequestParam(defaultValue = "10") Integer pageSize) {
        AttendanceRecordPageReqDto reqDto = new GsonBuilder().create().fromJson(filter, AttendanceRecordPageReqDto.class);
        IPage<AttendanceRecordPageRespDTO> result = attendanceRecordQueryAppService.page(reqDto, PageUtil.makePageInfo(pageNum,pageSize));
        return CommonResponseDTO.success(result);
    }


    @PostMapping("/save")
    @Operation(summary = "新增考勤记录操作", description = "新增考勤记录操作",
            parameters = {@Parameter(name = "AttendanceRecordSaveReqDto", description = "AttendanceRecordSaveReqDto")},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {String.class})))})
    public CommonResponseDTO save(@RequestBody @Validated List<AttendanceRecordSaveReqDto> reqDtos){
        attendanceRecordAppService.save(reqDtos);
        return CommonResponseDTO.success();
    }


    @GetMapping("/queryStudentInfoList")
    @Operation(summary = "根据班级和入园时间查询学生信息", description = "根据班级和入园时间查询学生信息",
            parameters = {@Parameter(name = "studentClass", description = "班级")},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {StudentInfoAndLongTermRespDTO.class})))})
    public CommonResponseDTO<List<StudentInfoAndLongTermRespDTO>> queryStudentInfoList(@RequestParam(value ="studentClass",required=true) String studentClass,
                                                                                       @RequestParam(value = "currentDate",required=false) String currentDate){
        return CommonResponseDTO.success(attendanceRecordQueryAppService.queryStudentInfoList(studentClass,currentDate));
    }


    @GetMapping("/queryDataIntegrityByClass")
    @Operation(summary = "班级考勤是否完整", description = "班级考勤是否完整",
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {AttendanceRecordDataIntegrityByClassRespDto.class})))})
    public CommonResponseDTO<List<AttendanceRecordDataIntegrityByClassRespDto>> queryStudentInfoList(){
        return CommonResponseDTO.success(attendanceRecordQueryAppService.queryAttendanceRecordDataIntegrityByClassRespDto());
    }


    @GetMapping("/initData")
    @Operation(summary = "initData", description = "initData",
            parameters = {@Parameter(name = "studentIdsStr", description = "学生id字符串")},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {Integer.class})))})
    public CommonResponseDTO<Integer> initData(@RequestParam(value ="studentIdsStr",required=false) String studentIdsStr,
                                                                                       @RequestParam(value = "dateListStr",required=false) String dateListStr){
        return CommonResponseDTO.success(attendanceRecordAppService.initData(studentIdsStr,dateListStr));
    }




}
