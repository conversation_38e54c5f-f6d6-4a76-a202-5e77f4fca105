package cathayfuture.opm.adapter.bms.order.dto.response;

import cathayfuture.opm.adapter.utils.DateUtil;
import cathayfuture.opm.client.order.dto.response.OrderRespDTO;
import cathayfuture.opm.client.order.dto.response.PaymentDetailRespDTO;
import cathayfuture.opm.domain.order.enums.OrderStatusEnum;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import cathayfuture.opm.domain.order.enums.PaymentModeEnum;
import cathayfuture.opm.domain.order.enums.PaymentStatusEnum;
import cn.hutool.core.bean.BeanUtil;
import com.google.common.base.Joiner;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/26/22
 */
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
public class BmsOrderRespDTO extends OrderRespDTO {

    private int orderNumber;

    @Override
    public String getStatusName() {
        return Optional.ofNullable(getStatus()).map(OrderStatusEnum::getEnumDescription).orElse("");
    }

    @Override
    public String getTypeName() {
        return Optional.ofNullable(getType()).map(OrderTypeEnum::getEnumDescription).orElse("");
    }

    @Override
    public String getPaymentModeName() {
        if (CollectionUtils.isEmpty(getPaymentDetails())) {
            return "";
        }
        return Joiner.on(",").join(getPaymentDetails().stream().map(PaymentDetailRespDTO::getPaymentMode).map(PaymentModeEnum::getEnumDescription).collect(Collectors.toList()));
    }

    @Override
    public String getPaymentStatusName() {
        return Optional.ofNullable(getPaymentStatus()).map(PaymentStatusEnum::getEnumDescription).orElse("");
    }

    public static BmsOrderRespDTO convertFrom(OrderRespDTO orderRespDTO) {
        BmsOrderRespDTO bmsOrderRespDTO = new BmsOrderRespDTO();
        BeanUtil.copyProperties(orderRespDTO, bmsOrderRespDTO);
        if (!CollectionUtils.isEmpty(bmsOrderRespDTO.getPaymentDetails())) {
            bmsOrderRespDTO.getPaymentDetails().forEach(paymentDetail -> {
                paymentDetail.setPaymentModeName(PaymentModeEnum.getEnumDescription(paymentDetail.getPaymentMode()));
                paymentDetail.setPaymentStatusName(PaymentStatusEnum.getEnumDescription(paymentDetail.getPaymentStatus()));
            });
        }
        return bmsOrderRespDTO;
    }

    @Override
    public String getCreateTimeStr() {
        return Optional.ofNullable(getCreateTime()).map(DateUtil::normalFormat).orElse("");
    }

    @Override
    public String getUpdateTimeStr() {
        return Optional.ofNullable(getUpdateTime()).map(DateUtil::normalFormat).orElse("");
    }

    @Override
    public String getPaymentTimeStr() {
        return Optional.ofNullable(getPaymentTime()).map(DateUtil::normalFormat).orElse("");
    }

    @Override
    public String getPreviousActualSurplus() {
        return Optional.ofNullable(super.getPreviousActualSurplus()).orElse("-");
    }

    @Override
    public String getChargingStandard() {
        return Optional.ofNullable(super.getChargingStandard()).orElse("-");
    }

    @Override
    public String getPreviousRemainingSum() {
        return Optional.ofNullable(super.getPreviousRemainingSum()).orElse("-");
    }

    @Override
    public String getPreviousPayableCharge() {
        return Optional.ofNullable(super.getPreviousPayableCharge()).orElse("-");
    }

    @Override
    public String getNextPayableCharge() {
        return Optional.ofNullable(super.getNextPayableCharge()).orElse("-");
    }

    @Override
    public String getPreviousAttendanceDays() {
        return Optional.ofNullable(super.getPreviousAttendanceDays()).map(Object::toString).orElse("-");
    }

    @Override
    public String getPreviousChargePercentage() {
        return Optional.ofNullable(super.getPreviousChargePercentage()).orElse("-");
    }

    @Override
    public String getDeleteTimeStr() {
        return Optional.ofNullable(getDeleteTime()).map(DateUtil::normalFormat).orElse("");
    }

    @Override
    public String getRefundTimeStr() {
        return Optional.ofNullable(getRefundTime()).map(d -> d.format(DateTimeFormatter.ISO_LOCAL_DATE)).orElse(null);
    }
}
