package cathayfuture.opm.adapter.bms.test.controller;

import cathayfuture.opm.adapter.security.LoginRateLimiter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 速率限制测试控制器
 * 仅在开发环境和测试环境启用
 * 
 * <AUTHOR> Code
 * @since 2025-08-13
 */
@RestController
@RequestMapping("/bms/test/rate-limit")
@ConditionalOnProperty(name = "spring.profiles.active", havingValue = "dev", matchIfMissing = false)
public class RateLimitTestController {

    @Resource
    private LoginRateLimiter rateLimiter;

    /**
     * 测试IP速率限制
     */
    @PostMapping("/test-ip-limit")
    public Map<String, Object> testIpLimit(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        String testUser = "test-user";
        boolean allowed = rateLimiter.allowLoginAttempt(request, testUser);
        
        if (!allowed) {
            long remainingTime = rateLimiter.getRemainingLockTime(request, testUser);
            result.put("status", "BLOCKED");
            result.put("message", "IP被锁定");
            result.put("remainingTime", remainingTime);
        } else {
            // 模拟登录失败
            rateLimiter.recordLoginFailure(request, testUser);
            result.put("status", "FAILED");
            result.put("message", "登录失败次数已记录");
        }
        
        return result;
    }

    /**
     * 测试用户名速率限制
     */
    @PostMapping("/test-username-limit")
    public Map<String, Object> testUsernameLimit(HttpServletRequest request, 
                                                 @RequestParam String username) {
        Map<String, Object> result = new HashMap<>();
        
        boolean allowed = rateLimiter.allowLoginAttempt(request, username);
        
        if (!allowed) {
            long remainingTime = rateLimiter.getRemainingLockTime(request, username);
            result.put("status", "BLOCKED");
            result.put("message", "用户名被锁定");
            result.put("remainingTime", remainingTime);
        } else {
            // 模拟登录失败
            rateLimiter.recordLoginFailure(request, username);
            result.put("status", "FAILED");
            result.put("message", "登录失败次数已记录");
        }
        
        return result;
    }

    /**
     * 重置速率限制（用于测试）
     */
    @PostMapping("/reset")
    public Map<String, Object> resetRateLimit(HttpServletRequest request, 
                                              @RequestParam String username) {
        Map<String, Object> result = new HashMap<>();
        
        // 模拟登录成功来清除限制
        rateLimiter.recordLoginSuccess(request, username);
        
        result.put("status", "SUCCESS");
        result.put("message", "速率限制已重置");
        
        return result;
    }

    /**
     * 获取当前速率限制状态
     */
    @GetMapping("/status")
    public Map<String, Object> getRateLimitStatus(HttpServletRequest request,
                                                  @RequestParam String username) {
        Map<String, Object> result = new HashMap<>();
        
        boolean allowed = rateLimiter.allowLoginAttempt(request, username);
        long remainingTime = rateLimiter.getRemainingLockTime(request, username);
        
        result.put("allowed", allowed);
        result.put("remainingTime", remainingTime);
        result.put("clientIp", getClientIp(request));
        result.put("username", username);
        
        return result;
    }

    /**
     * 获取客户端IP（与LoginRateLimiter中的逻辑保持一致）
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个代理的情况，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }
}