package cathayfuture.opm.adapter.bms.student.exporter;

import cathayfuture.opm.adapter.bms.student.dto.response.BmsStudentRespDTO;
import com.taslyware.framework.exceltools.excel.enums.ExcelColumn;
import com.taslyware.framework.exceltools.excel.utils.ListUtils;
import com.taslyware.framework.exceltools.excel.utils.lambda.LambdaUtils;
import com.taslyware.framework.exceltools.excel.writer.ExcelWriter;
import com.taslyware.framework.exceltools.exporter.handler.IExporterHandler;

import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 9/2/22
 */
public class StudentExporterHandler implements IExporterHandler {

    @Override
    public <T> void handleExport(List<T> list, Object data, ExcelWriter writer) {
        writer.setTitle("学生信息导出文件");
        writer.addList(ExcelColumn.A,
                1,
                "序号,学生姓名,学号,班级,民族,身份证号,性别,生日,国籍,家庭住址,联系人姓名,联系电话,关系,父亲姓名,父亲电话,母亲姓名,母亲电话,入园时间,业态,入园状态",
                ListUtils.list2Dot(
                        ListUtils.arrayToList(
                                LambdaUtils.property(BmsStudentRespDTO::getOrderNumber),
                                LambdaUtils.property(BmsStudentRespDTO::getStudentName),
                                LambdaUtils.property(BmsStudentRespDTO::getStudentNo),
                                LambdaUtils.property(BmsStudentRespDTO::getStudentClass),
                                LambdaUtils.property(BmsStudentRespDTO::getEthnicGroup),
                                LambdaUtils.property(BmsStudentRespDTO::getIdNumber),
                                LambdaUtils.property(BmsStudentRespDTO::getGenderStr),
                                LambdaUtils.property(BmsStudentRespDTO::getBirthday),
                                LambdaUtils.property(BmsStudentRespDTO::getNationality),
                                LambdaUtils.property(BmsStudentRespDTO::getWholeHomeAddress),
                                LambdaUtils.property(BmsStudentRespDTO::getContactName),
                                LambdaUtils.property(BmsStudentRespDTO::getContactPhoneNumber),
                                LambdaUtils.property(BmsStudentRespDTO::getRelation),
                                LambdaUtils.property(BmsStudentRespDTO::getFatherName),
                                LambdaUtils.property(BmsStudentRespDTO::getFatherPhoneNumber),
                                LambdaUtils.property(BmsStudentRespDTO::getMotherName),
                                LambdaUtils.property(BmsStudentRespDTO::getMotherPhoneNumber),
                                LambdaUtils.property(BmsStudentRespDTO::getAdmissionDateStr),
                                LambdaUtils.property(BmsStudentRespDTO::getBusinessUnitStr),
                                LambdaUtils.property(BmsStudentRespDTO::getRegisterStatusStr)
                        )
                ),
                list
        );
    }
}
