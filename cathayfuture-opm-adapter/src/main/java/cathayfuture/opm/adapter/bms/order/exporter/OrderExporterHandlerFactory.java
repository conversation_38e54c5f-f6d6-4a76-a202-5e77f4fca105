package cathayfuture.opm.adapter.bms.order.exporter;

import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 9/5/22
 */
@Component
public class OrderExporterHandlerFactory {

    private final List<IOrderExporterHandler> orderExporterHandlersList;


    public OrderExporterHandlerFactory(List<IOrderExporterHandler> orderExporterHandlersList) {
        this.orderExporterHandlersList = orderExporterHandlersList;
    }

    public List<IOrderExporterHandler> getOrderExporterHandlersList() {
        return this.orderExporterHandlersList;
    }
}
