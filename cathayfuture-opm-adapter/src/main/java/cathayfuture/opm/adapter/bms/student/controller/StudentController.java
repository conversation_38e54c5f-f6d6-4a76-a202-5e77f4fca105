package cathayfuture.opm.adapter.bms.student.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.adapter.bms.student.dto.request.BmsStudentReqDTO;
import cathayfuture.opm.adapter.bms.student.dto.response.BmsStudentRespDTO;
import cathayfuture.opm.adapter.bms.student.exporter.StudentExporterHandler;
import cathayfuture.opm.client.order.dto.response.PageRespDTO;
import cathayfuture.opm.client.student.api.StudentAppService;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import com.google.gson.GsonBuilder;
import com.taslyware.framework.exceltools.exporter.annotation.AutoExcelExporter;
import com.taslyware.framework.exceltools.pojo.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 20220902
 */
@Slf4j
@RestController(value = "BmsStudentController")
@Tag(name = "StudentController", description = "BMS学生相关接口")
@RequestMapping("/bms/students")
public class StudentController {

    private StudentQueryAppService studentQueryAppService;

    private StudentAppService studentAppService;

    public StudentController(StudentQueryAppService studentQueryAppService,
                             StudentAppService studentAppService) {
        this.studentQueryAppService = studentQueryAppService;
        this.studentAppService = studentAppService;
    }

    @GetMapping("/countByStudentNo")
    @Operation(summary = "查询学生号数量", description = "查询学生号数量",
            parameters = {@Parameter(name = "studentNo", description = "studentNo")},
            responses = {@ApiResponse(description = "", content = @Content(schema = @Schema(anyOf = {Integer.class})))})
    public CommonResponseDTO<Integer> queryOrder(@RequestParam("studentNo") String studentNo) {
        return CommonResponseDTO.success(studentQueryAppService.countByStudentNo(studentNo));
    }

    @GetMapping("/idCardResolver")
    @Operation(summary = "查询学生号数量", description = "查询学生号数量",
            parameters = {@Parameter(name = "idNumber", description = "idNumber")},
            responses = {@ApiResponse(description = "", content = @Content(schema = @Schema(anyOf = {StudentRespDTO.class})))})
    public CommonResponseDTO<StudentRespDTO> idCardResolver(@RequestParam("studentNo") String studentNo) {
        return CommonResponseDTO.success(studentQueryAppService.idCardResolver(studentNo));
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询学生信息", description = "分页查询学生信息",
            parameters = {@Parameter(name = "filter", description = "过滤条件Json字符串", content = @Content(schema = @Schema(anyOf = {BmsStudentReqDTO.class}))),
                    @Parameter(name = "pageNum", description = "页码(默认1)"),
                    @Parameter(name = "pageSize", description = "页数(默认10)")},
            responses = {@ApiResponse(description = "分页查询结果", content = @Content(schema = @Schema(anyOf = BmsStudentRespDTO.class)))})
    public CommonResponseDTO<PageRespDTO<BmsStudentRespDTO>> pageOrder(@RequestParam("filter") String filter,
                                                                       @RequestParam(defaultValue = "1") Integer pageNum,
                                                                       @RequestParam(defaultValue = "10") Integer pageSize) {
        BmsStudentReqDTO bmsStudentReqDTO = new GsonBuilder().create().fromJson(filter, BmsStudentReqDTO.class);
        PageRespDTO<StudentRespDTO> studentPageResult = studentQueryAppService.page(bmsStudentReqDTO, pageNum, pageSize);
        List<BmsStudentRespDTO> records = studentPageResult.getRecords().stream().map(BmsStudentRespDTO::convertFrom).collect(Collectors.toList());
        IntStream.range(0, records.size()).forEachOrdered(i -> records.get(i).setOrderNumber(i + 1));
        PageRespDTO<BmsStudentRespDTO> result = new PageRespDTO<BmsStudentRespDTO>()
                .setPageNum(studentPageResult.getPageNum())
                .setPageSize(studentPageResult.getPageSize())
                .setTotal(studentPageResult.getTotal())
                .setRecords(records);
        return CommonResponseDTO.success(result);
    }

    @GetMapping("/export")
    @AutoExcelExporter(handler = StudentExporterHandler.class)
    @Operation(summary = "学生信息导出", description = "学生信息导出",
            parameters = {@Parameter(name = "filter", description = "过滤条件(Str)", content = @Content(schema = @Schema(anyOf = {BmsStudentReqDTO.class}))),},
            responses = {@ApiResponse(description = "", content = @Content(schema = @Schema(anyOf = {BmsStudentRespDTO.class})))})
    RestResponse<List<BmsStudentRespDTO>> exportStudent(@RequestParam("filter") String filter) {
        BmsStudentReqDTO bmsStudentReqDTO = new GsonBuilder().create().fromJson(filter, BmsStudentReqDTO.class);
        List<StudentRespDTO> studentRespDTOList = studentQueryAppService.list(bmsStudentReqDTO);
        List<BmsStudentRespDTO> records = studentRespDTOList
                .stream()
                .map(BmsStudentRespDTO::convertFrom)
                .collect(Collectors.toList());
        IntStream.range(0, records.size()).forEachOrdered(i -> records.get(i).setOrderNumber(i + 1));
        return RestResponse.success().data(records);
    }

    @GetMapping("/{studentId}")
    @Operation(summary = "查询学生详情", description = "查询学生详情",
            parameters = {@Parameter(name = "studentId", description = "studentId")},
            responses = {@ApiResponse(description = "学生详情", content = @Content(schema = @Schema(anyOf = {BmsStudentRespDTO.class})))})
    public CommonResponseDTO<BmsStudentRespDTO> queryStudent(@PathVariable("studentId") Integer studentId) {
        return CommonResponseDTO.success(BmsStudentRespDTO.convertFrom(studentQueryAppService.getById(studentId)));
    }

    @PostMapping("/save")
    @Operation(summary = "保存学生", description = "保存学生",
            parameters = {@Parameter(name = "studentReqDTO", description = "studentReqDTO", content = @Content(schema = @Schema(anyOf = {BmsStudentReqDTO.class})))},
            responses = {@ApiResponse(description = "保存后的学生信息", content = @Content(schema = @Schema(anyOf = BmsStudentRespDTO.class)))})
    public CommonResponseDTO save(@RequestBody BmsStudentReqDTO studentReqDTO) {
        return CommonResponseDTO.success(studentAppService.save(studentReqDTO));
    }

    @PostMapping("/register")
    @Operation(summary = "入园", description = "入园",
            parameters = {@Parameter(name = "studentReqDTO", description = "studentReqDTO", content = @Content(schema = @Schema(anyOf = {BmsStudentReqDTO.class})))},
            responses = {@ApiResponse(description = "修改后的学生信息", content = @Content(schema = @Schema(anyOf = BmsStudentRespDTO.class)))})
    public CommonResponseDTO register(@RequestBody BmsStudentReqDTO studentReqDTO) {
        return CommonResponseDTO.success(studentAppService.register(studentReqDTO));
    }

    @GetMapping("queryListByStudentName")
    @Operation(summary = "根据学生姓名查询学生详情", description = "根据学生姓名查询学生详情",
            parameters = {@Parameter(name = "studentName", description = "studentName")},
            responses = {@ApiResponse(description = "根据学生姓名查询学生详情", content = @Content(schema = @Schema(anyOf = {BmsStudentRespDTO.class})))})
    public CommonResponseDTO<List<StudentRespDTO>> queryListByStudentName(@RequestParam("studentName") String studentName){
        return CommonResponseDTO.success(studentQueryAppService.queryListByStudentName(studentName));
    }
    @PostMapping("/quit/{id}")
    @Operation(summary = "退园", description = "退园",
            parameters = {@Parameter(name = "studentReqDTO", description = "studentReqDTO", content = @Content(schema = @Schema(anyOf = {BmsStudentReqDTO.class})))},
            responses = {@ApiResponse(description = "修改后的学生信息", content = @Content(schema = @Schema(anyOf = BmsStudentRespDTO.class)))})
    public CommonResponseDTO quit(@PathVariable("id") Integer id) {
        studentAppService.quit(id);
        return CommonResponseDTO.success();
    }

}
