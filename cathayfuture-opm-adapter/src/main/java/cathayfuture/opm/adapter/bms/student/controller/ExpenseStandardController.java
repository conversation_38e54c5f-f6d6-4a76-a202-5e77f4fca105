package cathayfuture.opm.adapter.bms.student.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.adapter.bms.student.dto.request.BmsExpenseStandardReqDTO;
import cathayfuture.opm.adapter.bms.student.dto.request.BmsStudentReqDTO;
import cathayfuture.opm.adapter.bms.student.dto.response.BmsExpenseStandardRespDTO;
import cathayfuture.opm.adapter.bms.student.dto.response.BmsStudentRespDTO;
import cathayfuture.opm.client.order.dto.response.PageRespDTO;
import cathayfuture.opm.client.student.api.query.ExpenseStandardQueryAppService;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.response.ExpenseStandardRespDTO;
import com.google.gson.GsonBuilder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 20230331
 */
@Slf4j
@RestController(value = "BmsExpenseStandardController")
@Tag(name = "ExpenseStandardController", description = "BMS学生费用标准相关接口")
@RequestMapping("/bms/expenseStandard")
public class ExpenseStandardController {

    private StudentQueryAppService studentQueryAppService;
    private ExpenseStandardQueryAppService expenseStandardQueryAppService;


    public ExpenseStandardController(StudentQueryAppService studentQueryAppService,
                                     ExpenseStandardQueryAppService expenseStandardQueryAppService) {
        this.studentQueryAppService = studentQueryAppService;
        this.expenseStandardQueryAppService = expenseStandardQueryAppService;
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询费用标准信息", description = "分页查询费用标准信息",
            parameters = {@Parameter(name = "filter", description = "过滤条件Json字符串", content = @Content(schema = @Schema(anyOf = {BmsExpenseStandardReqDTO.class}))),
                    @Parameter(name = "pageNum", description = "页码(默认1)"),
                    @Parameter(name = "pageSize", description = "页数(默认10)")},
            responses = {@ApiResponse(description = "分页查询结果", content = @Content(schema = @Schema(anyOf = BmsExpenseStandardReqDTO.class)))})
    public CommonResponseDTO<PageRespDTO<BmsExpenseStandardRespDTO>> pageOrder(@RequestParam("filter") String filter,
                                                                               @RequestParam(defaultValue = "1") Integer pageNum,
                                                                               @RequestParam(defaultValue = "10") Integer pageSize) {
        BmsExpenseStandardReqDTO bmsExpenseStandardReqDTO = new GsonBuilder().create().fromJson(filter, BmsExpenseStandardReqDTO.class);
        PageRespDTO<ExpenseStandardRespDTO> expenseStandardPageResult = expenseStandardQueryAppService.page(bmsExpenseStandardReqDTO, pageNum, pageSize);
        List<BmsExpenseStandardRespDTO> records = expenseStandardPageResult.getRecords().stream().map(BmsExpenseStandardRespDTO::convertFrom).collect(Collectors.toList());
        PageRespDTO<BmsExpenseStandardRespDTO> result = new PageRespDTO<BmsExpenseStandardRespDTO>()
                .setPageNum(expenseStandardPageResult.getPageNum())
                .setPageSize(expenseStandardPageResult.getPageSize())
                .setTotal(expenseStandardPageResult.getTotal())
                .setRecords(records);
        return CommonResponseDTO.success(result);
    }

    @GetMapping("/{expenseStandardId}")
    @Operation(summary = "查询学生费用标准详情", description = "查询学生费用标准详情",
            parameters = {@Parameter(name = "expenseStandardId", description = "expenseStandardId")},
            responses = {@ApiResponse(description = "学生费用标准详情", content = @Content(schema = @Schema(anyOf = {BmsStudentRespDTO.class})))})
    public CommonResponseDTO<BmsExpenseStandardRespDTO> queryStudent(@PathVariable("expenseStandardId") Integer expenseStandardId) {
        return CommonResponseDTO.success(BmsExpenseStandardRespDTO.convertFrom(expenseStandardQueryAppService.getById(expenseStandardId)));
    }

    @PostMapping("/edit")
    @Operation(summary = "编辑学生费用标准", description = "编辑学生费用标准",
            parameters = {@Parameter(name = "expenseStandardReqDTO", description = "expenseStandardReqDTO", content = @Content(schema = @Schema(anyOf = {BmsExpenseStandardReqDTO.class})))},
            responses = {@ApiResponse(description = "编辑后的学生费用标准信息", content = @Content(schema = @Schema(anyOf = BmsExpenseStandardRespDTO.class)))})
    public CommonResponseDTO edit(@RequestBody BmsExpenseStandardReqDTO expenseStandardReqDTO) {
        return CommonResponseDTO.success(expenseStandardQueryAppService.edit(expenseStandardReqDTO));
    }

}
