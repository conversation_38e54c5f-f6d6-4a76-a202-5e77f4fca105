package cathayfuture.opm.adapter.bms.student.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.client.student.api.ExpenseDefaultAppService;
import cathayfuture.opm.client.student.api.query.ExpenseDefaultQueryAppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 收费标准默认值
 * @date 2023/4/11 14:55
 */

@Slf4j
@RestController(value = "ExpenseDefaultController")
@Tag(name = "ExpenseDefaultController", description = "收费标准默认值")
@RequestMapping("/bms/expenseDefault")
public class ExpenseDefaultController {

    private ExpenseDefaultQueryAppService queryAppService;

    private ExpenseDefaultAppService appService;

    public ExpenseDefaultController(ExpenseDefaultQueryAppService queryAppService, ExpenseDefaultAppService appService) {
        this.queryAppService = queryAppService;
        this.appService = appService;
    }

    @GetMapping("/queryDefaultValue")
    @Operation(summary = "查询收费标准默认值", description = "查询收费标准默认值")
    public CommonResponseDTO<String> query(@RequestParam("type") Integer type){
        BigDecimal byType = queryAppService.getByType(type);
        return CommonResponseDTO.success(byType.toPlainString());
    }
}
