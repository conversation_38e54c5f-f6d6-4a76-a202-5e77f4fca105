package cathayfuture.opm.adapter.bms.attendance.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.adapter.utils.PageUtil;
import cathayfuture.opm.client.attendance.api.AskedForLeaveAppService;
import cathayfuture.opm.client.attendance.api.query.AskedForLeaveQueryAppService;
import cathayfuture.opm.client.attendance.dto.request.AskedForLeaveReqDto;
import cathayfuture.opm.client.attendance.dto.request.AskedForLeaveSaveReqDto;
import cathayfuture.opm.client.attendance.dto.request.ReturnOperateReqDto;
import cathayfuture.opm.client.attendance.dto.response.AskedForLeaveRespDto;
import cathayfuture.opm.client.attendance.dto.response.StudentInfoAndLongTermRespDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.GsonBuilder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 请假Controller
 * @date 2023/3/27 15:57
 */

@Slf4j
@RestController(value = "BmsAskedForLeaveController")
@Tag(name = "AskedForLeaveController", description = "BMS请假相关接口")
@RequestMapping("/bms/askedForLeave")
public class AskedForLeaveController {

    private AskedForLeaveAppService askedForLeaveAppService;

    private AskedForLeaveQueryAppService askedForLeaveQueryAppService;

    public AskedForLeaveController(AskedForLeaveAppService askedForLeaveAppService, AskedForLeaveQueryAppService askedForLeaveQueryAppService) {
        this.askedForLeaveAppService = askedForLeaveAppService;
        this.askedForLeaveQueryAppService = askedForLeaveQueryAppService;
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询请假信息", description = "分页查询请假信息",
            parameters = {@Parameter(name = "filter", description = "过滤条件Json字符串", content = @Content(schema = @Schema(anyOf = {AskedForLeaveReqDto.class}))),
                    @Parameter(name = "pageNum", description = "页码(默认1)"),
                    @Parameter(name = "pageSize", description = "页数(默认10)")},
            responses = {@ApiResponse(description = "分页查询结果", content = @Content(schema = @Schema(anyOf = AskedForLeaveRespDto.class)))})
    public CommonResponseDTO<IPage<AskedForLeaveRespDto>> pageOrder(@RequestParam("filter") String filter,
                                                                 @RequestParam(defaultValue = "1") Integer pageNum,
                                                                 @RequestParam(defaultValue = "10") Integer pageSize) {
        AskedForLeaveReqDto reqDto = new GsonBuilder().create().fromJson(filter, AskedForLeaveReqDto.class);
        IPage<AskedForLeaveRespDto> result = askedForLeaveQueryAppService.page(reqDto, PageUtil.makePageInfo(pageNum,pageSize));
        return CommonResponseDTO.success(result);
    }

    @PostMapping("/returnOperate")
    @Operation(summary = "销假操作", description = "销假操作"
            /*parameters = {@Parameter(name = "ReturnOperateReqDto", description = "ReturnOperateReqDto", content = @Content(schema = @Schema(anyOf = {ReturnOperateReqDto.class})))},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {Boolean.class})))}*/
            )
    public CommonResponseDTO<String> returnOperate(@RequestBody @Valid ReturnOperateReqDto reqDto) {
        askedForLeaveAppService.returnOperate(reqDto);
        return CommonResponseDTO.success("销假操作成功");
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除操作", description = "删除操作",
            parameters = {@Parameter(name = "id", description = "id")},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {Boolean.class})))})
    public CommonResponseDTO<String> delete(@PathVariable("id") Integer id) {
        askedForLeaveAppService.delete(id);
        return CommonResponseDTO.success("删除操作成功");
    }

    @PostMapping("/save")
    @Operation(summary = "新增请假操作", description = "新增请假操作",
            parameters = {@Parameter(name = "AskedForLeaveSaveReqDto", description = "AskedForLeaveSaveReqDto")},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {String.class})))})
    public CommonResponseDTO save(@RequestBody @Valid AskedForLeaveSaveReqDto reqDto){
        askedForLeaveAppService.save(reqDto);
        return CommonResponseDTO.success();
    }

    @GetMapping("/judgementLongTerm")
    @Operation(summary = "根据学生id判断长期病假", description = "根据学生id判断长期病假",
            parameters = {@Parameter(name = "stuId", description = "stuId")},
            responses = {@ApiResponse(description = "结果", content = @Content(schema = @Schema(anyOf = {Boolean.class})))})
    public CommonResponseDTO<Boolean> judgementLongTerm(@RequestParam("stuId") Integer stuId){
        return CommonResponseDTO.success(askedForLeaveQueryAppService.judgementLongTerm(stuId,null));
    }

}
