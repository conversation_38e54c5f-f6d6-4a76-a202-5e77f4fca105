package cathayfuture.opm.adapter.bms.action.dto.response;

import cathayfuture.opm.adapter.utils.DateUtil;
import cathayfuture.opm.client.action.dto.response.ActionRespDTO;
import cathayfuture.opm.domain.action.enums.FieldEnum;
import cathayfuture.opm.domain.student.enums.GenderEnum;
import cn.hutool.core.bean.BeanUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.core.convert.converter.Converter;

import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
public class BmsActionRespDTO extends ActionRespDTO {

    @Override
    public String getUpdateTimeStr() {
        return Optional.ofNullable(getUpdateTime()).map(DateUtil::normalFormat).orElse("");
    }

    @Override
    public String getPropertyName() {
        return Optional.ofNullable(this.getProperty()).map(FieldEnum::getEnumDescription).orElse("未知");
    }

    public static BmsActionRespDTO convertFrom(ActionRespDTO actionRespDTO) {
        BmsActionRespDTO bmsActionRespDTO = new BmsActionRespDTO();
        BeanUtil.copyProperties(actionRespDTO, bmsActionRespDTO);
        return bmsActionRespDTO;
    }
}
