package cathayfuture.opm.adapter.bms.order.dto.response;

import cathayfuture.opm.client.order.dto.response.PaymentDetailRespDTO;
import cathayfuture.opm.domain.order.enums.PaymentModeEnum;
import cathayfuture.opm.domain.order.enums.PaymentStatusEnum;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/26/22
 */
public class BmsPaymentDetailRespDTO extends PaymentDetailRespDTO {

   @Override
    public String getPaymentModeName() {
       return PaymentModeEnum.getEnumDescription(getPaymentMode());
   }

    @Override
    public String getPaymentStatusName() {
        return PaymentStatusEnum.getEnumDescription(getPaymentMode());
    }

}
