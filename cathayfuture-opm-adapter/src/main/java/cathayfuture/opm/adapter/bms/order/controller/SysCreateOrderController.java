package cathayfuture.opm.adapter.bms.order.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.client.order.api.ComputationalCostsAppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 系统创建订单Controller
 * @date 2023/6/9 16:01
 */


@Slf4j
@RestController(value = "BmsSysCreateOrderController")
@Tag(name = "SysCreateOrderController", description = "BMS系统创建订单相关接口")
@RequestMapping("/bms/sysCreateOrder")
public class SysCreateOrderController {

    private ComputationalCostsAppService computationalCostsAppService;

    public SysCreateOrderController(ComputationalCostsAppService computationalCostsAppService) {
        this.computationalCostsAppService = computationalCostsAppService;
    }


    @GetMapping("/childCareOperate")
    @Operation(summary = "生成保育费", description = "生成保育费")
    public CommonResponseDTO testChildCareOperate(@RequestParam(value = "studentIds",required = false) String studentIds,
                                                  @RequestParam(value = "dateStr",required = false) String dateStr){
        List<Integer> list = new ArrayList<>();
        if(StringUtils.isNotBlank(studentIds)){
            list = Arrays.stream(studentIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        computationalCostsAppService.childCareOperate(list,dateStr);
        return CommonResponseDTO.success();
    }

    @GetMapping("/dinnerOperate")
    @Operation(summary = "生成餐费", description = "生成餐费")
    public CommonResponseDTO testDinnerOperate(@RequestParam(value = "studentIds",required = false) String studentIds,
                                               @RequestParam(value = "dateStr",required = false) String dateStr){
        List<Integer> list = new ArrayList<>();
        if(StringUtils.isNotBlank(studentIds)){
            list = Arrays.stream(studentIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        computationalCostsAppService.dinnerOperate(list,dateStr);
        return CommonResponseDTO.success();
    }

    @GetMapping("/monthlySettlementOperate")
    @Operation(summary = "保育费月结", description = "保育费月结")
    public CommonResponseDTO monthlySettlementOperate(@RequestParam(value = "studentIds",required = false) String studentIds,
                                                      @RequestParam(value = "dateStr",required = false) String dateStr){
        List<Integer> list = new ArrayList<>();
        if(StringUtils.isNotBlank(studentIds)){
            list = Arrays.stream(studentIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        computationalCostsAppService.monthlySettlementOperate(list,dateStr);
        return CommonResponseDTO.success();
    }

    @GetMapping("/monthlySettlementForDinnerOperate")
    @Operation(summary = "餐费月结", description = "餐费月结")
    public CommonResponseDTO monthlySettlementForDinnerOperate(@RequestParam(value = "studentIds",required = false) String studentIds,
                                                               @RequestParam(value = "dateStr",required = false) String dateStr){
        List<Integer> list = new ArrayList<>();
        if(StringUtils.isNotBlank(studentIds)){
            list = Arrays.stream(studentIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        computationalCostsAppService.monthlySettlementForDinnerOperate(list,dateStr);
        return CommonResponseDTO.success();
    }
}
