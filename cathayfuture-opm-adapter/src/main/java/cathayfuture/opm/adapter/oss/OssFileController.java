package cathayfuture.opm.adapter.oss;

import com.dtyunxi.huieryun.oss.rest.OssPolicyController;
import com.dtyunxi.rest.RestResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2022/9/20
 */
@RestController
@RequestMapping("/v1/cathayfuture/oss")
public class OssFileController {

    @Resource
    private OssPolicyController ossPolicyController;

    @GetMapping("/{appId}/getpolicy")
    public RestResponse<?> applyPolicy(String appId, String bucketKey) {
        return ossPolicyController.applyPolicy(appId, bucketKey, null);
    }

    @GetMapping("geturl/{appId}/get-authorized-file-url")
    public RestResponse<String> getAuthorizedFileUrl(String appId, String fileURI) {
        return ossPolicyController.getAuthorizedFileUrl(appId, fileURI);
    }
}
