package cathayfuture.opm.adapter.mobile.order.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.adapter.mobile.account.dto.request.PayNotifyDTO;
import cathayfuture.opm.client.order.api.OrderAppService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 订单回调信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mobile/callbacks")
@Slf4j
public class PayCallBackController {

    @Resource
    private OrderAppService orderAppService;

    @PostMapping(value = "/pay/notify")
    public CommonResponseDTO payNotify(@RequestBody PayNotifyDTO payNotifyDTO) {
        log.info("支付回调开始，request[{}]", JSONObject.toJSONString(payNotifyDTO));
        orderAppService.payComplete(payNotifyDTO.getTenantOrderNo(), payNotifyDTO.getOrderStatus(), payNotifyDTO.getPayAmount(), payNotifyDTO.getTripartiteOrderNo());
        CommonResponseDTO<JSONObject> result = CommonResponseDTO.success(new JSONObject().fluentPut("receiptCode", HttpStatus.OK.value()).fluentPut("receiptMessage", HttpStatus.OK.name()));
        log.info("支付回调结束，response[{}]", JSONObject.toJSONString(result));
        return result;
    }

}
