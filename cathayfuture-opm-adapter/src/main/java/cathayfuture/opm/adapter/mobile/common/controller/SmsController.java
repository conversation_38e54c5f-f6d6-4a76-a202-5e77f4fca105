package cathayfuture.opm.adapter.mobile.common.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.client.common.SmsService;
import cathayfuture.opm.client.common.dto.request.CheckSmsVerificationCodeReqDTO;
import cathayfuture.opm.client.common.dto.request.SendSmsReqDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * <AUTHOR>
 * @date 20220829
 */
@RestController
@RequestMapping("/mobile/sms")
public class SmsController {

    @Resource
    private SmsService smsService;

    @PostMapping("/sendSms")
    public CommonResponseDTO sendSms(@Valid @RequestBody SendSmsReqDTO sendSmsReqDTO) {
        return CommonResponseDTO.success(smsService.sendSms(sendSmsReqDTO));
    }

    @PostMapping("/checkSmsVerificationCode")
    public CommonResponseDTO checkSmsVerificationCode(@Valid @RequestBody CheckSmsVerificationCodeReqDTO checkSmsVerificationCodeReqDTO) {
        return CommonResponseDTO.success(smsService.checkSmsVerificationCode(checkSmsVerificationCodeReqDTO));
    }
}
