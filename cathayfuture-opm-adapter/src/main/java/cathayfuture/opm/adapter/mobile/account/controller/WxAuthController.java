package cathayfuture.opm.adapter.mobile.account.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.adapter.mobile.account.dto.request.WxBindPhoneNumberReqDTO;
import cathayfuture.opm.adapter.mobile.utils.JwtUtils;
import cathayfuture.opm.client.account.api.AccountAppService;
import cathayfuture.opm.client.account.dto.request.AccountReqDTO;
import cathayfuture.opm.adapter.mobile.account.dto.request.WxLoginReqDTO;
import cathayfuture.opm.client.account.dto.response.AccountRespDTO;
import cathayfuture.opm.adapter.mobile.account.dto.response.WxLoginRespDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date ********
 */
@RestController
@RequestMapping("/mobile/wx")
public class WxAuthController {

    @Resource
    private AccountAppService accountAppService;

    @PostMapping("/login")
    public CommonResponseDTO login(@Valid @RequestBody WxLoginReqDTO wxLoginReqDTO) {
        String code = wxLoginReqDTO.getCode();
        String phoneCode = wxLoginReqDTO.getPhoneCode();
        AccountReqDTO userInfo = wxLoginReqDTO.getUserInfo();
        if (StringUtils.isBlank(code)) {
            return CommonResponseDTO.unauthorized();
        }
        AccountRespDTO accountRespDTO = accountAppService.wxLogin(code, phoneCode, userInfo);
        WxLoginRespDTO respDTO = new WxLoginRespDTO();
        String token = JwtUtils.createToken(accountRespDTO);
        if (StringUtils.isBlank(token)) {
            return CommonResponseDTO.unauthorized();
        }
        respDTO.setToken(JwtUtils.createToken(accountRespDTO));
        respDTO.setUserInfo(accountRespDTO.clearSessionKey());
        return CommonResponseDTO.success(respDTO);
    }

    @PostMapping("/bindPhoneNumber")
    public CommonResponseDTO bindPhoneNumber(@Valid @RequestBody WxBindPhoneNumberReqDTO wxBindPhoneNumberReqDTO) {
        String code = wxBindPhoneNumberReqDTO.getCode();
        String phoneNumber = accountAppService.bindPhoneNumber(code);
        return CommonResponseDTO.success(phoneNumber);
    }

}
