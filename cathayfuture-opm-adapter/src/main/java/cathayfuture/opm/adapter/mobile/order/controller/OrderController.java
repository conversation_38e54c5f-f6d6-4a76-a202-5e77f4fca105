package cathayfuture.opm.adapter.mobile.order.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.adapter.mobile.order.dto.request.WxPrePayReqDTO;
import cathayfuture.opm.client.order.api.OrderAppService;
import cathayfuture.opm.client.order.api.query.OrderQueryAppService;
import cathayfuture.opm.client.order.dto.request.CreateFrontMoneyOrderReqDTO;
import cathayfuture.opm.client.order.dto.request.EnrollReqDTO;
import cathayfuture.opm.client.order.dto.request.OrderChangePayerReqDTO;
import cathayfuture.opm.client.order.dto.request.PreOrderReqDTO;
import cathayfuture.opm.client.order.dto.response.MobileOrderRespDTO;
import cathayfuture.opm.client.order.dto.response.PageRespDTO;
import cn.hutool.json.JSONUtil;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@RestController
@RequestMapping("/mobile/orders")
public class OrderController {

    private OrderAppService orderAppService;
    private OrderQueryAppService orderQueryAppService;
    public OrderController(OrderAppService orderAppService, OrderQueryAppService orderQueryAppService){
        this.orderAppService = orderAppService ;
        this.orderQueryAppService = orderQueryAppService ;
    }

    @PostMapping("entroll")
    public CommonResponseDTO enroll(@RequestBody EnrollReqDTO enrollReqDTO) {
        return CommonResponseDTO.success(this.orderAppService.enroll(enrollReqDTO));
    }

    @GetMapping("/page/{status}")
    CommonResponseDTO<PageRespDTO<MobileOrderRespDTO>> pageOrder(@PathVariable("status") Integer status,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return CommonResponseDTO.success(orderQueryAppService.pageOrderForMobile(status, pageNum, pageSize));
    }

    @GetMapping("/detail/{orderId}")
    CommonResponseDTO<MobileOrderRespDTO> queryOrderById(@PathVariable("orderId") Integer orderId) {
        return CommonResponseDTO.success(orderQueryAppService.queryOrderById(orderId));
    }

    @GetMapping("/paymentStatus/{orderId}")
    CommonResponseDTO<Integer> queryOrderPaymentStatusById(@PathVariable("orderId") Integer orderId) {
        return CommonResponseDTO.success(orderQueryAppService.queryOrderPaymentStatusById(orderId));
    }

    @PostMapping("/prePay")
    public CommonResponseDTO prePay(@RequestBody WxPrePayReqDTO wxPrePayReqDTO) {
        return CommonResponseDTO.success(JSONUtil.parseObj(this.orderAppService.prePay(wxPrePayReqDTO.getOrderId())));
    }

    @GetMapping("/queryPrePayAndPayStatus/{orderId}")
    CommonResponseDTO<Integer> queryPrePayAndPayStatus(@PathVariable("orderId") Integer orderId) {
        return CommonResponseDTO.success(orderQueryAppService.queryPrePayAndPayStatus(orderId));
    }

    @PutMapping("/changePayer")
    public CommonResponseDTO changePayer(@RequestBody OrderChangePayerReqDTO changePayerReqDTO) {
        return CommonResponseDTO.success(this.orderAppService.changePayer(changePayerReqDTO));
    }

    @GetMapping("/notPayOrderCount")
    public CommonResponseDTO<Integer> notPayOrderCount() {
        return CommonResponseDTO.success(orderQueryAppService.notPayOrderCount());
    }

    @PostMapping("/createFrontMoneyOrder")
    public CommonResponseDTO createFrontMoneyOrder(@Valid @RequestBody CreateFrontMoneyOrderReqDTO createFrontMoneyOrderReqDTO) {
        return CommonResponseDTO.success(this.orderAppService.createFrontMoneyOrder(createFrontMoneyOrderReqDTO));
    }

    @PostMapping("/preOrder")
    public CommonResponseDTO preOrder(@Valid @RequestBody PreOrderReqDTO preOrderReqDTO) {
        return CommonResponseDTO.success(this.orderAppService.preOrder(preOrderReqDTO));
    }
}
