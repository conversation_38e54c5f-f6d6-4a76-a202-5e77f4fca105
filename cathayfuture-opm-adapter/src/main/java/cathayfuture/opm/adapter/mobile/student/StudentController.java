package cathayfuture.opm.adapter.mobile.student;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.client.order.dto.request.CreateFrontMoneyOrderReqDTO;
import cathayfuture.opm.client.student.api.StudentAppService;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.request.QueryByContactPhoneNumberReqDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * <AUTHOR>
 * @date 20220824
 */
@RestController
@RequestMapping("/mobile/student")
public class StudentController {

    @Resource
    private StudentQueryAppService studentQueryAppService;
    @Resource
    private StudentAppService studentAppService;

    @PostMapping("/listByContactPhoneNumber")
    public CommonResponseDTO listByContactPhoneNumber(@Valid @RequestBody QueryByContactPhoneNumberReqDTO queryByContactPhoneNumberReqDTO) {
        return CommonResponseDTO.success(studentQueryAppService.listByContactPhoneNumber(queryByContactPhoneNumberReqDTO));
    }

    @PostMapping("/checkRepeatData")
    public CommonResponseDTO checkRepeatData(@Valid  @RequestBody CreateFrontMoneyOrderReqDTO reqDTO){
        studentAppService.checkRepeatData(reqDTO);
        return CommonResponseDTO.success();
    }

}
