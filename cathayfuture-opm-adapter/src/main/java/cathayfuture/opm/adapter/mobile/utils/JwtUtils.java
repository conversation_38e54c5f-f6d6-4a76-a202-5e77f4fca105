package cathayfuture.opm.adapter.mobile.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 20220824
 */
@Slf4j
public class JwtUtils {

    /**
     * 有效期
     */
    private static final int EFFECTIVE_HOURS = 7 * 24;
    /**
     * 密钥
     */
    private static final String SECRET = "Q0FUSEFZLUZVVFVSRS1NSU5JLVBST0dSQU0=";
    /**
     * 发行人
     */
    private static final String ISSUER = "CATHAY-FUTURE-MINI-PROGRAM";
    /**
     * 主题
     */
    private static final String SUBJECT = "CATHAY-FUTURE-MINI-PROGRAM TOKEN";
    /**
     * 用户
     */
    private static final String AUDIENCE = "CATHAY-FUTURE-MINI-PROGRAM";

    public static String createToken(Object obj) {
        if (obj == null) {
            return null;
        }
        return createToken(new Gson().toJson(obj));
    }

    public static String createToken(String user) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(SECRET);
            Map<String, Object> map = new HashMap<>();
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireDateTime = now.plusHours(EFFECTIVE_HOURS);
            map.put("alg", "HS256");
            map.put("typ", "JWT");
            String token = JWT.create()
                    .withHeader(map)
                    .withClaim("user", user)
                    .withIssuer(ISSUER)
                    .withSubject(SUBJECT)
                    .withAudience(AUDIENCE)
                    .withIssuedAt(now.atZone(ZoneId.systemDefault()).toInstant())
                    .withExpiresAt(expireDateTime.atZone(ZoneId.systemDefault()).toInstant())
                    .sign(algorithm);
            return token;
        } catch (JWTCreationException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static <T> T decodeToken(String token, Class<T> clazz) {
        String userJsonStr = decodeToken(token);
        if (StringUtils.isBlank(userJsonStr)) {
            return null;
        }
        return new Gson().fromJson(userJsonStr, clazz);
    }

    public static String decodeToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(SECRET);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(ISSUER)
                    .build();
            DecodedJWT jwt = verifier.verify(token);
            Map<String, Claim> claims = jwt.getClaims();
            Claim claim = claims.get("user");
            return claim.asString();
        } catch (JWTVerificationException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

}
