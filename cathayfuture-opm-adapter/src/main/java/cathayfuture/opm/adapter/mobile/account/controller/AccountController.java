package cathayfuture.opm.adapter.mobile.account.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.client.account.api.AccountAppService;
import cathayfuture.opm.client.account.api.query.AccountQueryAppService;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;


/**
 * <AUTHOR>
 * @date ********
 */
@RestController
@RequestMapping("/mobile/account")
public class AccountController {

    @Resource
    private AccountQueryAppService accountQueryAppService;
    @Resource
    private AccountAppService accountAppService;

    @GetMapping("/listBoundStudents")
    public CommonResponseDTO listBoundStudents() {
        return CommonResponseDTO.success(accountQueryAppService.listBoundStudents());
    }

    @PostMapping("/bindStudents")
    public CommonResponseDTO bindStudents(@RequestBody List<StudentReqDTO> studentReqDTOList) {
        return CommonResponseDTO.success(accountAppService.bindStudents(studentReqDTOList));
    }

    @GetMapping("/listBoundAccounts")
    public CommonResponseDTO listBoundAccounts(@RequestParam Integer studentId) {
        return CommonResponseDTO.success(accountQueryAppService.listBoundAccounts(studentId, false));
    }
}
