package cathayfuture.opm.adapter;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CommonResponseDTO<T> {

    private String resultCode ;
    private String resultMsg ;
    private T data ;

    public String getResultCode() {
        return resultCode;
    }

    public CommonResponseDTO<T> setResultCode(String resultCode) {
        this.resultCode = resultCode;
        return this;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public CommonResponseDTO<T> setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
        return this;
    }

    public T getData() {
        return data;
    }

    public CommonResponseDTO<T> setData(T data) {
        this.data = data;
        return this;
    }

    public static <T> CommonResponseDTO<T> badRequest(T data) {
        return instance(CommonResponseCode.BAD_REQUEST, data);
    }

    public static CommonResponseDTO serverError(String message) {
        return instance(CommonResponseCode.SERVER_ERROR, null);
    }

    public static <T> CommonResponseDTO<T> bussinessError(T data) {
        return instance(CommonResponseCode.BUSSINESS_ERROR, data);
    }

    public static <T> CommonResponseDTO<T> accessError(T data) {
        return instance(CommonResponseCode.ACCESS_ERROR, data);
    }

    public static CommonResponseDTO unauthorized() {
        return instance(CommonResponseCode.UNAUTHORIZED, null);
    }

    public static <T> CommonResponseDTO<T> success(T data) {
        return instance(CommonResponseCode.SUCCESS, data);
    }

    public static CommonResponseDTO<Void> success() {
        return instance(CommonResponseCode.SUCCESS, null);
    }

    private static <T> CommonResponseDTO<T> instance(CommonResponseCode responseCode, T data) {
        return instance(responseCode.resCode, responseCode.message, data);
    }

    private static <T> CommonResponseDTO<T> instance(String resCode, String message, T data) {
        return new CommonResponseDTO<T>()
                .setResultCode(resCode)
                .setResultMsg(message)
                .setData(data);
    }

}
