package cathayfuture.opm.adapter.security;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

/**
 * BMS系统专用JWT工具类
 * 与小程序JWT系统完全分离，使用不同的密钥、发行人和主题标识
 * 
 * <AUTHOR> Code
 * @date 2025-08-13
 */
@Slf4j
@Component
public class BmsJwtUtils {

    /**
     * BMS专用密钥（从配置文件读取）
     */
    @Value("${cathay.security.jwt.bms.secret:}")
    private String bmsSecret;
    
    /**
     * BMS Access Token有效期（小时）
     */
    @Value("${cathay.security.jwt.bms.access-token-hours:24}")
    private int bmsAccessTokenEffectiveHours;
    
    /**
     * BMS Refresh Token有效期（小时）
     */
    @Value("${cathay.security.jwt.bms.refresh-token-hours:48}")
    private int bmsRefreshTokenEffectiveHours;
    
    /**
     * BMS专用发行人
     */
    @Value("${cathay.security.jwt.bms.issuer:CATHAY-FUTURE-BMS-SYSTEM}")
    private String bmsIssuer;
    
    /**
     * BMS专用受众
     */
    @Value("${cathay.security.jwt.bms.audience:CATHAY-FUTURE-BMS-SYSTEM}")
    private String bmsAudience;
    
    /**
     * BMS Access Token主题
     */
    private static final String BMS_ACCESS_TOKEN_SUBJECT = "CATHAY-FUTURE-BMS-ACCESS-TOKEN";
    
    /**
     * BMS Refresh Token主题
     */
    private static final String BMS_REFRESH_TOKEN_SUBJECT = "CATHAY-FUTURE-BMS-REFRESH-TOKEN";
    
    /**
     * BMS Token类型：Access Token
     */
    public static final String BMS_TOKEN_TYPE_ACCESS = "bms_access_token";
    
    /**
     * BMS Token类型：Refresh Token
     */
    public static final String BMS_TOKEN_TYPE_REFRESH = "bms_refresh_token";
    
    /**
     * 静态实例用于静态方法调用
     */
    private static BmsJwtUtils instance;
    
    @PostConstruct
    public void init() {
        // 验证关键配置
        if (StringUtils.isBlank(bmsSecret)) {
            throw new IllegalStateException("BMS JWT密钥未配置，请设置 cathay.security.jwt.bms.secret");
        }
        if (bmsSecret.length() < 32) {
            throw new IllegalStateException("BMS JWT密钥长度不足，至少需要32位");
        }
        
        instance = this;
        log.info("BMS JWT工具类初始化完成");
        log.info("Access Token有效期: {} 小时", bmsAccessTokenEffectiveHours);
        log.info("Refresh Token有效期: {} 小时", bmsRefreshTokenEffectiveHours);
        log.info("发行人: {}", bmsIssuer);
        log.info("受众: {}", bmsAudience);
    }
    
    // 测试用的setter方法（仅在测试环境使用）
    public void setBmsSecret(String bmsSecret) {
        this.bmsSecret = bmsSecret;
    }
    
    public void setBmsAccessTokenEffectiveHours(int bmsAccessTokenEffectiveHours) {
        this.bmsAccessTokenEffectiveHours = bmsAccessTokenEffectiveHours;
    }
    
    public void setBmsRefreshTokenEffectiveHours(int bmsRefreshTokenEffectiveHours) {
        this.bmsRefreshTokenEffectiveHours = bmsRefreshTokenEffectiveHours;
    }
    
    public void setBmsIssuer(String bmsIssuer) {
        this.bmsIssuer = bmsIssuer;
    }
    
    public void setBmsAudience(String bmsAudience) {
        this.bmsAudience = bmsAudience;
    }

    /**
     * 创建BMS Access Token
     * 
     * @param username 用户名
     * @param userInfo 用户信息JSON字符串
     * @return BMS Access Token
     */
    public static String createBmsAccessToken(String username, String userInfo) {
        if (instance == null) {
            throw new IllegalStateException("BmsJwtUtils未初始化");
        }
        return instance.doCreateBmsAccessToken(username, userInfo);
    }
    
    /**
     * 实际创建BMS Access Token的方法
     */
    private String doCreateBmsAccessToken(String username, String userInfo) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(bmsSecret);
            Map<String, Object> map = new HashMap<>();
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireDateTime = now.plusHours(bmsAccessTokenEffectiveHours);
            map.put("alg", "HS256");
            map.put("typ", "JWT");
            String token = JWT.create()
                    .withHeader(map)
                    .withClaim("username", username)
                    .withClaim("userInfo", userInfo)
                    .withClaim("type", BMS_TOKEN_TYPE_ACCESS)
                    .withIssuer(bmsIssuer)
                    .withSubject(BMS_ACCESS_TOKEN_SUBJECT)
                    .withAudience(bmsAudience)
                    .withIssuedAt(now.atZone(ZoneId.systemDefault()).toInstant())
                    .withExpiresAt(expireDateTime.atZone(ZoneId.systemDefault()).toInstant())
                    .sign(algorithm);
            return token;
        } catch (JWTCreationException e) {
            log.error("创建BMS Access Token失败", e);
        }
        return null;
    }
    
    /**
     * 创建BMS Access Token（使用Map payload）
     * 
     * @param payload token负载信息
     * @return BMS Access Token
     */
    public static String createBmsAccessToken(Map<String, Object> payload) {
        if (payload == null) {
            return null;
        }
        String username = (String) payload.get("username");
        String userInfo = new Gson().toJson(payload);
        return createBmsAccessToken(username, userInfo);
    }
    
    /**
     * 创建BMS Refresh Token
     * 
     * @param username 用户名
     * @return BMS Refresh Token
     */
    public static String createBmsRefreshToken(String username) {
        if (instance == null) {
            throw new IllegalStateException("BmsJwtUtils未初始化");
        }
        return instance.doCreateBmsRefreshToken(username);
    }
    
    /**
     * 实际创建BMS Refresh Token的方法
     */
    private String doCreateBmsRefreshToken(String username) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(bmsSecret);
            Map<String, Object> map = new HashMap<>();
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireDateTime = now.plusHours(bmsRefreshTokenEffectiveHours);
            map.put("alg", "HS256");
            map.put("typ", "JWT");
            String token = JWT.create()
                    .withHeader(map)
                    .withClaim("username", username)
                    .withClaim("type", BMS_TOKEN_TYPE_REFRESH)
                    .withIssuer(bmsIssuer)
                    .withSubject(BMS_REFRESH_TOKEN_SUBJECT)
                    .withAudience(bmsAudience)
                    .withIssuedAt(now.atZone(ZoneId.systemDefault()).toInstant())
                    .withExpiresAt(expireDateTime.atZone(ZoneId.systemDefault()).toInstant())
                    .sign(algorithm);
            return token;
        } catch (JWTCreationException e) {
            log.error("创建BMS Refresh Token失败", e);
        }
        return null;
    }
    
    /**
     * 创建BMS Refresh Token（使用Map payload）
     * 
     * @param payload token负载信息
     * @return BMS Refresh Token
     */
    public static String createBmsRefreshToken(Map<String, Object> payload) {
        if (payload == null) {
            return null;
        }
        String username = (String) payload.get("username");
        return createBmsRefreshToken(username);
    }

    /**
     * 验证BMS Access Token是否有效
     * 
     * @param token BMS Access Token
     * @return 是否有效
     */
    public static boolean verifyBmsAccessToken(String token) {
        if (instance == null) {
            throw new IllegalStateException("BmsJwtUtils未初始化");
        }
        return instance.doVerifyBmsAccessToken(token);
    }
    
    /**
     * 实际验证BMS Access Token的方法
     */
    private boolean doVerifyBmsAccessToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(bmsSecret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(bmsIssuer)
                    .withSubject(BMS_ACCESS_TOKEN_SUBJECT)
                    .withClaim("type", BMS_TOKEN_TYPE_ACCESS)
                    .build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            log.debug("BMS Access Token验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 验证BMS Refresh Token是否有效
     * 
     * @param token BMS Refresh Token
     * @return 是否有效
     */
    public static boolean verifyBmsRefreshToken(String token) {
        if (instance == null) {
            throw new IllegalStateException("BmsJwtUtils未初始化");
        }
        return instance.doVerifyBmsRefreshToken(token);
    }
    
    /**
     * 实际验证BMS Refresh Token的方法
     */
    private boolean doVerifyBmsRefreshToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(bmsSecret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(bmsIssuer)
                    .withSubject(BMS_REFRESH_TOKEN_SUBJECT)
                    .withClaim("type", BMS_TOKEN_TYPE_REFRESH)
                    .build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            log.debug("BMS Refresh Token验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从BMS Access Token中获取用户名
     * 
     * @param token BMS Access Token
     * @return 用户名
     */
    public static String getUsernameFromBmsAccessToken(String token) {
        return getClaimFromBmsAccessToken(token, "username");
    }
    
    /**
     * 从BMS Access Token中获取用户信息
     * 
     * @param token BMS Access Token
     * @return 用户信息JSON字符串
     */
    public static String getUserInfoFromBmsAccessToken(String token) {
        return getClaimFromBmsAccessToken(token, "userInfo");
    }
    
    /**
     * 从BMS Refresh Token中获取用户名
     * 
     * @param token BMS Refresh Token
     * @return 用户名
     */
    public static String getUsernameFromBmsRefreshToken(String token) {
        return getClaimFromBmsRefreshToken(token, "username");
    }

    /**
     * 从BMS Access Token中获取指定的claim
     * 
     * @param token BMS Access Token
     * @param claimName claim名称
     * @return claim值
     */
    public static String getClaimFromBmsAccessToken(String token, String claimName) {
        if (instance == null) {
            throw new IllegalStateException("BmsJwtUtils未初始化");
        }
        return instance.doGetClaimFromBmsAccessToken(token, claimName);
    }
    
    /**
     * 实际获取BMS Access Token claim的方法
     */
    private String doGetClaimFromBmsAccessToken(String token, String claimName) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(bmsSecret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(bmsIssuer)
                    .withSubject(BMS_ACCESS_TOKEN_SUBJECT)
                    .build();
            DecodedJWT jwt = verifier.verify(token);
            Claim claim = jwt.getClaim(claimName);
            return claim.asString();
        } catch (JWTVerificationException e) {
            log.debug("从BMS Access Token获取claim失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 从BMS Refresh Token中获取指定的claim
     * 
     * @param token BMS Refresh Token
     * @param claimName claim名称
     * @return claim值
     */
    public static String getClaimFromBmsRefreshToken(String token, String claimName) {
        if (instance == null) {
            throw new IllegalStateException("BmsJwtUtils未初始化");
        }
        return instance.doGetClaimFromBmsRefreshToken(token, claimName);
    }
    
    /**
     * 实际获取BMS Refresh Token claim的方法
     */
    private String doGetClaimFromBmsRefreshToken(String token, String claimName) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(bmsSecret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(bmsIssuer)
                    .withSubject(BMS_REFRESH_TOKEN_SUBJECT)
                    .build();
            DecodedJWT jwt = verifier.verify(token);
            Claim claim = jwt.getClaim(claimName);
            return claim.asString();
        } catch (JWTVerificationException e) {
            log.debug("从BMS Refresh Token获取claim失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取BMS Token类型
     * 
     * @param token BMS Token
     * @return token类型
     */
    public static String getBmsTokenType(String token) {
        if (instance == null) {
            throw new IllegalStateException("BmsJwtUtils未初始化");
        }
        return instance.doGetBmsTokenType(token);
    }
    
    /**
     * 实际获取BMS Token类型的方法
     */
    private String doGetBmsTokenType(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(bmsSecret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(bmsIssuer)
                    .build();
            DecodedJWT jwt = verifier.verify(token);
            Claim claim = jwt.getClaim("type");
            return claim.asString();
        } catch (JWTVerificationException e) {
            log.debug("获取BMS Token类型失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析BMS Token获取用户信息对象
     * 
     * @param token BMS Access Token
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 用户信息对象
     */
    public static <T> T decodeBmsToken(String token, Class<T> clazz) {
        String userInfoJson = getUserInfoFromBmsAccessToken(token);
        if (StringUtils.isBlank(userInfoJson)) {
            return null;
        }
        try {
            return new Gson().fromJson(userInfoJson, clazz);
        } catch (Exception e) {
            log.debug("解析BMS Token用户信息失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查token是否为BMS系统的token
     * 
     * @param token 待检查的token
     * @return 是否为BMS token
     */
    public static boolean isBmsToken(String token) {
        String tokenType = getBmsTokenType(token);
        return BMS_TOKEN_TYPE_ACCESS.equals(tokenType) || BMS_TOKEN_TYPE_REFRESH.equals(tokenType);
    }
}