package cathayfuture.opm.adapter.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 登录速率限制器
 * 防止暴力破解攻击，实现基于IP和用户名的双重限制
 * 
 * <AUTHOR> Code  
 * @date 2025-08-13
 */
@Slf4j
@Component
public class LoginRateLimiter {

    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * IP限制：每个IP每分钟最多尝试次数
     */
    @Value("${cathay.security.rate-limit.ip.max-attempts:10}")
    private int ipMaxAttempts;
    
    /**
     * IP限制：时间窗口（分钟）
     */
    @Value("${cathay.security.rate-limit.ip.window-minutes:1}")
    private int ipWindowMinutes;
    
    /**
     * 用户名限制：每个用户名每小时最多尝试次数
     */
    @Value("${cathay.security.rate-limit.username.max-attempts:5}")
    private int usernameMaxAttempts;
    
    /**
     * 用户名限制：时间窗口（小时）
     */
    @Value("${cathay.security.rate-limit.username.window-hours:1}")
    private int usernameWindowHours;
    
    /**
     * IP锁定时间（分钟）
     */
    @Value("${cathay.security.rate-limit.ip.lockout-minutes:30}")
    private int ipLockoutMinutes;
    
    /**
     * 用户名锁定时间（小时）
     */
    @Value("${cathay.security.rate-limit.username.lockout-hours:24}")
    private int usernameLockoutHours;

    public LoginRateLimiter(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 检查是否允许登录尝试
     * 
     * @param request HTTP请求
     * @param username 用户名
     * @return 是否允许尝试
     */
    public boolean allowLoginAttempt(HttpServletRequest request, String username) {
        String clientIp = getClientIp(request);
        
        // 检查IP限制
        if (isIpBlocked(clientIp)) {
            log.warn("IP {} 已被锁定，拒绝登录尝试", maskIp(clientIp));
            return false;
        }
        
        // 检查用户名限制
        if (isUsernameBlocked(username)) {
            log.warn("用户名 {} 已被锁定，拒绝登录尝试", maskUsername(username));
            return false;
        }
        
        return true;
    }

    /**
     * 记录登录失败
     * 
     * @param request HTTP请求
     * @param username 用户名
     */
    public void recordLoginFailure(HttpServletRequest request, String username) {
        String clientIp = getClientIp(request);
        
        // 记录IP失败次数
        recordIpFailure(clientIp);
        
        // 记录用户名失败次数
        recordUsernameFailure(username);
    }

    /**
     * 记录登录成功
     * 
     * @param request HTTP请求
     * @param username 用户名
     */
    public void recordLoginSuccess(HttpServletRequest request, String username) {
        String clientIp = getClientIp(request);
        
        // 清除IP失败记录
        clearIpFailureRecord(clientIp);
        
        // 清除用户名失败记录
        clearUsernameFailureRecord(username);
        
        log.debug("清除 IP {} 和用户名 {} 的失败记录", maskIp(clientIp), maskUsername(username));
    }

    /**
     * 检查IP是否被锁定
     */
    private boolean isIpBlocked(String clientIp) {
        String lockKey = "login:ip:lock:" + clientIp;
        return Boolean.TRUE.equals(redisTemplate.hasKey(lockKey));
    }

    /**
     * 检查用户名是否被锁定
     */
    private boolean isUsernameBlocked(String username) {
        String lockKey = "login:username:lock:" + username;
        return Boolean.TRUE.equals(redisTemplate.hasKey(lockKey));
    }

    /**
     * 记录IP失败次数
     */
    private void recordIpFailure(String clientIp) {
        String countKey = "login:ip:count:" + clientIp;
        String lockKey = "login:ip:lock:" + clientIp;
        
        // 增加失败次数
        Long failureCount = redisTemplate.opsForValue().increment(countKey);
        
        // 设置过期时间
        if (failureCount == 1) {
            redisTemplate.expire(countKey, ipWindowMinutes, TimeUnit.MINUTES);
        }
        
        log.debug("IP {} 登录失败次数: {}/{}", maskIp(clientIp), failureCount, ipMaxAttempts);
        
        // 检查是否需要锁定
        if (failureCount >= ipMaxAttempts) {
            redisTemplate.opsForValue().set(lockKey, "blocked", ipLockoutMinutes, TimeUnit.MINUTES);
            log.warn("IP {} 登录失败次数达到 {} 次，锁定 {} 分钟", 
                    maskIp(clientIp), ipMaxAttempts, ipLockoutMinutes);
        }
    }

    /**
     * 记录用户名失败次数
     */
    private void recordUsernameFailure(String username) {
        String countKey = "login:username:count:" + username;
        String lockKey = "login:username:lock:" + username;
        
        // 增加失败次数
        Long failureCount = redisTemplate.opsForValue().increment(countKey);
        
        // 设置过期时间
        if (failureCount == 1) {
            redisTemplate.expire(countKey, usernameWindowHours, TimeUnit.HOURS);
        }
        
        log.debug("用户名 {} 登录失败次数: {}/{}", maskUsername(username), failureCount, usernameMaxAttempts);
        
        // 检查是否需要锁定
        if (failureCount >= usernameMaxAttempts) {
            redisTemplate.opsForValue().set(lockKey, "blocked", usernameLockoutHours, TimeUnit.HOURS);
            log.warn("用户名 {} 登录失败次数达到 {} 次，锁定 {} 小时", 
                    maskUsername(username), usernameMaxAttempts, usernameLockoutHours);
        }
    }

    /**
     * 清除IP失败记录
     */
    private void clearIpFailureRecord(String clientIp) {
        String countKey = "login:ip:count:" + clientIp;
        redisTemplate.delete(countKey);
    }

    /**
     * 清除用户名失败记录
     */
    private void clearUsernameFailureRecord(String username) {
        String countKey = "login:username:count:" + username;
        redisTemplate.delete(countKey);
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个代理的情况，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }

    /**
     * IP地址脱敏
     */
    private String maskIp(String ip) {
        if (ip == null || ip.length() < 7) {
            return "***";
        }
        
        // IPv4脱敏：保留前两段，后两段用*代替
        if (ip.contains(".")) {
            String[] parts = ip.split("\\.");
            if (parts.length >= 4) {
                return parts[0] + "." + parts[1] + ".***.***.";
            }
        }
        
        // IPv6或其他格式的简单脱敏
        return ip.substring(0, Math.min(4, ip.length())) + "***";
    }

    /**
     * 用户名脱敏
     */
    private String maskUsername(String username) {
        if (username == null || username.length() <= 3) {
            return "***";
        }
        
        // 只显示前2位和后1位，中间用*代替
        return username.substring(0, 2) + "***" + username.substring(username.length() - 1);
    }

    /**
     * 获取剩余锁定时间（秒）
     * 
     * @param request HTTP请求
     * @param username 用户名
     * @return 剩余锁定时间，-1表示未被锁定
     */
    public long getRemainingLockTime(HttpServletRequest request, String username) {
        String clientIp = getClientIp(request);
        
        // 检查IP锁定剩余时间
        String ipLockKey = "login:ip:lock:" + clientIp;
        Long ipTtl = redisTemplate.getExpire(ipLockKey, TimeUnit.SECONDS);
        
        // 检查用户名锁定剩余时间
        String usernameLockKey = "login:username:lock:" + username;
        Long usernameTtl = redisTemplate.getExpire(usernameLockKey, TimeUnit.SECONDS);
        
        // 返回较大的剩余时间
        long maxTtl = Math.max(ipTtl != null ? ipTtl : -1, usernameTtl != null ? usernameTtl : -1);
        return maxTtl;
    }
}