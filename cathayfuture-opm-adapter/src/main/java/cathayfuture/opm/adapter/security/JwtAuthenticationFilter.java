package cathayfuture.opm.adapter.security;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    @Resource
    private TokenManagementService tokenManagementService;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {

        String token = extractTokenFromRequest(request);
        if (StringUtils.hasText(token)) {
            try {
                // 1. 首先验证JWT token的签名和过期时间
                if (BmsJwtUtils.verifyBmsAccessToken(token)) {
                    // 2. 验证token是否在Redis中存在（未被登出）
                    if (tokenManagementService.isTokenValid(token)) {
                        // 3. 解析token中的用户信息
                        String userJson = BmsJwtUtils.getUserInfoFromBmsAccessToken(token);
                        if (StringUtils.hasText(userJson)) {
                            // 安全地解析用户信息JSON
                            UserAuthInfo authInfo = parseUserInfoSafely(userJson);
                            if (authInfo != null && StringUtils.hasText(authInfo.getUsername())) {
                                // 创建权限列表
                                List<SimpleGrantedAuthority> authorities = createAuthorities(authInfo.getAuthorities());

                                // 创建Authentication对象
                                JwtAuthenticationToken authentication =
                                    new JwtAuthenticationToken(authInfo.getUsername(), token, authorities);
                                SecurityContextHolder.getContext().setAuthentication(authentication);

                                logger.debug("JWT认证成功，用户: {}, 权限数量: {}", 
                                    maskSensitiveInfo(authInfo.getUsername()), authorities.size());
                            } else {
                                logger.debug("用户信息解析失败或用户名为空");
                            }
                        } else {
                            logger.debug("Token中未找到用户信息");
                        }
                    } else {
                        logger.debug("Token在Redis中不存在或已失效");
                    }
                } else {
                    logger.debug("JWT token签名验证失败");
                }
            } catch (Exception e) {
                logger.debug("JWT token验证过程异常", e);
                // 清除可能的部分认证信息
                SecurityContextHolder.clearContext();
            }
        }

        filterChain.doFilter(request, response);
    }
    
    /**
     * 安全地解析用户信息JSON
     */
    private UserAuthInfo parseUserInfoSafely(String userJson) {
        try {
            if (!StringUtils.hasText(userJson)) {
                logger.debug("用户信息JSON为空");
                return null;
            }
            
            // 长度限制，防止过大的JSON攻击
            if (userJson.length() > 4096) {
                logger.warn("用户信息JSON过长，可能存在安全风险");
                return null;
            }
            
            JSONObject userInfo = JSON.parseObject(userJson);
            if (userInfo == null) {
                logger.debug("用户信息JSON解析为null");
                return null;
            }
            
            // 验证必要字段
            String username = userInfo.getString("username");
            if (!StringUtils.hasText(username)) {
                logger.debug("用户名为空或无效");
                return null;
            }
            
            // 用户名长度和格式验证
            if (username.length() > 50 || !username.matches("^[a-zA-Z0-9_.-]+$")) {
                logger.warn("用户名格式不符合要求");
                return null;
            }
            
            // 安全地获取权限列表
            List<String> authorities = new ArrayList<>();
            try {
                if (userInfo.containsKey("authorities") && userInfo.get("authorities") != null) {
                    authorities = userInfo.getJSONArray("authorities").toJavaList(String.class);
                    // 验证权限数量限制
                    if (authorities.size() > 50) {
                        logger.warn("权限数量过多，可能存在安全风险");
                        authorities = authorities.subList(0, 50);
                    }
                    // 验证权限格式
                    authorities = authorities.stream()
                        .filter(auth -> StringUtils.hasText(auth) && auth.length() <= 100)
                        .collect(java.util.stream.Collectors.toList());
                }
            } catch (Exception e) {
                logger.debug("权限信息解析失败，使用默认权限: {}", e.getMessage());
                authorities.add("ROLE_USER");
            }
            
            return new UserAuthInfo(username, authorities);
            
        } catch (JSONException e) {
            logger.warn("用户信息JSON格式错误");
            return null;
        } catch (Exception e) {
            logger.warn("用户信息解析异常");
            return null;
        }
    }
    
    /**
     * 创建权限列表
     */
    private List<SimpleGrantedAuthority> createAuthorities(List<String> authoritiesStr) {
        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
        
        if (authoritiesStr != null && !authoritiesStr.isEmpty()) {
            for (String auth : authoritiesStr) {
                if (StringUtils.hasText(auth)) {
                    authorities.add(new SimpleGrantedAuthority(auth.trim()));
                }
            }
        }
        
        // 如果没有权限，添加默认权限
        if (authorities.isEmpty()) {
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
            logger.debug("用户权限列表为空，使用默认权限 ROLE_USER");
        }
        
        return authorities;
    }
    
    /**
     * 敏感信息脱敏处理
     */
    private String maskSensitiveInfo(String info) {
        if (!StringUtils.hasText(info)) {
            return "***";
        }
        
        if (info.length() <= 3) {
            return "***";
        }
        
        // 只显示前2位和后1位，中间用*代替
        return info.substring(0, 2) + "***" + info.substring(info.length() - 1);
    }
    
    private String extractTokenFromRequest(HttpServletRequest request) {
        String header = request.getHeader("Authorization");
        if (StringUtils.hasText(header) && header.startsWith("Bearer ")) {
            return header.substring(7);
        }
        return header;
    }
    
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String uri = request.getRequestURI();
        // 跳过登录接口和公开接口
        if (uri.startsWith("/bms/auth/") || uri.startsWith("/mobile/wx/login") ||
            uri.startsWith("/mobile/callbacks/") || uri.startsWith("/actuator/") ||
            uri.startsWith("/swagger-") || uri.startsWith("/v3/api-docs") ||
            uri.equals("/swagger-ui.html") || uri.startsWith("/webjars/")) {
            return true;
        }
        // 对其他路径进行JWT验证
        return false;
    }
    
    /**
     * 用户认证信息内部类
     */
    private static class UserAuthInfo {
        private final String username;
        private final List<String> authorities;
        
        public UserAuthInfo(String username, List<String> authorities) {
            this.username = username;
            this.authorities = authorities != null ? authorities : new ArrayList<>();
        }
        
        public String getUsername() {
            return username;
        }
        
        public List<String> getAuthorities() {
            return authorities;
        }
    }
}