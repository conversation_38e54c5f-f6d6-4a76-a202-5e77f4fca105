package cathayfuture.opm.adapter.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

/**
 * 带日志的认证提供者
 * 用于调试密码验证过程
 */
@Slf4j
public class LoggingAuthenticationProvider extends DaoAuthenticationProvider {

    public LoggingAuthenticationProvider(UserDetailsService userDetailsService, PasswordEncoder passwordEncoder) {
        super();
        setUserDetailsService(userDetailsService);
        setPasswordEncoder(passwordEncoder);
        log.info("LoggingAuthenticationProvider 初始化完成");
        log.info("UserDetailsService: {}", userDetailsService.getClass().getSimpleName());
        log.info("PasswordEncoder: {}", passwordEncoder.getClass().getSimpleName());
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        log.info("=== 开始认证流程 ===");
        
        String username = authentication.getName();
        String password = (String) authentication.getCredentials();
        
        log.info("认证请求 - 用户名: {}", username);
        log.info("认证请求 - 密码长度: {}", password != null ? password.length() : 0);
        log.info("认证请求 - 密码内容: {}", password); // 注意：生产环境中不要记录明文密码
        
        try {
            // 调用父类的认证方法
            Authentication result = super.authenticate(authentication);
            log.info("✅ 认证成功: {}", username);
            log.info("认证结果权限: {}", result.getAuthorities());
            return result;
        } catch (AuthenticationException e) {
            log.error("❌ 认证失败: {} - {}", username, e.getMessage());
            throw e;
        }
    }

    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, 
            UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        
        log.info("=== 开始密码验证 ===");
        
        String username = userDetails.getUsername();
        String presentedPassword = authentication.getCredentials().toString();
        String storedPassword = userDetails.getPassword();
        
        log.info("用户名: {}", username);
        log.info("输入密码: {}", presentedPassword);
        log.info("存储密码哈希: {}", storedPassword);
        log.info("存储密码哈希长度: {}", storedPassword != null ? storedPassword.length() : 0);
        log.info("存储密码哈希格式: {}", storedPassword != null ? storedPassword.substring(0, Math.min(10, storedPassword.length())) : "null");
        
        PasswordEncoder encoder = getPasswordEncoder();
        log.info("使用的密码编码器: {}", encoder.getClass().getSimpleName());
        
        if (presentedPassword == null) {
            log.error("❌ 输入密码为空");
            throw new BadCredentialsException("密码不能为空");
        }
        
        if (storedPassword == null || storedPassword.isEmpty()) {
            log.error("❌ 存储密码为空");
            throw new BadCredentialsException("用户密码未设置");
        }
        
        // 执行密码匹配
        log.info("开始执行密码匹配...");
        boolean matches = encoder.matches(presentedPassword, storedPassword);
        log.info("密码匹配结果: {}", matches);
        
        if (!matches) {
            log.error("❌ 密码不匹配");
            log.error("输入密码: '{}'", presentedPassword);
            log.error("存储哈希: '{}'", storedPassword);
            
            // 尝试手动验证（用于调试）
            try {
                log.info("尝试手动BCrypt验证...");
                // 这里可以添加手动BCrypt验证的代码
                if (storedPassword.startsWith("$2a$") || storedPassword.startsWith("$2b$") || storedPassword.startsWith("$2y$")) {
                    log.info("检测到BCrypt格式密码");
                } else {
                    log.warn("密码不是BCrypt格式");
                }
            } catch (Exception e) {
                log.error("手动验证异常: {}", e.getMessage());
            }
            
            throw new BadCredentialsException("用户名或密码错误");
        }
        
        log.info("✅ 密码验证成功");
    }

    // retrieveUser方法是final的，不能重写，所以我们在authenticate方法中添加日志
}
