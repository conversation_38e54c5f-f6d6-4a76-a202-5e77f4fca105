package cathayfuture.opm.adapter.security;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Service
public class FixedUserDetailsService implements UserDetailsService {

    @Value("${cathay.auth.users[0].username}")
    private String username1;

    @Value("${cathay.auth.users[0].password}")
    private String password1;

    @Value("${cathay.auth.users[0].role:ROLE_ADMIN}")
    private String role1;

    @Value("${cathay.auth.users[1].username}")
    private String username2;

    @Value("${cathay.auth.users[1].password}")
    private String password2;

    @Value("${cathay.auth.users[1].role:ROLE_USER}")
    private String role2;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    private final Map<String, UserInfo> users = new HashMap<>();

    @PostConstruct
    public void initUsers() {
        users.put(username1, new UserInfo(username1, password1, role1));
        users.put(username2, new UserInfo(username2, password2, role2));
    }

    @Override
    public UserDetails loadUserByUsername(String inputUsername) throws UsernameNotFoundException {
        UserInfo userInfo = users.get(inputUsername);
        if (userInfo == null) {
            throw new UsernameNotFoundException("用户不存在: " + inputUsername);
        }

        // 使用BCrypt编码密码
        return User.builder()
            .username(userInfo.username)
            .password(passwordEncoder.encode(userInfo.password))
            .authorities(userInfo.role)
            .accountExpired(false)
            .accountLocked(false)
            .credentialsExpired(false)
            .disabled(false)
            .build();
    }

    private static class UserInfo {
        final String username;
        final String password;
        final String role;

        UserInfo(String username, String password, String role) {
            this.username = username;
            this.password = password;
            this.role = role;
        }
    }
}