package cathayfuture.opm.adapter;

/**
 * <AUTHOR>
 * */
public enum CommonResponseCode {

    //成功
    SUCCESS("SUCCESS","操作成功"),
    //业务错误
    BUSSINESS_ERROR("BUSSINESS_ERROR", "业务异常"),

    //请求参数错误
    BAD_REQUEST("BAD_REQUEST","请求参数校验错误"),

    //服务异常
    SERVER_ERROR("SERVER_ERROR","服务器异常"),

    //禁止访问
    ACCESS_ERROR("ACCESS_ERROR","禁止访问"),

    //未授权、登陆失败等
    UNAUTHORIZED("UNAUTHORIZED","未授权"),
    ;



    public final String resCode ;
    public final String message ;

    CommonResponseCode(String resCode, String message){
        this.resCode = resCode ;
        this.message = message ;
    }

}
