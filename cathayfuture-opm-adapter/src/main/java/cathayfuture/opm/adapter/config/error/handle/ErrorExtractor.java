package cathayfuture.opm.adapter.config.error.handle;

import com.google.common.base.CaseFormat;

/**
 * <AUTHOR>
 * */
public class ErrorExtractor {

    public static final String SVC_EXCEPTION_SUFFIX = "SvcException";

    public static String extractErrorCode(Class<? extends Throwable> e, String suffix){
        return extractErrorCode(e.getName(), suffix);
    }

    public static String extractErrorCode(String exceptionClassName, String suffix){
        String[] split = exceptionClassName.split("\\.");
        String name = split[split.length - 1].replace(suffix,"");
        return CaseFormat.UPPER_CAMEL.to(CaseFormat.UPPER_UNDERSCORE, name);
    }


}
