package cathayfuture.opm.adapter.config.error.handle;

import static cathayfuture.opm.adapter.config.error.handle.ErrorExtractor.SVC_EXCEPTION_SUFFIX;
import static cathayfuture.opm.adapter.config.error.handle.ErrorExtractor.extractErrorCode;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.client.AppException;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 * */
@Slf4j
@ControllerAdvice
public class MyExceptionHandler {

    @ExceptionHandler(Throwable.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public CommonResponseDTO handlerThrowable(Throwable throwable, HttpServletRequest request) {
        log.error("uri:" + request.getRequestURI() + ",errormsg:" + throwable.getMessage(), throwable);
        return CommonResponseDTO.serverError("服务器异常");
    }

    @ExceptionHandler(AppException.class)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CommonResponseDTO<ErrorInfo> handlerServiceException(AppException e) {
        log.warn("ApplicationException:" + e.getMessage(), e);
        return CommonResponseDTO.bussinessError(
                new ErrorInfo(
                        extractErrorCode(e.getClass(), SVC_EXCEPTION_SUFFIX), e.getMessage()));
    }


    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CommonResponseDTO<List<FieldErrorInfo>> handlerMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        return CommonResponseDTO.badRequest(getFieldErrorInfoError(e.getBindingResult(), e));
    }

    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public CommonResponseDTO<List<FieldErrorInfo>> handlerMethodArgumentNotValidException(BindException e) {
        return CommonResponseDTO.badRequest(getFieldErrorInfoError(e.getBindingResult(), e));
    }

    private List<FieldErrorInfo> getFieldErrorInfoError(BindingResult result, Exception e) {
        StringBuilder errMessage = new StringBuilder();
        List<FieldErrorInfo> errors = result.getAllErrors().stream().map(input -> {
            FieldError fieldError = FieldError.class.cast(input);
            log.debug("-------field:{},message:{}", fieldError.getField(), fieldError.getDefaultMessage());
            errMessage.append(",").append(fieldError.getDefaultMessage());
            return new FieldErrorInfo(fieldError.getField(), fieldError.getDefaultMessage());
        }).collect(Collectors.toList());
        log.warn(e.getClass().getSimpleName() + ":" + e.getMessage());
        return errors;
    }
}
