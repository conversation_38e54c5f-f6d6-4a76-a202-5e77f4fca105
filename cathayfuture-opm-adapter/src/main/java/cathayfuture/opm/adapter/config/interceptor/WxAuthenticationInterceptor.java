package cathayfuture.opm.adapter.config.interceptor;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.adapter.mobile.utils.JwtUtils;
import cathayfuture.opm.client.utils.WxAccountHolder;
import cathayfuture.opm.client.account.dto.response.AccountRespDTO;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.lang.Nullable;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date ********
 */
@Slf4j
public class WxAuthenticationInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response,
                             Object handler) {
        try {
            String token = request.getHeader("Authorization");
            if (StringUtils.isEmpty(token)) {
                responseWrite(response, CommonResponseDTO.unauthorized());
                return false;
            }

            AccountRespDTO account = JwtUtils.decodeToken(token, AccountRespDTO.class);
            if (Objects.isNull(account)) {
                responseWrite(response, CommonResponseDTO.unauthorized());
                return false;
            }
            WxAccountHolder.set(account);
            return true;
        } catch (Exception e) {
            responseWrite(response, CommonResponseDTO.unauthorized());
            return false;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable Exception ex) throws Exception {
        WxAccountHolder.remove();
    }

    public void responseWrite(HttpServletResponse response, Object message) {
        try {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8");
            response.setHeader("Access-Control-Allow-Origin", "*");
            PrintWriter writer = response.getWriter();
            writer.write(new Gson().toJson(message));
            writer.flush();
            writer.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }
}
