package cathayfuture.opm.adapter.config;

import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.servers.Server;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 描述: OpenAPI 3.0 配置
 *
 * <AUTHOR>
 * @date 8/24/22
 */
@OpenAPIDefinition(
        tags = {
                @Tag(name = "BMS认证", description = "后台管理系统认证模块"),
                @Tag(name = "学生管理", description = "学生信息管理模块"),
                @Tag(name = "订单管理", description = "订单管理模块"),
                @Tag(name = "考勤管理", description = "考勤记录管理模块"),
                @Tag(name = "移动端", description = "移动端接口模块")
        },
        info = @Info(
                title = "华夏未来OPM系统 API 文档",
                description = "华夏未来运营管理系统接口文档",
                version = "1.0.0",
                contact = @Contact(name = "ChengZhenxing", email = "<EMAIL>"),
                license = @License(name = "Apache 2.0", url = "http://www.apache.org/licenses/LICENSE-2.0.html")
        ),
        servers = {
                @Server(description = "本地开发服务器", url = "http://localhost:8080"),
                @Server(description = "测试环境服务器", url = "http://test.cathayfuture.com")
        },
        security = @SecurityRequirement(name = "bearerAuth"),
        externalDocs = @ExternalDocumentation(
                description = "项目文档",
                url = "http://localhost:8080/swagger-ui/index.html"
        )
)
@Configuration
public class OpenApiConfiguration {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .components(new Components()
                        .addSecuritySchemes("bearerAuth",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("JWT认证token")))
                .addSecurityItem(new io.swagger.v3.oas.models.security.SecurityRequirement().addList("bearerAuth"));
    }
}