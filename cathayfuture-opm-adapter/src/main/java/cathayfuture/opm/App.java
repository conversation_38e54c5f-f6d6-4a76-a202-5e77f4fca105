package cathayfuture.opm;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
// 移除Spring Cloud OpenFeign依赖
import org.springframework.context.annotation.ComponentScan;

@MapperScan({
        "cathayfuture.opm.infra.**.repository.mapper"
})
@SpringBootApplication
// 移除@EnableDiscoveryClient
@ComponentScan({"cathayfuture.opm",
        "com.dtyunxi.huieryun.oss",
        "com.dtyunxi.huieryun.cache",
        "com.dtyunxi.huieryun.lock.api"})
// 移除com.dtyunxi.cube和com.dtyunxi.yundt.cube相关的扫描
// 移除EnableFeignClients注解
public class App {

    public static void main(String[] args) {
        SpringApplication.run(App.class, args);
    }
}
