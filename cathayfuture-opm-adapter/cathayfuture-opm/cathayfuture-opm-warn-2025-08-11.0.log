2025-08-11 17:02:27,964 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'bmsAuthController' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/bms/auth/controller/BmsAuthController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'securityConfig' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/security/SecurityConfig.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'fixedUserDetailsService' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/security/FixedUserDetailsService.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'securityConfig': Requested bean is currently in creation: Is there an unresolvable circular reference?
2025-08-11 17:03:42,483 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'excelUploadController' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/bms/order/controller/ExcelUploadController.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [cathayfuture.opm.adapter.bms.order.controller.ExcelUploadController]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The import cathayfuture.opm.app.common.ExcelUploadService cannot be resolved
	ExcelUploadService cannot be resolved to a type
	ExcelUploadService cannot be resolved to a type

2025-08-11 17:06:19,275 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.s.c.bcrypt.BCryptPasswordEncoder [L:90] - Encoded password does not look like BCrypt
2025-08-11 17:06:19,277 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.s.c.bcrypt.BCryptPasswordEncoder [L:90] - Encoded password does not look like BCrypt
