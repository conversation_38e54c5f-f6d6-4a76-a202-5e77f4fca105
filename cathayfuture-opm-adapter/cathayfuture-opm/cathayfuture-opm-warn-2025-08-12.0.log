2025-08-12 14:58:44,650 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'bmsAuthController' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/bms/auth/controller/BmsAuthController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'securityConfig' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/security/SecurityConfig.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseUserDetailsService': Resolution of declared constructors on bean Class [cathayfuture.opm.adapter.security.DatabaseUserDetailsService] from ClassLoader [jdk.internal.loader.ClassLoaders$AppClassLoader@4b9af9a9] failed; nested exception is java.lang.NoClassDefFoundError: cathayfuture/opm/domain/system/repository/SysUserRepository
