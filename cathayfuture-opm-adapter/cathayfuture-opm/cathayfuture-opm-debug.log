2025-08-13 09:40:28,594 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 09:40:31,075 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 09:41:19,219 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 09:41:21,650 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 09:47:17,996 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 09:47:20,292 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 11:03:02,555 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 11:03:04,936 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 11:03:32,579 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:63] - 密码哈希长度: 60
2025-08-13 11:03:32,580 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:64] - 密码哈希格式: $2a$10$FxZ
2025-08-13 11:03:32,587 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:146] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-13 11:03:32,726 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:58] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-13 11:03:32,733 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:201] - JWT Refresh Token生成成功: username=admin
2025-08-13 11:04:17,131 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:87] - Token验证成功: username=admin
2025-08-13 11:04:17,149 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.JwtAuthenticationFilter [L:57] - JWT认证成功，用户: ad***n, 权限数量: 1
2025-08-13 11:04:47,171 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.TokenManagementService [L:87] - Token验证成功: username=admin
2025-08-13 11:04:47,172 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.JwtAuthenticationFilter [L:57] - JWT认证成功，用户: ad***n, 权限数量: 1
2025-08-13 11:05:48,545 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.TokenManagementService [L:129] - 删除用户token映射: username=admin
2025-08-13 11:06:02,921 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:79] - Token在Redis中不存在: eyJ0eXAiOiJKV1QiLCJh
2025-08-13 11:06:02,921 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.JwtAuthenticationFilter [L:66] - Token在Redis中不存在或已失效
