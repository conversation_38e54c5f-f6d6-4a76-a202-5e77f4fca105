{
    "extensions.ignoreRecommendations": false,
    "team.showWelcomeMessage": false,
    "git.enableSmartCommit": true,
    "vsicons.dontShowNewVersionMessage": true,
    "git.autofetch": true,
    "react.beautify.onSave": true,
    "files.associations": {
        "*.js": "javascriptreact"
    },
    "git.confirmSync": false,
    "explorer.confirmDelete": false,
    "[markdown]": {},
    "eslint.enable": true,
    "eslint.options": {
        "extensions": [
            ".js",
            ".jsx",
            ".vue"
        ]
    },
    // 配置 ESLint 检查的文件类型
    "eslint.validate": [
        "javascript",
        "javascriptreact",
        {
            "language": "vue",
            "autoFix": true
        }
    ],
    "vetur.validation.template": false,
    // 保存时eslint自动修复错误
    "eslint.autoFixOnSave": true,
    "editor.tabSize": 2,
    "jest.autoEnable": false,
    "jest.pathToConfig": "./.jest.js",
    "terminal.integrated.rendererType": "dom",
    "window.zoomLevel": 0,
    "editor.quickSuggestions": {
        "strings": true
    },
    "diffEditor.renderSideBySide": true
}