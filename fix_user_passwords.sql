-- 修复用户密码的SQL脚本
-- 使用正确的BCrypt哈希值

-- 更新admin用户密码 (admin123)
UPDATE sys_user 
SET password = '$2b$12$MVx18fHaQf7TQeE83VfdkufS6FtsSByhksl1p4dcsNCJUQhbG5tZa',
    password_update_time = NOW()
WHERE username = 'admin';

-- 更新manager用户密码 (manager123)  
UPDATE sys_user 
SET password = '$2b$12$JKSCJlAHO.5sb70kjhqxoeGZEcj1NNPT8aY3g/D3B3M1V5sh69/Ui',
    password_update_time = NOW()
WHERE username = 'manager';

-- 验证更新结果
SELECT 
    id,
    username,
    user_code,
    real_name,
    oa_name,
    status,
    password_update_time,
    CASE 
        WHEN password LIKE '$2b$%' THEN '✅ BCrypt格式正确'
        WHEN password LIKE '$2a$%' THEN '⚠️ 旧BCrypt格式'
        ELSE '❌ 密码格式错误'
    END as password_format
FROM sys_user 
WHERE dr = 0
ORDER BY id;
