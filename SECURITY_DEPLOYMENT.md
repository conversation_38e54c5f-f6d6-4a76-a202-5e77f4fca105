# Cathayfuture OPM 安全部署指南

## 概述

本文档详细说明了华夏未来OPM系统的安全配置和部署要求，包括JWT密钥管理、登录限制、日志安全等关键安全措施。

## 环境变量配置

### 必需的安全配置

在生产环境部署时，必须设置以下环境变量：

```bash
# JWT密钥配置（必需）
export BMS_JWT_SECRET="your-very-secure-secret-key-at-least-32-characters-long-here"

# JWT Token有效期配置（可选，默认值如下）
export BMS_JWT_ACCESS_TOKEN_HOURS=24
export BMS_JWT_REFRESH_TOKEN_HOURS=48

# JWT发行人和受众（可选）
export BMS_JWT_ISSUER="CATHAY-FUTURE-BMS-SYSTEM"
export BMS_JWT_AUDIENCE="CATHAY-FUTURE-BMS-SYSTEM"

# 认证方式配置（可选，默认使用数据库认证）
export USE_DATABASE_AUTH=true

# 登录速率限制配置（可选，默认值如下）
export LOGIN_RATE_LIMIT_IP_MAX_ATTEMPTS=10
export LOGIN_RATE_LIMIT_IP_WINDOW_MINUTES=1
export LOGIN_RATE_LIMIT_IP_LOCKOUT_MINUTES=30
export LOGIN_RATE_LIMIT_USERNAME_MAX_ATTEMPTS=5
export LOGIN_RATE_LIMIT_USERNAME_WINDOW_HOURS=1
export LOGIN_RATE_LIMIT_USERNAME_LOCKOUT_HOURS=24
```

## JWT密钥安全要求

### 密钥生成建议

1. **长度要求**：密钥长度至少32个字符
2. **随机性**：使用加密安全的随机数生成器
3. **复杂性**：包含大小写字母、数字和特殊字符

#### 密钥生成示例

```bash
# 使用openssl生成256位随机密钥
openssl rand -base64 32

# 或使用Python生成
python3 -c "import secrets; print(secrets.token_urlsafe(32))"
```

### 密钥管理最佳实践

1. **不要硬编码**：绝不将密钥写入代码或配置文件
2. **环境隔离**：不同环境使用不同的密钥
3. **定期轮换**：建议每6个月更换一次密钥
4. **安全存储**：使用密钥管理服务（如Azure Key Vault、AWS KMS）
5. **最小权限**：只有必要的人员才能访问密钥

## 登录安全配置

### 速率限制参数说明

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `LOGIN_RATE_LIMIT_IP_MAX_ATTEMPTS` | 10 | 每个IP每分钟最多登录尝试次数 |
| `LOGIN_RATE_LIMIT_IP_WINDOW_MINUTES` | 1 | IP限制的时间窗口（分钟） |
| `LOGIN_RATE_LIMIT_IP_LOCKOUT_MINUTES` | 30 | IP被锁定的时间（分钟） |
| `LOGIN_RATE_LIMIT_USERNAME_MAX_ATTEMPTS` | 5 | 每个用户名每小时最多尝试次数 |
| `LOGIN_RATE_LIMIT_USERNAME_WINDOW_HOURS` | 1 | 用户名限制的时间窗口（小时） |
| `LOGIN_RATE_LIMIT_USERNAME_LOCKOUT_HOURS` | 24 | 用户名被锁定的时间（小时） |

### 调优建议

- **高流量环境**：适当增加IP最大尝试次数
- **高安全环境**：减少最大尝试次数，延长锁定时间
- **内网环境**：可以适当放宽IP限制

## 数据库用户管理

### 用户表结构要求

确保系统用户表包含以下安全相关字段：

```sql
-- 用户密码（BCrypt哈希）
password VARCHAR(255) NOT NULL,

-- 账户状态
status INT DEFAULT 1,  -- 1:启用, 0:禁用

-- 登录失败计数
login_failure_count INT DEFAULT 0,

-- 账户锁定时间
account_locked_time DATETIME NULL,

-- 密码更新时间
password_update_time DATETIME NULL,

-- 最后登录信息
last_login_time DATETIME NULL,
last_login_ip VARCHAR(45) NULL
```

### 密码策略

1. **最小长度**：8位字符
2. **复杂性要求**：包含大小写字母、数字
3. **历史检查**：不能重复使用最近5次密码
4. **有效期**：90天过期
5. **加密算法**：使用BCrypt（成本因子10-12）

## Redis配置

### 连接配置
```yaml
spring:
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:0}
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
```

### 安全考虑

1. **密码保护**：Redis实例必须设置密码
2. **网络隔离**：Redis不应暴露在公网
3. **数据过期**：合理设置token过期时间
4. **监控告警**：监控Redis连接和内存使用

## 日志安全配置

### 敏感信息处理

系统已实现敏感信息脱敏，包括：
- 用户名：显示前2位和后1位
- 真实姓名：显示姓氏
- IP地址：显示前两段
- Token：显示前8位和后4位
- 密码：永不记录原文

### 日志级别建议

```yaml
logging:
  level:
    root: INFO
    cathayfuture.opm: DEBUG  # 开发环境
    cathayfuture.opm.adapter.security: INFO  # 生产环境
```

## 网络安全配置

### 反向代理配置

使用Nginx作为反向代理时的安全配置：

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /path/to/certificate.pem;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # 限制请求大小
    client_max_body_size 10m;
    
    # 速率限制
    limit_req_zone $binary_remote_addr zone=login:10m rate=10r/m;
    
    location /bms/auth/login {
        limit_req zone=login burst=3 nodelay;
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 监控和告警

### 关键指标监控

1. **登录失败率**：异常高的失败率可能表示攻击
2. **IP封锁频率**：频繁封锁可能需要调整策略
3. **Token异常率**：JWT验证失败的频率
4. **系统错误率**：意外异常的频率

### 告警规则建议

```yaml
alerts:
  - name: "High Login Failure Rate"
    condition: "login_failure_rate > 0.5 for 5m"
    action: "notify_admin"
    
  - name: "Frequent IP Blocks"
    condition: "ip_block_rate > 10 per minute"
    action: "notify_security_team"
    
  - name: "JWT Verification Failures"
    condition: "jwt_error_rate > 0.1 for 2m"
    action: "notify_admin"
```

## 部署检查清单

### 部署前检查

- [ ] 环境变量已正确设置
- [ ] JWT密钥已生成并安全存储
- [ ] Redis连接配置正确
- [ ] 数据库用户表结构完整
- [ ] 日志配置适合环境
- [ ] 网络防火墙规则配置
- [ ] SSL证书已配置

### 部署后检查

- [ ] 登录功能正常
- [ ] JWT token生成和验证正常
- [ ] 速率限制功能有效
- [ ] 敏感信息已脱敏
- [ ] 监控和告警正常工作
- [ ] 日志记录正常且安全

## 故障排除

### 常见问题

1. **JWT初始化失败**
   - 检查BMS_JWT_SECRET是否设置且长度≥32位
   - 检查Spring Boot配置是否正确加载

2. **登录被频繁限制**
   - 检查Redis连接是否正常
   - 检查速率限制配置是否过于严格
   - 检查客户端IP获取是否正确

3. **用户认证失败**
   - 检查数据库用户密码是否正确加密
   - 检查用户状态是否正常
   - 检查账户是否被锁定

### 日志分析

关键日志级别：
- `ERROR`: 系统错误，需要立即处理
- `WARN`: 安全相关警告，需要关注
- `INFO`: 重要业务事件
- `DEBUG`: 详细调试信息（仅开发环境）

## 安全更新

定期检查并更新：
1. Spring Boot及相关依赖
2. JWT库版本
3. 数据库驱动
4. Redis客户端库
5. 安全补丁

建议建立定期安全审计机制，至少每季度进行一次安全评估。