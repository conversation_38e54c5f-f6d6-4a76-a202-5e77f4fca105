#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终认证测试 - 对比工作和不工作的用户
"""

import requests
import json
import bcrypt

def compare_working_vs_broken_users():
    """对比工作和不工作的用户"""
    print("=== 对比工作和不工作的用户 ===")
    
    base_url = "http://localhost:8080"
    
    # 获取所有用户
    response = requests.get(f"{base_url}/bms/system/user/enabled")
    if response.status_code != 200:
        print("❌ 无法获取用户列表")
        return
    
    users = response.json()
    
    # 测试每个用户的登录
    working_users = []
    broken_users = []
    
    test_passwords = {
        "admin": "admin123",
        "manager": "manager123", 
        "testuser": "test123",
        "debuguser": "debug123"
    }
    
    for user in users:
        username = user['username']
        password = test_passwords.get(username, "unknown")
        
        print(f"测试用户: {username}")
        print(f"  ID: {user['id']}")
        print(f"  密码哈希: {user['password']}")
        
        # 验证BCrypt
        try:
            bcrypt_valid = bcrypt.checkpw(password.encode('utf-8'), user['password'].encode('utf-8'))
            print(f"  BCrypt验证: {'✅' if bcrypt_valid else '❌'}")
        except Exception as e:
            print(f"  BCrypt验证异常: {e}")
            bcrypt_valid = False
        
        # 测试登录
        try:
            login_response = requests.post(
                f"{base_url}/bms/auth/login",
                json={"username": username, "password": password},
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            login_success = login_response.status_code == 200
            print(f"  Spring登录: {'✅' if login_success else '❌'}")
            
            if login_success:
                working_users.append(user)
            else:
                broken_users.append(user)
                try:
                    error_data = login_response.json()
                    print(f"  错误信息: {error_data.get('error', '未知')}")
                except:
                    pass
                    
        except Exception as e:
            print(f"  登录测试异常: {e}")
            broken_users.append(user)
        
        print()
    
    # 分析差异
    print("=== 分析差异 ===")
    print(f"工作的用户数量: {len(working_users)}")
    print(f"不工作的用户数量: {len(broken_users)}")
    
    if working_users and broken_users:
        print("\n工作用户的特征:")
        for user in working_users:
            print(f"  {user['username']}: ID={user['id']}, 哈希前缀={user['password'][:10]}")
        
        print("\n不工作用户的特征:")
        for user in broken_users:
            print(f"  {user['username']}: ID={user['id']}, 哈希前缀={user['password'][:10]}")
        
        # 检查是否有模式
        working_hash_prefixes = [u['password'][:4] for u in working_users]
        broken_hash_prefixes = [u['password'][:4] for u in broken_users]
        
        print(f"\n工作用户哈希前缀: {set(working_hash_prefixes)}")
        print(f"不工作用户哈希前缀: {set(broken_hash_prefixes)}")

def test_direct_password_encoding():
    """测试直接的密码编码"""
    print("=== 测试Spring Boot密码编码 ===")
    
    # 创建一个用户，然后立即用相同密码登录
    base_url = "http://localhost:8080"
    
    test_user = {
        "username": "springtest",
        "userCode": "SPRING001",
        "password": "spring123",
        "realName": "Spring测试",
        "oaName": "Spring测试OA",
        "email": "<EMAIL>",
        "status": 1
    }
    
    print("1. 创建用户")
    try:
        create_response = requests.post(
            f"{base_url}/bms/system/user",
            json=test_user,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if create_response.status_code == 200:
            created_user = create_response.json()
            print(f"  ✅ 用户创建成功")
            print(f"  生成的密码哈希: {created_user['password']}")
            
            # 立即测试登录
            print("\n2. 立即测试登录")
            login_response = requests.post(
                f"{base_url}/bms/auth/login",
                json={"username": "springtest", "password": "spring123"},
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if login_response.status_code == 200:
                print("  ✅ 登录成功 - Spring密码编码工作正常")
                return created_user['password']
            else:
                print(f"  ❌ 登录失败: {login_response.status_code}")
                try:
                    error_data = login_response.json()
                    print(f"  错误: {error_data.get('error')}")
                except:
                    pass
        else:
            print(f"  ❌ 用户创建失败: {create_response.status_code}")
            
    except Exception as e:
        print(f"  ❌ 异常: {e}")
    
    return None

def test_manual_password_update():
    """手动更新admin密码为新生成的格式"""
    print("=== 手动更新admin密码 ===")
    
    # 首先生成一个工作的密码哈希
    working_hash = test_direct_password_encoding()
    
    if not working_hash:
        print("❌ 无法获取工作的密码哈希")
        return
    
    print(f"\n使用工作的密码哈希格式: {working_hash[:20]}...")
    
    # 为admin123生成相同格式的哈希
    base_url = "http://localhost:8080"
    
    # 创建一个临时用户来获取admin123的正确哈希
    temp_user = {
        "username": "tempadmin",
        "userCode": "TEMP001", 
        "password": "admin123",
        "realName": "临时管理员",
        "oaName": "临时管理员",
        "email": "<EMAIL>",
        "status": 1
    }
    
    try:
        create_response = requests.post(
            f"{base_url}/bms/system/user",
            json=temp_user,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if create_response.status_code == 200:
            temp_created = create_response.json()
            admin123_hash = temp_created['password']
            print(f"获取到admin123的正确哈希: {admin123_hash}")
            
            # 现在更新真正的admin用户
            print("\n更新admin用户密码...")
            
            # 首先获取admin用户信息
            users_response = requests.get(f"{base_url}/bms/system/user/enabled")
            if users_response.status_code == 200:
                users = users_response.json()
                admin_user = next((u for u in users if u['username'] == 'admin'), None)
                
                if admin_user:
                    # 直接更新密码哈希（不通过API，而是模拟数据库更新）
                    print("建议执行以下SQL来修复admin密码:")
                    print(f"UPDATE sys_user SET password = '{admin123_hash}' WHERE username = 'admin';")
                    
                    # 或者尝试通过API更新
                    update_data = admin_user.copy()
                    update_data['password'] = 'admin123'  # 明文密码
                    
                    update_response = requests.put(
                        f"{base_url}/bms/system/user/{admin_user['id']}",
                        json=update_data,
                        headers={"Content-Type": "application/json"},
                        timeout=10
                    )
                    
                    if update_response.status_code == 200:
                        print("✅ Admin密码更新成功")
                        
                        # 测试登录
                        login_response = requests.post(
                            f"{base_url}/bms/auth/login",
                            json={"username": "admin", "password": "admin123"},
                            headers={"Content-Type": "application/json"},
                            timeout=10
                        )
                        
                        if login_response.status_code == 200:
                            print("🎉 Admin登录成功！")
                        else:
                            print(f"❌ Admin登录仍然失败: {login_response.status_code}")
                    else:
                        print(f"❌ 更新失败: {update_response.status_code}")
            
            # 清理临时用户
            print("\n清理临时用户...")
            # 这里可以删除临时用户，但为了简单起见先跳过
            
    except Exception as e:
        print(f"❌ 异常: {e}")

def main():
    """主函数"""
    print("最终认证测试")
    print("=" * 60)
    
    # 对比工作和不工作的用户
    compare_working_vs_broken_users()
    
    print("\n" + "=" * 60)
    
    # 测试手动密码更新
    test_manual_password_update()
    
    print("\n" + "=" * 60)
    print("测试完成")

if __name__ == "__main__":
    main()
