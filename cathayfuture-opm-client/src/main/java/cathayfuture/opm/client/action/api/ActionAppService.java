package cathayfuture.opm.client.action.api;

import cathayfuture.opm.client.action.dto.request.ActionReqDTO;
import cathayfuture.opm.client.action.dto.response.ActionRespDTO;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import java.util.List;

public interface ActionAppService {

    Integer addBatch(List<ActionReqDTO> List);

    List<ActionRespDTO> actionList(String module, Integer businessId);
    /**
     * 对比新旧学生数据的变更记录
     * @param studentNew 新数据
     * @param studentOld 老数据
     * @return 变更记录
     */
    List<ActionReqDTO> collectDiff(StudentReqDTO studentNew, StudentReqDTO studentOld);

    <T> List<ActionReqDTO> collectDiff(T newData, T oldData, String actionCode, String module);
}
