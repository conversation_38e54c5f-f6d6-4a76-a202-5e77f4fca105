package cathayfuture.opm.client.action.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/24/22
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(name = "ActionRespDTO", description = "操作历史返回体")
public class ActionRespDTO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 模块
     */
    @Schema(description = "模块")
    private String module;

    /**
     * 批号
     */
    @Schema(description = "批号")
    private String actionId;

    @Schema(description = "操作类型")
    private String actionCode;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    private Integer businessId;

    @Schema(description = "属性")
    private String property;

    @Schema(description = "属性名称")
    private String propertyName;

    /**
     * 新值
     */
    @Schema(description = "新值")
    private String newVal;

    /**
     * 旧值
     */
    @Schema(description = "旧值")
    private String oldVal;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updatePerson;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "更新时间格式化字符串")
    private String updateTimeStr;

}
