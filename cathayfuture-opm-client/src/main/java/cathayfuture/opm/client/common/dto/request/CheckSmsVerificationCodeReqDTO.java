package cathayfuture.opm.client.common.dto.request;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 20220824
 */
@Data
@Accessors(chain = true)
@ToString
public class CheckSmsVerificationCodeReqDTO {

    @NotBlank
    private String phoneNumber;
    @NotBlank
    private String smsVerificationCode;
}
