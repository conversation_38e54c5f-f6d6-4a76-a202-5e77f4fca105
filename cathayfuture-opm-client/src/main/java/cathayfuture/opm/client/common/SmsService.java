package cathayfuture.opm.client.common;

import cathayfuture.opm.client.common.dto.request.CheckSmsVerificationCodeReqDTO;
import cathayfuture.opm.client.common.dto.request.SendSmsReqDTO;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 20220829
 */
public interface SmsService {

    /**
     * 发送短信
     * @param sendSmsReqDTO
     * @return
     */
    Boolean sendSms(SendSmsReqDTO sendSmsReqDTO);

    /**
     * 校验短信验证码
     * @param checkSmsVerificationCodeReqDTO
     * @return
     */
    Boolean checkSmsVerificationCode(CheckSmsVerificationCodeReqDTO checkSmsVerificationCodeReqDTO);
}
