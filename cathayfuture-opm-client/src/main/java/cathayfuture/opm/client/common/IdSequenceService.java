package cathayfuture.opm.client.common;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2022/8/27
 */
public interface IdSequenceService {

    /**
     * 获取序列
     * @param prefix 前缀
     * @return 序列
     * */
    Integer incrAndGetSequence(String prefix);

    /**
     * 批量获取序列
     * @param prefix 前缀
     * @param increment 增量
     * @return 序列
     * */
    Integer incrAndGetSequence(String prefix, Integer increment);
}
