package cathayfuture.opm.client.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统用户DTO
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Schema(description = "系统用户信息")
public class SysUserDTO {

    @Schema(description = "用户ID")
    private Integer id;

    @Schema(description = "用户名", required = true)
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;

    @Schema(description = "用户编码")
    @Size(max = 50, message = "用户编码长度不能超过50个字符")
    private String userCode;

    @Schema(description = "密码", required = true)
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    @Schema(description = "真实姓名")
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    @Schema(description = "OA姓名")
    @Size(max = 50, message = "OA姓名长度不能超过50个字符")
    private String oaName;

    @Schema(description = "邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    @Schema(description = "手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "状态(0:禁用 1:启用)")
    private Integer status;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    @Schema(description = "登录失败次数")
    private Integer loginFailureCount;

    @Schema(description = "账户锁定时间")
    private LocalDateTime accountLockedTime;

    @Schema(description = "角色ID列表")
    private List<Integer> roleIds;

    @Schema(description = "角色名称列表")
    private List<String> roleNames;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建用户请求DTO
     */
    @Data
    @Schema(description = "创建用户请求")
    public static class CreateRequest {
        
        @Schema(description = "用户名", required = true)
        @NotBlank(message = "用户名不能为空")
        @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
        @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
        private String username;

        @Schema(description = "用户编码")
        @Size(max = 50, message = "用户编码长度不能超过50个字符")
        private String userCode;

        @Schema(description = "密码", required = true)
        @NotBlank(message = "密码不能为空")
        @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
        private String password;

        @Schema(description = "真实姓名")
        @Size(max = 50, message = "真实姓名长度不能超过50个字符")
        private String realName;

        @Schema(description = "OA姓名")
        @Size(max = 50, message = "OA姓名长度不能超过50个字符")
        private String oaName;

        @Schema(description = "邮箱")
        @Email(message = "邮箱格式不正确")
        @Size(max = 100, message = "邮箱长度不能超过100个字符")
        private String email;

        @Schema(description = "手机号")
        @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
        private String phone;

        @Schema(description = "头像URL")
        private String avatar;

        @Schema(description = "状态(0:禁用 1:启用)")
        private Integer status = 1;

        @Schema(description = "角色ID列表")
        private List<Integer> roleIds;
    }

    /**
     * 更新用户请求DTO
     */
    @Data
    @Schema(description = "更新用户请求")
    public static class UpdateRequest {
        
        @Schema(description = "用户ID", required = true)
        private Integer id;

        @Schema(description = "真实姓名")
        @Size(max = 50, message = "真实姓名长度不能超过50个字符")
        private String realName;

        @Schema(description = "OA姓名")
        @Size(max = 50, message = "OA姓名长度不能超过50个字符")
        private String oaName;

        @Schema(description = "邮箱")
        @Email(message = "邮箱格式不正确")
        @Size(max = 100, message = "邮箱长度不能超过100个字符")
        private String email;

        @Schema(description = "手机号")
        @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
        private String phone;

        @Schema(description = "头像URL")
        private String avatar;

        @Schema(description = "状态(0:禁用 1:启用)")
        private Integer status;

        @Schema(description = "角色ID列表")
        private List<Integer> roleIds;
    }

    /**
     * 修改密码请求DTO
     */
    @Data
    @Schema(description = "修改密码请求")
    public static class ChangePasswordRequest {
        
        @Schema(description = "用户ID", required = true)
        private Integer id;

        @Schema(description = "旧密码", required = true)
        @NotBlank(message = "旧密码不能为空")
        private String oldPassword;

        @Schema(description = "新密码", required = true)
        @NotBlank(message = "新密码不能为空")
        @Size(min = 6, max = 20, message = "新密码长度必须在6-20个字符之间")
        private String newPassword;
    }

    /**
     * 用户查询请求DTO
     */
    @Data
    @Schema(description = "用户查询请求")
    public static class QueryRequest {
        
        @Schema(description = "用户名（模糊查询）")
        private String username;

        @Schema(description = "用户编码（模糊查询）")
        private String userCode;

        @Schema(description = "真实姓名（模糊查询）")
        private String realName;

        @Schema(description = "OA姓名（模糊查询）")
        private String oaName;

        @Schema(description = "状态(0:禁用 1:启用)")
        private Integer status;

        @Schema(description = "页码", example = "1")
        private Integer pageNum = 1;

        @Schema(description = "每页大小", example = "10")
        private Integer pageSize = 10;
    }
}
