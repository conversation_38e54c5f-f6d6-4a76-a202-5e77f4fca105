package cathayfuture.opm.client.system.api;

import cathayfuture.opm.client.system.dto.SysUserDTO;

import java.util.List;

/**
 * 系统用户应用服务接口
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface SysUserAppService {

    /**
     * 创建用户
     *
     * @param request 创建用户请求
     * @return 用户信息
     */
    SysUserDTO createUser(SysUserDTO.CreateRequest request);

    /**
     * 更新用户
     *
     * @param request 更新用户请求
     * @return 用户信息
     */
    SysUserDTO updateUser(SysUserDTO.UpdateRequest request);

    /**
     * 根据ID删除用户
     *
     * @param id 用户ID
     * @return 删除结果
     */
    boolean deleteUser(Integer id);

    /**
     * 根据ID查询用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    SysUserDTO getUserById(Integer id);

    /**
     * 根据条件查询用户列表
     *
     * @param request 查询请求
     * @return 用户列表
     */
    List<SysUserDTO> getUserList(SysUserDTO.QueryRequest request);

    /**
     * 查询所有启用的用户
     *
     * @return 用户列表
     */
    List<SysUserDTO> getAllEnabledUsers();

    /**
     * 修改密码
     *
     * @param request 修改密码请求
     * @return 修改结果
     */
    boolean changePassword(SysUserDTO.ChangePasswordRequest request);

    /**
     * 重置密码
     *
     * @param id 用户ID
     * @param newPassword 新密码
     * @return 重置结果
     */
    boolean resetPassword(Integer id, String newPassword);

    /**
     * 启用/禁用用户
     *
     * @param id 用户ID
     * @param status 状态(0:禁用 1:启用)
     * @return 操作结果
     */
    boolean updateUserStatus(Integer id, Integer status);

    /**
     * 为用户分配角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 分配结果
     */
    boolean assignRoles(Integer userId, List<Integer> roleIds);

    /**
     * 解锁用户账户
     *
     * @param id 用户ID
     * @return 解锁结果
     */
    boolean unlockUser(Integer id);
}