package cathayfuture.opm.client.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 系统角色DTO
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Schema(description = "系统角色信息")
public class SysRoleDTO {

    @Schema(description = "角色ID")
    private Integer id;

    @Schema(description = "角色编码", required = true)
    @NotBlank(message = "角色编码不能为空")
    @Size(min = 2, max = 50, message = "角色编码长度必须在2-50个字符之间")
    @Pattern(regexp = "^[A-Z][A-Z0-9_]*$", message = "角色编码必须以大写字母开头，只能包含大写字母、数字和下划线")
    private String roleCode;

    @Schema(description = "角色名称", required = true)
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 2, max = 50, message = "角色名称长度必须在2-50个字符之间")
    private String roleName;

    @Schema(description = "角色描述")
    @Size(max = 255, message = "角色描述长度不能超过255个字符")
    private String description;

    @Schema(description = "状态(0:禁用 1:启用)")
    private Integer status;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建角色请求DTO
     */
    @Data
    @Schema(description = "创建角色请求")
    public static class CreateRequest {
        
        @Schema(description = "角色编码", required = true)
        @NotBlank(message = "角色编码不能为空")
        @Size(min = 2, max = 50, message = "角色编码长度必须在2-50个字符之间")
        @Pattern(regexp = "^[A-Z][A-Z0-9_]*$", message = "角色编码必须以大写字母开头，只能包含大写字母、数字和下划线")
        private String roleCode;

        @Schema(description = "角色名称", required = true)
        @NotBlank(message = "角色名称不能为空")
        @Size(min = 2, max = 50, message = "角色名称长度必须在2-50个字符之间")
        private String roleName;

        @Schema(description = "角色描述")
        @Size(max = 255, message = "角色描述长度不能超过255个字符")
        private String description;

        @Schema(description = "状态(0:禁用 1:启用)")
        private Integer status = 1;

        @Schema(description = "排序")
        private Integer sortOrder = 0;
    }

    /**
     * 更新角色请求DTO
     */
    @Data
    @Schema(description = "更新角色请求")
    public static class UpdateRequest {
        
        @Schema(description = "角色ID", required = true)
        private Integer id;

        @Schema(description = "角色名称", required = true)
        @NotBlank(message = "角色名称不能为空")
        @Size(min = 2, max = 50, message = "角色名称长度必须在2-50个字符之间")
        private String roleName;

        @Schema(description = "角色描述")
        @Size(max = 255, message = "角色描述长度不能超过255个字符")
        private String description;

        @Schema(description = "状态(0:禁用 1:启用)")
        private Integer status;

        @Schema(description = "排序")
        private Integer sortOrder;
    }

    /**
     * 角色查询请求DTO
     */
    @Data
    @Schema(description = "角色查询请求")
    public static class QueryRequest {
        
        @Schema(description = "角色编码（模糊查询）")
        private String roleCode;

        @Schema(description = "角色名称（模糊查询）")
        private String roleName;

        @Schema(description = "状态(0:禁用 1:启用)")
        private Integer status;

        @Schema(description = "页码", example = "1")
        private Integer pageNum = 1;

        @Schema(description = "每页大小", example = "10")
        private Integer pageSize = 10;
    }
}
