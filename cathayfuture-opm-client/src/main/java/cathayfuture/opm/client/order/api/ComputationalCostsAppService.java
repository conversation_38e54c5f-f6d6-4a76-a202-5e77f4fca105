package cathayfuture.opm.client.order.api;

import cathayfuture.opm.client.order.dto.response.ComputationalOrderAddRespDTO;

import java.util.List;
import java.util.Map;

public interface ComputationalCostsAppService {

    /**
     *
     */
    void batchAddOrderEntityList(List<ComputationalOrderAddRespDTO> respDTOList);

    /**
     * 杂费订单生成
     * @param studentId
     */
    void computeSundryCharges(Integer studentId);

    String generateCode(int type);

    List<String> generateCodes(int type, int increment);

    /**
     * 餐费订单生成
     * @param studentIdList
     * @param dateStr
     */
    void dinnerOperate(List<Integer> studentIdList,String dateStr);

    /**
     * 保育费订单生成
     * @param studentIdList
     * @param dateStr
     */
    void childCareOperate(List<Integer> studentIdList,String dateStr);

    /**
     * 保育费月结
     * @param studentIdList
     * @param dateStr
     */
    void monthlySettlementOperate(List<Integer> studentIdList, String dateStr);

    /**
     *  餐费月结
     * @param studentIdList
     * @param dateStr
     */
    void monthlySettlementForDinnerOperate(List<Integer> studentIdList, String dateStr);
}
