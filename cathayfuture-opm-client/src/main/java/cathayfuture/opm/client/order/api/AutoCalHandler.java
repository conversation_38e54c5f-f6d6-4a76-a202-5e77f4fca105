package cathayfuture.opm.client.order.api;

import cathayfuture.opm.client.order.dto.bo.ComputationalCostsBo;
import cathayfuture.opm.client.order.dto.bo.MonthlyStatementBo;
import cathayfuture.opm.client.order.dto.response.ComputationalOrderAddRespDTO;
import org.springframework.beans.factory.InitializingBean;

/**
 * <AUTHOR>
 */
public interface AutoCalHandler extends InitializingBean {
    /**
     * 生成新订单
     * @param bo
     * @return
     */
    ComputationalOrderAddRespDTO handler(ComputationalCostsBo bo);

    /**
     * 月结
     * @param bo
     * @return
     */
    MonthlyStatementBo monthlyStatement(ComputationalCostsBo bo);
}
