package cathayfuture.opm.client.order.dto.response;

import cathayfuture.opm.client.order.dto.bo.OrderChargeAmountRecordBo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/4/14 17:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ComputationalOrderAddRespDTO {
    /**
     * 上个月日历版本号
     */
    private String previousVerson;
    /**
     * 当月日历版本号
     */
    private String currentVersion;

    /**
     * 订单编码
     */
    private String code;

    /**
     * 订单类型（1保育费，2餐费，3代收费，4定金，5杂费）
     */
    private Integer type;

    /**
     * 订单状态（正常，已作废）
     */
    private Integer status;

    /**
     * 批次号
     */
    private String batchCode;

    /**
     * 作废原因
     */
    private String deleteReason;

    /**
     * 学生ID
     */
    private Integer studentId;

    /**
     * 学生编码
     */
    private String studentCode;

    /**
     * 学生名称
     */
    private String studentName;

    /**
     * 年级
     */
    private String grade;

    /**
     * 联系方式
     */
    private String contactInfo;

    /**
     * 费用标准
     */
    private BigDecimal chargingStandard;

    /**
     * 收费项目
     */
    private String chargeItem;

    /**
     * 上期预缴余额
     */
    private BigDecimal previousRemainingSum;

    /**
     * 上期出勤天数
     */
    private Integer previousAttendanceDays;

    /**
     * 上期收费比例
     */
    private String previousChargePercentage;

    /**
     * 上期应缴费用
     */
    private BigDecimal previousPayableCharge;

    /**
     * 上期余额
     */
    private BigDecimal previousActualSurplus;

    /**
     * 下期出勤天数
     */
    private Integer nextAttendanceDays;

    /**
     * 下期应收费用
     */
    private BigDecimal nextPayableCharge;

    /**
     * 收费金额（本期应缴费用，下期应收餐费）
     */
    private BigDecimal chargeAmount;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 支付时间
     */
    private Date paymentTime;

    /**
     * 支付渠道（线上，线下）
     */
    private Integer paymentChannel;

    /**
     * 支付方式（0线上-微信，1线下-微信，2支付宝，3现金，4银行卡，5余额, 6活动减免）
     */
    private String paymentMode;

    /**
     * 支付状态（未支付，已支付）
     */
    private Integer paymentStatus;

    /**
     * 明细备注
     */
    private String detailRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 付款方的账号ID
     */
    private Integer payer;

    /**
     * 发给微信的商户订单号
     */
    private String tradeNo;

    /**
     * 费用科目（1,保育费，2,餐费，3,代收费，4,定金，5,杂费）
     */
    private Integer subject;

    /**
     * 业态
     */
    private Integer businessForms;

    /**
     * 扩展字段
     */
    private String extension;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 线下收款人
     */
    private String collectionPerson;

    /**
     * 退款时间
     */
    private LocalDate refundTime;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 作废时间
     */
    private Date deleteTime;

    /**
     * 作废人员
     */
    private String deletePerson;

//    private List<RecordBo> recordBoList;

    private LocalDate targetDate;

    private OrderChargeAmountRecordBo orderChargeAmountRecordBo;
}
