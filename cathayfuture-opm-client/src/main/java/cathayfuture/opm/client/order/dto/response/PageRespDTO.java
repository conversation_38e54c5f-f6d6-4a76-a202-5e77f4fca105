package cathayfuture.opm.client.order.dto.response;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/25/22
 */
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
public class PageRespDTO<T> {

    private List<T> records = Collections.emptyList();

    private Long total = 0L;

    private Long pageSize = 0L;

    private Long pageNum = 0L;
}
