package cathayfuture.opm.client.order.api.query;

import cathayfuture.opm.client.order.dto.request.OrderReqDTO;
import cathayfuture.opm.client.order.dto.response.MobileOrderRespDTO;
import cathayfuture.opm.client.order.dto.response.OrderRespDTO;
import cathayfuture.opm.client.order.dto.response.PageRespDTO;

import java.util.List;

public interface OrderQueryAppService {
    PageRespDTO<OrderRespDTO> pageOrder(OrderReqDTO orderReqDTO, int pageNum, int pageSize);

    List<OrderRespDTO> listOrder(OrderReqDTO orderReqDTO);

    /**
     * 手机端分页查询订单
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageRespDTO<MobileOrderRespDTO> pageOrderForMobile(Integer status, Integer pageNum, Integer pageSize);

    /**
     * 根据ID查询订单详情
     * @param orderId
     * @return
     */
    MobileOrderRespDTO queryOrderById(Integer orderId);

    /**
     * 查询订单支付状态
     * @param orderId
     * @return
     */
    Integer queryOrderPaymentStatusById(Integer orderId);

    /**
     * 根据ID查询订单详情
     * @param orderId
     * @return
     */
    OrderRespDTO queryOrder(Integer orderId);

    /**
     * 查询当前用户有多少未支付的订单
     * @return
     */
    Integer notPayOrderCount();

    /**
     * 查询当前订单预支付状态
     * @param orderId
     * @return
     */
    Integer queryPrePayAndPayStatus(Integer orderId);

    List<OrderRespDTO> queryOrderTemplate(Integer orderType);


//    List<OrderRespDTO> queryOrderListByStudentIdAndTypeAndPaymentStatus(Integer studentId, Integer type,Integer paymentStatus);

    List<OrderRespDTO> queryOrderListByStudentIdListAndTypeAndPaymentStatus(List<Integer> studentIds, Integer type, List<Integer> paymentStatusList);
}
