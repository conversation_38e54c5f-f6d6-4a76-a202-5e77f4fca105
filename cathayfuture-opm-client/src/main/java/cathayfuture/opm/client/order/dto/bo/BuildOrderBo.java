package cathayfuture.opm.client.order.dto.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/4/20 10:48
 */
@Data
public class BuildOrderBo {
    private String batchId;
    private String orderCode;
    private BigDecimal currentPlan;
    private BigDecimal previousPlan;
    private BigDecimal previousActual;
    private BigDecimal currentRelief;
    private BigDecimal orderAmount;
    /**
     * 学生表eo
     */
    private StudentEntityBo studentBo;
    /**
     * 收费标准
     */
    private BigDecimal chargingStandard;
    /**
     * 上期出勤天数
     */
    private Integer previousAttendanceDays;

    /**
     * 下期应出勤天数（本月需上多少天学）
     */
    private Integer nextAttendanceDays;
    /**
     * 上期比例
     */
    private String percentage;
    /**
     * 上个月结余
     */
    private BigDecimal previousActualSurplus;
    /**
     * 上个月预缴余额
     */
    private BigDecimal PreviousRemainingSum;
    /**
     * 上上期结余
     */
    private BigDecimal twoMonthAgoSurplus;


    private LocalDate targetDate;

    /**
     * 本月长期病假
     */
    private Boolean currentLongTerm;
    /**
     * 当月寒暑假
     */
    private Integer holiday;
    /**
     * 当月教学日
     */
    private Integer weekday;
    /**
     * 日历版本号
     */
    private String version;
}
