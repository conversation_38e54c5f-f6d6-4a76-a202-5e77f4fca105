package cathayfuture.opm.client.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/24/22
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(name = "OrderRespDTO", description = "订单返回体")
public class OrderRespDTO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 订单编码
     */
    @Schema(description = "订单编码")
    private String code;

    /**
     * 订单类型（1保育费，2餐费，3代收费，4定金，5杂费）
     */
    @Schema(description = "订单类型（1保育费，2餐费，3代收费，4定金，5杂费）")
    private Integer type;

    @Schema(description = "订单类型名称（0保育费，1餐费，2代收费，3定金，4杂费）")
    private String typeName;

    /**
     * 订单状态（正常，已作废）
     */
    @Schema(description = "订单状态（正常，已作废）")
    private Integer status;

    @Schema(description = "订单状态名称（0正常，1已作废）")
    private String statusName;

    /**
     * 学生ID
     */
    @Schema(description = "学生ID")
    private Integer studentId;

    /**
     * 学生编码
     */
    @Schema(description = "学生编码")
    private String studentCode;

    /**
     * 学生名称
     */
    @Schema(description = "学生名称")
    private String studentName;

    /**
     * 年级
     */
    @Schema(description = "年级")
    private String grade;

    /**
     * 联系方式
     */
    @Schema(description = "联系方式")
    private String contactInfo;

    /**
     * 费用标准
     */
    @Schema(description = "费用标准")
    private BigDecimal chargingStandard;

    /**
     * 收费项目
     */
    @Schema(description = "收费项目")
    private String chargeItem;

    /**
     * 上期预缴余额
     */
    @Schema(description = "上期预缴余额")
    private BigDecimal previousRemainingSum;

    /**
     * 上期出勤天数
     */
    @Schema(description = "上期出勤天数")
    private Integer previousAttendanceDays;

    /**
     * 上期收费比例
     */
    @Schema(description = "上期收费比例")
    private String previousChargePercentage;

    /**
     * 上期应缴费用
     */
    @Schema(description = "上期应缴费用")
    private BigDecimal previousPayableCharge;

    /**
     * 上期余额
     */
    @Schema(description = "上期余额")
    private BigDecimal previousActualSurplus;

    /**
     * 下期出勤天数
     */
    @Schema(description = "下期出勤天数")
    private Integer nextAttendanceDays;

    /**
     * 下期应收费用
     */
    @Schema(description = "下期应收费用")
    private BigDecimal nextPayableCharge;

    /**
     * 收费金额（本期应缴费用，下期应收餐费）
     */
    @Schema(description = "收费金额（本期应缴费用，预收餐费金额）")
    private BigDecimal chargeAmount;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
    private Date paymentTime;

    /**
     * 支付渠道（线上，线下）
     */
    @Schema(description = "支付渠道（线上，线下）")
    private Integer paymentChannel;

    @Schema(description = "支付渠道名称（线上，线下）")
    private String paymentChannelName;

    /**
     * 支付方式（0线上-微信，1线下-微信，2支付宝，3现金，4银行卡，5余额，6活动减免）
     */
    @Schema(description = "支付方式（0线上-微信，1线下-微信，2支付宝，3现金，4银行卡，5余额，6活动减免）")
    private String paymentMode;

    @Schema(description = "支付方式名称（0线上-微信，1线下-微信，2支付宝，3现金，4银行卡，5余额，6活动减免）")
    private String paymentModeName;

    /**
     * 支付状态（未支付，已支付）
     */
    @Schema(description = "支付状态（未支付，已支付）")
    private Integer paymentStatus;

    @Schema(description = "支付状态名称（未支付，已支付）")
    private String paymentStatusName;

    /**
     * 明细备注
     */
    @Schema(description = "明细备注")
    private String detailRemark;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 付款方的账号ID
     */
    @Schema(description = "付款方的账号ID")
    private Integer payer;

    /**
     * 费用科目（1,保育费，2,餐费，3,代收费，4,定金，5,杂费）
     */
    @Schema(description = "费用科目（1,保育费，2,餐费，3,代收费，4,定金，5,杂费）")
    private Integer subject;

    /**
     * 业态
     */
    @Schema(description = "业态")
    private Integer businessForms;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createPerson;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updatePerson;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Schema(description = "逻辑删除标志")
    private Integer dr;

    @Schema(description = "创建时间格式化字符串")
    private String createTimeStr;

    @Schema(description = "支付时间格式化字符串")
    private String paymentTimeStr;

    @Schema(description = "更新时间格式化字符串")
    private String updateTimeStr;

    private String deleteReason;

    private List<PaymentDetailRespDTO> paymentDetails;

    private String birthday;

    @Schema(description = "线下收款人")
    private String collectionPerson;

    @Schema(description = "退款时间")
    private LocalDate refundTime;

    @Schema(description = "退款时间")
    private String refundTimeStr;

    @Schema(description = "退款原因")
    private String refundReason;

    @Schema(description = "作废时间")
    private Date deleteTime;

    @Schema(description = "作废时间格式化字符串")
    private String deleteTimeStr;

    @Schema(description = "作废人员")
    private String deletePerson;

    public String getPreviousActualSurplus() {
        return Optional.ofNullable(this.previousActualSurplus).map(BigDecimal::toPlainString).orElse(null);
    }

    public String getChargingStandard() {
        return Optional.ofNullable(this.chargingStandard).map(BigDecimal::toPlainString).orElse(null);
    }

    public String getPreviousRemainingSum() {
        return Optional.ofNullable(this.previousRemainingSum).map(BigDecimal::toPlainString).orElse(null);
    }


    public String getPreviousPayableCharge() {
        return Optional.ofNullable(this.previousPayableCharge).map(BigDecimal::toPlainString).orElse(null);
    }

    public String getNextPayableCharge() {
        return Optional.ofNullable(this.nextPayableCharge).map(BigDecimal::toPlainString).orElse(null);
    }

    public String getPreviousAttendanceDays() {
        return Optional.ofNullable(this.previousAttendanceDays).map(Object::toString).orElse(null);
    }

    public String getNextAttendanceDays() {
        return Optional.ofNullable(this.nextAttendanceDays).map(Object::toString).orElse(null);
    }


    public String getPreviousChargePercentage() {
        return Optional.ofNullable(this.previousChargePercentage).orElse("100");
    }

    public BigDecimal getChargingStandardValue() {
        return Optional.ofNullable(this.chargingStandard).orElse(BigDecimal.ZERO);
    }

    public BigDecimal getPreviousRemainingSumValue() {
        return Optional.ofNullable(this.previousRemainingSum).orElse(BigDecimal.ZERO);
    }


    public BigDecimal getPreviousPayableChargeValue() {
        return Optional.ofNullable(this.previousPayableCharge).orElse(BigDecimal.ZERO);
    }

    public BigDecimal getNextPayableChargeValue() {
        return Optional.ofNullable(this.nextPayableCharge).orElse(BigDecimal.ZERO);
    }

    public Integer getPreviousAttendanceDaysValue() {
        return Optional.ofNullable(this.previousAttendanceDays).orElse(0);
    }

    public Integer getNextAttendanceDaysValue() {
        return Optional.ofNullable(this.nextAttendanceDays).orElse(0);
    }


    public BigDecimal getChargeAmount() {
        return Optional.ofNullable(this.chargeAmount).orElse(BigDecimal.ZERO);
    }

    public BigDecimal getPayAmount(){
        return Optional.ofNullable(this.payAmount).orElse(BigDecimal.ZERO);
    }

}
