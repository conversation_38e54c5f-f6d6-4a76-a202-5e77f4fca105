package cathayfuture.opm.client.order.dto.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/4/17 11:25
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class ComputationalCostsBo {
    /**
     * 上个月日历版本号
     */
    private String previousVerson;
    /**
     * 当月日历版本号
     */
    private String currentVersion;

    private String orderCode;

    private String batchId;

    private LocalDate targetDate;
    /**
     * 当月入园标记
     */
    private Boolean currentRegisterFlg;

    /**
     * 学生表eo
     */
    private StudentEntityBo studentBo;
    /**
     * 当月工作日期取yyyy-MM-dd中的dd列表
     */
    private List<Integer> dayOfMonthList;

    private List<Integer> dayOfMonthHolidayList;
    /**
     * 上个月工作日取yyyy-MM-dd中的dd列表
    */
    private List<Integer> lastMonthDayOfMonthList;
    /**
     * 上个月寒暑假取yyyy-MM-dd中的dd列表
     */
    private List<Integer> lastMonthDaydayOfMonthHolidayList;


    /**
     * 本月寒暑假总天数
     */

    private BigDecimal currentAllHolidays;

    /**
     * 上月出勤天数(餐费用)
     */
    private Integer attendanceDay;
    /**
     * 上个月订单
     */
    private BigDecimal previousChargeAmount;
    /**
     * 当月订单
     */
    private BigDecimal currentChargeAmount;

    /**
     * 当月减免费用
     * @return
     */
    private BigDecimal currentRelief;
    /**
     * 上个月减免
     */
    private BigDecimal previousRelief;
    /**
     * 收费标准
     */
    private BigDecimal chargingStandard;
    /**
     * 本月长期病假
     */
    private Boolean currentLongTerm;
    /**
     * 上个月长期病假
     */
    private Boolean previousLongTerm;
    /**
     * 上个月寒暑假出勤
     */
    private Long holidayAttendanceDays;
    /**
     * 上个月开园日出勤
     */
    private Long weekdayAttendanceDays;


    private String previousPercentage;

    /**
     * 当前月总开园天数（排除周末）
     */
    private Long currentAllWeekdays;
    /**
     * 上个月总开园天数（排除周末）
     */
    private Long lastMonthAllWeekdays;
    /**
     * 上个月总寒暑假天数
     */
    private Long lastMonthAllHolidays;
    /**
     * 参与计算每日费用的当月天数
     */
    private Long withoutWeekend;
    /**
     * 参与计算每日费用的上月天数
     */
    private Long lastMonthCountWithoutWeekend;
    /**
     * 上期预缴余额
     */
    private BigDecimal previousRemainingSum;
    /**
     * 上个月实际
     */
    private BigDecimal previousActual;
    /**
     * 上上期余额
     */
    private BigDecimal twoMonthAgoSurplus;

    private BigDecimal previousSurplus;
    /**
     * 学生第一次产生考勤日期
     */
    private LocalDate firstDay;

    /**
     * 产生过考勤
     * @return
     */
    public Boolean hasAttendance(){
        LocalDate previousLastDayOfMonth = targetDate
                .minusMonths(1)
                .with(TemporalAdjusters.lastDayOfMonth());
        //有考勤的第一条大于previousLastDayOfMontht，证明该学生从未出勤
        if(Objects.isNull(firstDay) || firstDay.isAfter(previousLastDayOfMonth)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public BigDecimal getChargingStandard(){
        return Optional.ofNullable(chargingStandard).orElse(BigDecimal.ZERO);
    }

    public BigDecimal getWeekdayAttendanceDays(){
        return new BigDecimal(String.valueOf(weekdayAttendanceDays));
    }
    public BigDecimal getHolidayAttendanceDays(){
        return new BigDecimal(String.valueOf(holidayAttendanceDays));
    }


    public BigDecimal getCurrentRelief(){
        return Optional.ofNullable(currentRelief).orElse(BigDecimal.ZERO);
    }


    public BigDecimal getPreviousChargeAmount(){
        return Optional.ofNullable(previousChargeAmount).orElse(BigDecimal.ZERO);
    }


    /**
     * 获取上个月寒暑假天数
     * @return
     */
    public BigDecimal getLastMonthAllHolidays(){
        //统计新入学的学生大于入园日期的天数
        LocalDate lastMonthFirstDay = targetDate
                .minusMonths(1)
                .with(TemporalAdjusters.firstDayOfMonth());
        LocalDate thisMonthFirstDay = targetDate
                .with(TemporalAdjusters.firstDayOfMonth());
        if(currentRegisterFlg && lastMonthFirstDay.getMonthValue() == studentBo.getAdmissionDate().getMonthValue()
                && lastMonthFirstDay.getYear() ==  studentBo.getAdmissionDate().getYear()){
            //获取数组中比入院日期大的数的个数
            long count = lastMonthDaydayOfMonthHolidayList.stream().filter(x -> x - studentBo.getAdmissionDate().getDayOfMonth() >= 0).count();
            return new BigDecimal(String.valueOf(count));
        }
        if(currentRegisterFlg && thisMonthFirstDay.getMonthValue() == studentBo.getAdmissionDate().getMonthValue()){
            return BigDecimal.ZERO;
        }
        return  new BigDecimal(String.valueOf(lastMonthAllHolidays));
    }


    /**
     * 获取上个月开园日天数
     * @return
     */
    public BigDecimal getLastMonthAllWeekdays(){
        //统计新入学的学生大于入园日期的天数
        LocalDate lastMonthFirstDay = targetDate
                .minusMonths(1)
                .with(TemporalAdjusters.firstDayOfMonth());

        LocalDate thisMonthFirstDay = targetDate
                .with(TemporalAdjusters.firstDayOfMonth());

        if( currentRegisterFlg && lastMonthFirstDay.getMonthValue() == studentBo.getAdmissionDate().getMonthValue()
                && lastMonthFirstDay.getYear() ==  studentBo.getAdmissionDate().getYear()){

            //获取数组中比入院日期大的数的个数
            long count = lastMonthDayOfMonthList.stream().filter(x -> x - studentBo.getAdmissionDate().getDayOfMonth() >= 0).count();
            return new BigDecimal(String.valueOf(count));
        }
        if(currentRegisterFlg && thisMonthFirstDay.getMonthValue() == studentBo.getAdmissionDate().getMonthValue()){
            return BigDecimal.ZERO;
        }
        if(Objects.isNull(lastMonthAllWeekdays)){
            return BigDecimal.ZERO;
        }else{
            return new BigDecimal(String.valueOf(lastMonthAllWeekdays));
        }
    }

    /**
     * 获取上个月寒暑假天数，上月实际考勤金额计算专用
     * @return
     */
    public BigDecimal getLastMonthAllHolidaysForPreviousActual(){
        //统计新入学的学生大于入园日期的天数
        LocalDate previousLastDayOfMonth = targetDate
                .minusMonths(1)
                .with(TemporalAdjusters.firstDayOfMonth());

        if(!hasAttendance()){
            return BigDecimal.ZERO;
        }

        if(previousLastDayOfMonth.getMonthValue() == firstDay.getMonthValue() && previousLastDayOfMonth.getYear() ==  firstDay.getYear()){
            //获取数组中比入院日期大的数的个数
            long count = lastMonthDaydayOfMonthHolidayList.stream().filter(x -> x - firstDay.getDayOfMonth() >= 0).count();
            return new BigDecimal(String.valueOf(count));
        }
        return  new BigDecimal(String.valueOf(lastMonthAllHolidays));
    }

    /**
     * 获取上个月开园日天数,上月实际考勤金额计算专用
     * @return
     */
    public BigDecimal getLastMonthAllWeekdaysForPreviousActual(){
        //统计新入学的学生大于入园日期的天数
        LocalDate previousLastDayOfMonth = targetDate
                .minusMonths(1)
                .with(TemporalAdjusters.firstDayOfMonth());

        //有考勤的第一条大于target，证明该学生从未出勤
        if(!hasAttendance()){
            return BigDecimal.ZERO;
        }

        if( previousLastDayOfMonth.getMonthValue() == firstDay.getMonthValue() && previousLastDayOfMonth.getYear() ==  firstDay.getYear()){
            //获取数组中比入院日期大的数的个数
            long count = lastMonthDayOfMonthList.stream().filter(x -> x - firstDay.getDayOfMonth() >= 0).count();
            return new BigDecimal(String.valueOf(count));
        }

        if(Objects.isNull(lastMonthAllWeekdays)){
            return BigDecimal.ZERO;
        }else{
            return new BigDecimal(String.valueOf(lastMonthAllWeekdays));
        }
    }





    /**
     * 获取当月开园日天数
     * @return
     */
    public BigDecimal getCurrentAllWeekdays(){
        //长期病假且未出勤过，下期应出勤天数为0
        if(getCurrentLongTerm() && !hasAttendance()){
            return BigDecimal.ZERO;
        }
        //统计新入学的学生大于入园日期的天数
        LocalDate thisMonthFirstDay = targetDate
                .with(TemporalAdjusters.firstDayOfMonth());

        if(currentRegisterFlg && thisMonthFirstDay.getMonthValue() == studentBo.getAdmissionDate().getMonthValue()){
            //获取数组中比入院日期大的数的个数
            long count = dayOfMonthList.stream()
                    .filter(x -> x - studentBo.getAdmissionDate().getDayOfMonth() >= 0)
                    .count();
            return new BigDecimal(String.valueOf(count));
        }
        if(Objects.isNull(currentAllWeekdays)){
            return BigDecimal.ZERO;
        }else{
            return new BigDecimal(String.valueOf(currentAllWeekdays));
        }
    }

    public BigDecimal getCurrentAllHolidays(){
        if(getCurrentLongTerm() && !hasAttendance()){
            return BigDecimal.ZERO;
        }
        //统计新入学的学生大于入园日期的天数
        LocalDate thisMonthFirstDay = targetDate
                .with(TemporalAdjusters.firstDayOfMonth());

        if(currentRegisterFlg && thisMonthFirstDay.getMonthValue() == studentBo.getAdmissionDate().getMonthValue()){
            //获取数组中比入院日期大的数的个数
            long count = dayOfMonthHolidayList.stream()
                    .filter(x -> x - studentBo.getAdmissionDate().getDayOfMonth() >= 0)
                    .count();
            return new BigDecimal(String.valueOf(count));
        }
        if(Objects.isNull(currentAllHolidays)){
            return BigDecimal.ZERO;
        }else{
            return new BigDecimal(String.valueOf(currentAllHolidays));
        }
    }
}
