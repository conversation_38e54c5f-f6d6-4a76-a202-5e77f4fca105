package cathayfuture.opm.client.order.dto.bo;

import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/4/17 14:18
 */
@Data
public class StudentEntityBo {

    private Integer id;
    private Integer studentType;

    private String studentName;

    private String studentNo;

    private String studentClass;

    private String ethnicGroup;

    private String idNumber;

    private Integer gender;

    private LocalDate birthday;
    private String nationality;

    private String provinceCode;
    private String provinceName;
    private String cityCode;
    private String cityName;
    private String regionCode;
    private String regionName;
    private String homeAddress;

    private String contactName;
    private String contactPhoneNumber;
    private String relation;
    private String fatherName;
    private String fatherPhoneNumber;
    private String motherName;
    private String motherPhoneNumber;

    private LocalDate admissionDate;

    private Integer businessUnit;

    private Integer registerStatus;

    private LocalDate firstDay;


}
