package cathayfuture.opm.client.order.dto.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 子类订单参数汇总
 * @date 2023/4/17 16:49
 */
@Data
public class ComputationalCostsChildOrderBo {

    private StudentEntityBo studentEntityBo;

    private String code;

    private String batchId;
    /**
     * 费用标准
     */
    private BigDecimal expenseStandard;
}
