package cathayfuture.opm.client.order.dto.bo;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 月结
 * @date 2023/5/23 14:32
 */
@Data
@NoArgsConstructor
public class MonthlyStatementBo {

        /**
         * 月份
         */

        private LocalDate month;
        /**
         * 订单类型（0保育费，1餐费，2定金，3代收费，4杂费）
         */

        private Integer orderType;
        /**
         * 学生id
         */

        private Integer studentId;
        /**
         * 学生状态（0预在园，1在园，2退园）
         */

        private Integer studentStatus;
        /**
         * 出勤天数
         */

        private Integer attendanceDay;
        /**
         * 本月结余
         */

        private BigDecimal currentActualSurplus;
        /**
         * 上月结余
         */

        private BigDecimal previousActualSurplus;
        /**
         * 实际应缴费用（根据考勤实际应缴费）
         */

        private BigDecimal actualPayableCharge;
        /**
         * 订单金额
         */

        private BigDecimal chargeAmount;
        /**
         * 寒暑假
         */
        private Integer holidays;
        /**
         * 开学日
         */
        private Integer weekdays;
        /**
         * 长期病假标记
         */
        private Integer longTermFlg;
        /**
         * 日历版本号
         */
        private String calendarVersion;
        /**
         * 本月保育费标准
         */
        private BigDecimal chargingStandard;
        /**
         * 收费比例
         */
        private String percentage;
        /**
         * month月份减免
         */
        private BigDecimal rateRelief;

        private String batchCode;

        public static MonthlyStatementBo buildByComputationalCostsBo(ComputationalCostsBo bo){
                Integer longTerm = 0;
                if(bo.getPreviousLongTerm()){
                        longTerm = 1;
                }
                MonthlyStatementBo result = new MonthlyStatementBo();
                result.setMonth(bo.getTargetDate().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()));
                result.setStudentId(bo.getStudentBo().getId());
                result.setStudentStatus(bo.getStudentBo().getRegisterStatus());

                result.setPreviousActualSurplus(Optional.ofNullable(bo.getTwoMonthAgoSurplus()).orElse(BigDecimal.ZERO));
                result.setChargeAmount(Optional.ofNullable(bo.getPreviousChargeAmount()).orElse(BigDecimal.ZERO));
                result.setLongTermFlg(longTerm);
                result.setChargingStandard(bo.getChargingStandard());
                result.setPercentage(bo.getPreviousPercentage());
                result.setRateRelief(bo.getPreviousRelief());
                return result;
        }
}
