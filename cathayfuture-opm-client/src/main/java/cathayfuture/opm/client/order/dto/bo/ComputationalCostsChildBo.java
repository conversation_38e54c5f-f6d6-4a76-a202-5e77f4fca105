package cathayfuture.opm.client.order.dto.bo;

import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 子类bo
 * @date 2023/4/17 15:34
 */
@Data
public class ComputationalCostsChildBo {
    /**
     * 学生表id，可不传
     */
    private Integer studentId;

    private String code;

    private String batchId;
    /**
     * 寒暑假天数
     */
    private long holidayDays;
    /**
     * 寒暑假天数
     */
    private long weekdays;

    private StudentEntityBo studentEntityBo;

    /**
     * 费用标准
     */
    private Map<Integer, BigDecimal> expenseStandardMap;
    /**
     * 上个月月初
     */
    private LocalDate lastMonthFirstDay;
    /**
     * 上个月月末
     */
    private LocalDate lastMonthLastDay;
    /**
     * 上个月长期病假
     */
    private Map<Integer, Boolean> lastMonthlongTermMap;
    /**
     * 本月长期病假
     */
    private Map<Integer, Boolean> longTermMap;
    /**
     * 上月订单
     */
    private Map<Integer, BigDecimal> lastMonthOrderMap;
    /**
     * 本月订单
     */
    private Map<Integer, BigDecimal> orderMap;
    /**
     * 统计本月工作日
     */
    private long weekDays;
    /**
     * 统计本月工作日去除闭园
     */
    private long workDaysWithoutColsed;
    /**
     * 统计上月工作日
     */
    private long lastWeekDays;
    /**
     * 统计上月工作日去除闭园
     */
    private long lastWorkDaysWithoutColse;
    /**
     * 上个月寒暑假天数
     */
    private long lastWinterVacationAndSummerVacationDaysWithoutWeekDay;
    /**
     * 上个月假期map
     */
    private Map<Integer, Long> holidayMap;
    /**
     * 上个月工作日map
     */
    private Map<Integer, Long> normalMap;
    /**
     * 当月工作日期取yyyy-MM-dd中的dd列表
     */
    private List<Integer> dayOfMonthList;

    private ComputationalCostsChildOrderBo orderBo;

}
