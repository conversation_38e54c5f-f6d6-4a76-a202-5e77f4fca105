package cathayfuture.opm.client.order.dto.response;

import icu.mhb.mybatisplus.plugln.tookit.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @date 20220826
 */
@Data
public class MobileOrderRespDTO {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 订单编号
     */
    private String code;

    /**
     * 订单类型（1保育费，2餐费，3代收费，4定金，5杂费）
     */
    private Integer type;

    /**
     * 学生ID
     */
    private Integer studentId;

    /**
     * 学号
     */
    private String studentCode;

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 学生生日
     */
    private String birthday;

    /**
     * 联系方式
     */
    private String contactInfo;

    /**
     * 收费项目
     */
    private String chargeItem;

    /**
     * 上期余额
     */
    private BigDecimal previousActualSurplus;
    /**
     * 上期预缴余额
     */
    private BigDecimal previousRemainingSum;

    /**
     * 上期应缴费用
     */
    private BigDecimal previousPayableCharge;

    /**
     * 上期出勤天数
     */
    private Integer previousAttendanceDays;

    /**
     * 下期出勤天数
     */
    private Integer nextAttendanceDays;

    /**
     * 收费金额（本期应缴费用，下期应收餐费）
     */
    private BigDecimal chargeAmount;

    /**
     * 费用标准
     */
    private BigDecimal chargingStandard;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单类型文字
     */
    private String orderType;

    /**
     * 支付状态文字
     */
    private String paymentStatus;
    /**
     * 支付状态文字
     */
    private String paymentTime;

    private Boolean showPayButton;
    private Boolean showReplaceButton;
    /**
     * 本月预计花费金额
     */
    private BigDecimal nextPayableCharge;
    /**
     * 支付方式（0线上-微信，1线下-微信，2支付宝，3现金，4银行卡，5余额，6活动减免）
     */
    private String paymentMode;

    public String getPreviousAttendanceDays() {
        return Optional.ofNullable(previousAttendanceDays).map(d -> String.valueOf(d)).orElse(StringUtils.EMPTY);
    }

    /**
     * 本期实缴
     * @return
     */
    public String getActualPayAmount() {
        return Optional.ofNullable(chargeAmount).map(BigDecimal::toPlainString).orElse(StringUtils.EMPTY);
    }

    /**
     * 上期余额
     * @return
     */
    public String getPreviousRemainingSum() {
        return Optional.ofNullable(previousActualSurplus).map(BigDecimal::toPlainString).orElse(StringUtils.EMPTY);
    }

    /**
     * 本期应缴
     * @return
     */
    public String getChargeAmount() {
        /*if (Objects.equals(type, 0)) {
            return Optional.ofNullable(chargingStandard).map(BigDecimal::toPlainString).orElse(StringUtils.EMPTY);
        } else if (Objects.equals(type, 1)) {
            return Optional.ofNullable(chargingStandard).map(d -> String.valueOf(d.multiply(Optional.ofNullable(nextAttendanceDays).map(BigDecimal::new).orElse(BigDecimal.ONE)))).orElse(StringUtils.EMPTY);
        } else {
            return Optional.ofNullable(chargeAmount).map(BigDecimal::toPlainString).orElse(StringUtils.EMPTY);
        }*/
        //20230605 需要显示本月预计花费金额
        if(!Lists.newArrayList(0,1).contains(type)){
            return Optional.ofNullable(chargeAmount)
                    .map(BigDecimal::toPlainString)
                    .orElse(StringUtils.EMPTY);
        }else {
            return Optional.ofNullable(nextPayableCharge)
                    .map(BigDecimal::toPlainString)
                    .orElse(StringUtils.EMPTY);
        }

    }
}
