package cathayfuture.opm.client.order.dto.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/6/9 16:50
 */
@Data
public class OrderOperationRecordEntityBo {
    /**
     * 订单类别
     */
    private Integer type;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 批次号
     */
    private String batchCode;

    private Integer dr;
    /**
     * 操作类型
     */
    private Integer operationType;
}
