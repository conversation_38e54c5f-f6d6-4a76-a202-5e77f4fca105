package cathayfuture.opm.client.order.dto.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单金额记录bo
 * @date 2023/6/1 17:05
 */
@Data
@AllArgsConstructor
@Builder
public class OrderChargeAmountRecordBo {

    /**
     * 保育费标准
     */
    @TableField("charging_standard")
    private BigDecimal chargingStandard;
    /**
     * 月份
     */
    @TableField("month")
    private LocalDate month;
    /**
     * 当月去除周末和法定节假日的总天数
     */
    @TableField("all_days")
    private Integer allDays;
    /**
     * 当月工作日
     */
    @TableField("weekday")
    private Integer weekday;
    /**
     * 当月寒暑假
     */
    @TableField("holiday")
    private Integer holiday;
    /**
     * 日历版本号
     */
    @TableField("version")
    private String version;
    /**
     * 订单编码
     */
    @TableField("order_code")
    private String orderCode;
    /**
     * 订单类型（0保育费，1餐费，2定金，3代收费，4杂费）
     */
    @TableField("order_type")
    private Integer orderType;
    /**
     * 学生id
     */
    @TableField("student_id")
    private Integer studentId;
    /**
     * 订单金额
     */
    @TableField("charge_amount")
    private BigDecimal chargeAmount;


    public static OrderChargeAmountRecordBo buildBo(Integer orderType,BuildOrderBo buildOrderBo){
        return OrderChargeAmountRecordBo.builder()
                .chargeAmount(buildOrderBo.getOrderAmount())
                .chargingStandard(buildOrderBo.getChargingStandard())
                .holiday(buildOrderBo.getHoliday().intValue())
                .weekday(buildOrderBo.getWeekday().intValue())
                .allDays(buildOrderBo.getNextAttendanceDays())
                .version(buildOrderBo.getVersion())
                .orderCode(buildOrderBo.getOrderCode())
                .orderType(orderType)
                .month(buildOrderBo.getTargetDate())
                .studentId(buildOrderBo.getStudentBo().getId())
                .build();
    }
}
