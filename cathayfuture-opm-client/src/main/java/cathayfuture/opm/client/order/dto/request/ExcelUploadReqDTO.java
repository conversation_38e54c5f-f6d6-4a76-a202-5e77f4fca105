package cathayfuture.opm.client.order.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/30/22
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ExcelUploadReqDTO {

    /**
     * 文件地址
     */
    @Schema(name = "uri")
    private String uri;
    /**
     * 文件名
     */
    @Schema(name = "fileName")
    private String fileName;

    @Schema(name = "importFlag")
    private Integer importFlag;

}
