package cathayfuture.opm.client.order.dto.response;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 20220824
 */
@Data
@ToString
public class PreOrderRespDTO {

    private BigDecimal actualPayAmount;

    public String getActualPayAmount() {
        return String.valueOf(Optional.ofNullable(actualPayAmount).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
    }
}
