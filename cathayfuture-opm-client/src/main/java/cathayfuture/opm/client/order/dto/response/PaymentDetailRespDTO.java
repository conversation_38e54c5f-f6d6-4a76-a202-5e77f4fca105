package cathayfuture.opm.client.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/26/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(name = "PaymentDetailRespDTO", description = "订单支付信息返回体")
public class PaymentDetailRespDTO {
    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
    private Integer orderId;

    /**
     * 支付类型
     */
    @Schema(description = "支付类型")
    private Integer paymentType;

    @Schema(description = "支付类型名称")
    private String paymentTypeName;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式")
    private Integer paymentMode;

    @Schema(description = "支付方式名称")
    private String paymentModeName;

    /**
     * pos机号
     */
    @Schema(description = "pos机号")
    private String posNo;

    /**
     * 支付状态
     */
    @Schema(description = "支付状态")
    private Integer paymentStatus;

    @Schema(description = "支付状态名称")
    private String paymentStatusName;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    /**
     * 支付交易流水号
     */
    @Schema(description = "支付交易流水号")
    private String transactionId;

    /**
     * 支付人ID
     */
    @Schema(description = "支付人ID")
    private Integer payerId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
