package cathayfuture.opm.client.order.dto.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/29/22
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PaymentDetailReqDTO {

    private Integer id;

    private Integer orderId;

    private Integer paymentMode;

    private String paymentModeName;

    private Integer paymentType;

    private Integer paymentStatus;

    private BigDecimal payAmount;

    private String pos;

    private Date createTime;

    private Date updateTime;
}
