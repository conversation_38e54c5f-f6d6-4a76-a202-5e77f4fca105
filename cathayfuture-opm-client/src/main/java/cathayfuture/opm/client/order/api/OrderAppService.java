package cathayfuture.opm.client.order.api;

import cathayfuture.opm.client.order.dto.request.*;
import cathayfuture.opm.client.order.dto.response.EnrollRespDTO;
import cathayfuture.opm.client.order.dto.response.PreOrderRespDTO;

import java.math.BigDecimal;
import java.util.List;

public interface OrderAppService {

    EnrollRespDTO enroll(EnrollReqDTO enrollReqDTO);

    /**
     * 发起预支付
     * @param orderId
     * @return
     */
    String prePay(Integer orderId);

    /**
     * 微信支付完成
     * @param tradeNo
     * @param payStatus
     * @param payAmount
     * @param tripartiteOrderNo
     */
    void payComplete(String tradeNo, String payStatus, BigDecimal payAmount, String tripartiteOrderNo);

    Boolean deleteOrder(Integer orderId, String deleteReason);

    String generateCode(int type);

    List<String> generateCodes(int type, int increment);

    Boolean changePayer(OrderChangePayerReqDTO changePayerReqDTO);

    Boolean payOffline(OrderReqDTO orderReqDTO);

    Boolean importOrders(List<OrderReqDTO> orderReqDTOList);

    /**
     * 创建报名（定金）订单
     * @param createFrontMoneyOrderReqDTO
     * @return  订单Id
     */
    Integer createFrontMoneyOrder(CreateFrontMoneyOrderReqDTO createFrontMoneyOrderReqDTO);

    /**
     * 预下单（目前只有小程序端的定金订单）
     * @param preOrderReqDTO
     * @return
     */
    PreOrderRespDTO preOrder(PreOrderReqDTO preOrderReqDTO);

    /**
     * 根据订单id进行退款
     * @param orderId 订单id
     * @param refundTime 退款时间
     * @param refundReason 退款原因
     * @return
     */
    Boolean refund(Integer orderId, String refundTime, String refundReason);
}
