package cathayfuture.opm.client.order.api;

import cathayfuture.opm.client.order.dto.request.PaymentDetailReqDTO;
import cathayfuture.opm.client.order.dto.response.PaymentDetailRespDTO;

import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/24/22
 */
public interface PaymentDetailAppService {

    PaymentDetailRespDTO add(PaymentDetailReqDTO paymentDetailReqDTO);

    List<PaymentDetailRespDTO> batchAdd(List<PaymentDetailReqDTO> paymentDetailReqDTOList);

    /**
     * 订单退款
     * @param orderId 订单Id
     * @return 退款结果
     */
    Boolean refund(Integer orderId);
}
