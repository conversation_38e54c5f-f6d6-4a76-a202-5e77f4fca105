package cathayfuture.opm.client.order.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/24/22
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(name = "OrderReqDTO", description = "订单请求体")
public class OrderReqDTO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 订单编码
     */
    @Schema(description = "订单编码")
    private String code;

    /**
     * 订单类型（1保育费，2餐费，3代收费，4定金，5杂费）
     */
    @NotNull(message = "订单类型不能为空")
    @Digits(integer = 1, fraction = 0, message = "订单类型为整数")
    @Min(value = 0L, message = "订单类型不能小于1")
    @Max(value = 4L, message = "订单类型不能大于5")
    @Schema(description = "订单类型（0保育费，1餐费，2代收费，3定金，4杂费）")
    private Integer type;

    /**
     * 订单状态（正常，已作废）
     */
    @NotNull(message = "订单状态不能为空")
    @Digits(integer = 1, fraction = 0, message = "订单状态为整数")
    @Min(value = 0L, message = "订单状态不能小于1")
    @Max(value = 1L, message = "订单状态不能大于2")
    @Schema(description = "订单状态（0正常，1已作废）")
    private Integer status;

    private Integer studentId;
    /**
     * 学生编码
     */
    @Schema(description = "学生编码")
    private String studentCode;

    /**
     * 学生生日
     */
    @Schema(description = "学生生日")
    private String birthday;

    /**
     * 学生姓名
     */
    @Schema(description = "学生姓名")
    private String studentName;

    private String grade;

    /**
     * 联系方式
     */
    @Schema(description = "联系方式")
    private String contactInfo;

    private BigDecimal chargingStandard;

    /**
     * 收费项目
     */
    @Schema(description = "收费项目")
    private String chargeItem;

    /**
     * 上期预缴余额
     */
    @Schema(description = "上期预缴余额")
    private BigDecimal previousRemainingSum;

    /**
     * 上期出勤天数
     */
    @Schema(description = "上期出勤天数")
    private Integer previousAttendanceDays;

    /**
     * 上期收费比例
     */
    @Schema(description = "上期收费比例")
    private String previousChargePercentage;

    /**
     * 上期应缴费用
     */
    @Schema(description = "上期应缴费用")
    private BigDecimal previousPayableCharge;

    /**
     * 上期余额
     */
    @Schema(description = "上期余额")
    private BigDecimal previousActualSurplus;

    /**
     * 下期出勤天数
     */
    @Schema(description = "下期出勤天数")
    private Integer nextAttendanceDays;

    /**
     * 下期应收费用
     */
    @Schema(description = "下期应收费用")
    private BigDecimal nextPayableCharge;

    /**
     * 收费金额（本期应缴费用，下期应收餐费）
     */
    @Schema(description = "收费金额（本期应缴费用，下期应收餐费）")
    private BigDecimal chargeAmount;

    /**
     * 支付渠道（线上，线下）
     */
    @Schema(description = "支付渠道（线上，线下）")
    private Integer paymentChannel;

    @Schema(description = "支付渠道名称（线上，线下）")
    private String paymentChannelName;

    /**
     * 支付方式（0线上-微信，1线下-微信，2支付宝，3现金，4银行卡，5余额，6活动减免）
     */
    @NotNull(message = "支付方式不能为空")
    @Digits(integer = 1, fraction = 0, message = "支付方式为整数")
    @Min(value = 0L, message = "支付方式不能小于1")
    @Max(value = 6L, message = "支付方式不能大于6")
    @Schema(description = "支付方式（0线上-微信，1线下-微信，2支付宝，3现金，4银行卡，5余额，6活动减免）")
    private String paymentMode;

    @Schema(description = "支付方式名称（0线上-微信，1线下-微信，2支付宝，3现金，4银行卡，5余额，6活动减免）")
    private String paymentModeName;

    /**
     * 支付状态（未支付，已支付）
     */
    @NotNull(message = "支付方式不能为空")
    @Digits(integer = 1, fraction = 0, message = "支付方式为整数")
    @Min(value = 0L, message = "支付方式不能小于1")
    @Max(value = 6L, message = "支付方式不能大于6")
    @Schema(description = "支付状态（未支付，已支付）")
    private Integer paymentStatus;

    @Schema(description = "支付状态名称（未支付，已支付）")
    private String paymentStatusName;

    private String remark;

    private String detailRemark;

    /**
     * 业态
     */
    @Schema(description = "业态")
    private Integer businessForms;

    @Schema(description = "支付时间字符串yyyy-MM-dd")
    private String paymentTime;

    @Schema(description = "查询支付时间的开始时间yyyy-MM-dd HH:mm:ss")
    private String startPaymentTime;

    @Schema(description = "查询支付时间的结束时间yyyy-MM-dd HH:mm:ss")
    private String endPaymentTime;

    @Schema(description = "创建时间字符串yyyy-MM-dd")
    private String createTime;

    @Schema(description = "查询创建时间的开始时间yyyy-MM-dd HH:mm:ss")
    private String startCreateTime;

    @Schema(description = "查询创建时间的结束时间yyyy-MM-dd HH:mm:ss")
    private String endCreateTime;

    @Schema(description = "支付详情")
    List<PaymentDetailReqDTO> paymentDetails;

    @Schema(description = "导入批次号")
    private String batchCode;

    @Schema(description = "作废原因")
    private String deleteReason;

    @Schema(description = "退费时间")
    private String refundTime;

    @Schema(description = "退费原因")
    private String refundReason;

    @Schema(description = "查询作废时间的开始时间yyyy-MM-dd HH:mm:ss")
    private String startDeleteTime;

    @Schema(description = "查询作废时间的结束时间yyyy-MM-dd HH:mm:ss")
    private String endDeleteTime;
}
