package cathayfuture.opm.client.order.api;

import cathayfuture.opm.client.order.dto.bo.MonthlyStatementBo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface OrderMonthlyStatementAppService {
    /**
     * 批量新增
     * @param boList
     */
    void batchInsert(List<MonthlyStatementBo> boList);
    /**
     * 查询月结
     * @param list  学生id列表
     * @param date 查询月份
     * @param orderType OrderTypeEnum的key
     * @return
     */
    Map<Integer,BigDecimal> queryPreviousActualSurplusMap(List<Integer> list, LocalDate date, Integer orderType);
}
