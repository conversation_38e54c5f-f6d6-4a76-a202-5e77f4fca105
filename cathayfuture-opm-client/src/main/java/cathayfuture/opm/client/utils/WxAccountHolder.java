package cathayfuture.opm.client.utils;

import cathayfuture.opm.client.account.dto.response.AccountRespDTO;

/**
 * <AUTHOR>
 * @since ********
 */
public class WxAccountHolder {

    private WxAccountHolder() {

    }

    private static final ThreadLocal<AccountRespDTO> HOLDER = new ThreadLocal<>();

    public static void set(AccountRespDTO user) {
        HOLDER.set(user);
    }

    public static AccountRespDTO get() {
        return HOLDER.get();
    }

    public static void remove(){
        HOLDER.remove();
    }

}
