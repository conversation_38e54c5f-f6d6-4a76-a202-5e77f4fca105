package cathayfuture.opm.client.student.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 20220824
 */
@Data
@Accessors(chain = true)
@ToString
public class StudentReqDTO {

    @Schema(description = "主键")
    private Integer id;
    @Schema(description = "类型")
    private Integer studentType;

    @NotBlank
    @Schema(description = "姓名")
    private String studentName;
    @NotBlank
    @Schema(description = "学号")
    private String studentNo;
    @NotBlank
    @Schema(description = "班级")
    private String studentClass;
    @NotBlank
    @Schema(description = "民族")
    private String ethnicGroup;
    @NotBlank
    @Schema(description = "身份证号")
    private String idNumber;
    @NotNull
    @Schema(description = "性别")
    private Integer gender;
    @NotBlank
    @Schema(description = "生日")
    private String birthday;
    @NotBlank
    @Schema(description = "国籍")
    private String nationality;
    @NotBlank
    @Schema(description = "联系人")
    private String contactName;
    @NotBlank
    @Schema(description = "关系")
    private String relation;
    @NotBlank
    @Schema(description = "联系方式")
    private String contactPhoneNumber;
    @Schema(description = "爸爸姓名")
    private String fatherName;
    @Schema(description = "爸爸联系方式")
    private String fatherPhoneNumber;
    @Schema(description = "妈妈姓名")
    private String motherName;
    @Schema(description = "妈妈联系方式")
    private String motherPhoneNumber;

    @Schema(description = "省编码")
    private String provinceCode;
    @Schema(description = "省名称")
    private String provinceName;

    @Schema(description = "市编码")
    private String cityCode;
    @Schema(description = "市名称")
    private String cityName;

    @Schema(description = "区编码")
    private String regionCode;

    @Schema(description = "区名称")
    private String regionName;

    @Schema(description = "家庭住址")
    private String homeAddress;

    @Schema(description = "入园时间")
    private String admissionDate;
    @Schema(description = "业态")
    private Integer businessUnit;

    @Schema(description = "入园时间查询开始时间， yyyy-MM-dd")
    private String startAdmissionDate;
    @Schema(description = "入园时间查询截至时间， yyyy-MM-dd")
    private String endAdmissionDate;

    @Schema(description = "家长姓名")
    private String parentName;

    @Schema(description = "入园标记（0预报名，1在园）,不传值为忽略入园标记")
    private Integer registerStatus;

}
