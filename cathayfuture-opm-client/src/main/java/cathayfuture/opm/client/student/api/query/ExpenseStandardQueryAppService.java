package cathayfuture.opm.client.student.api.query;

import cathayfuture.opm.client.order.dto.response.PageRespDTO;
import cathayfuture.opm.client.student.dto.request.ExpenseStandardReqDTO;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import cathayfuture.opm.client.student.dto.response.ExpenseStandardRespDTO;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20230331
 */
public interface ExpenseStandardQueryAppService {

    PageRespDTO<ExpenseStandardRespDTO> page(ExpenseStandardReqDTO expenseStandardReqDTO, int pageNum, int pageSize);
    ExpenseStandardRespDTO getById(Integer expenseStandardId);

    /**
     * 根据费用类别和学生ids查询标准费用
     * @param studentId
     * @param expenseType
     * @return
     */
    List<ExpenseStandardRespDTO> findListByStudentIdsAndExpenseType(List<Integer> studentId, Integer expenseType);

    ExpenseStandardRespDTO edit(ExpenseStandardReqDTO expenseStandardReqDTO);

    void add(ExpenseStandardReqDTO expenseStandardReqDTO);
}
