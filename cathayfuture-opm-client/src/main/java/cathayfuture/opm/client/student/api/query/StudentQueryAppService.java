package cathayfuture.opm.client.student.api.query;

import cathayfuture.opm.client.order.dto.response.PageRespDTO;
import cathayfuture.opm.client.student.dto.request.QueryByContactPhoneNumberReqDTO;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 20220824
 */
public interface StudentQueryAppService {


    StudentRespDTO getById(Integer studentId);

    /**
     * 根据Ids查询
     *
     * @param studentIdList
     * @return
     */
    List<StudentRespDTO> listByIds(List<Integer> studentIdList);

    /**
     * 根据联系人手机号查询学生列表
     *
     * @param queryByContactPhoneNumberReqDTO
     * @return
     */
    List<StudentRespDTO> listByContactPhoneNumber(QueryByContactPhoneNumberReqDTO queryByContactPhoneNumberReqDTO);

    /**
     * 根据联系人手机号查询学生列表
     *
     * @param phoneNumber
     * @return
     */
    List<StudentRespDTO> listByContactPhoneNumber(String phoneNumber);

    List<StudentRespDTO> listByType(Integer type);

    List<StudentRespDTO> list(StudentReqDTO studentReqDTO);

    List<StudentRespDTO> listByStudentNo(String studentNo);

    Integer countByStudentNo(String studentNo);

    StudentRespDTO idCardResolver(String idNumber);

    PageRespDTO<StudentRespDTO> page(StudentReqDTO studentReqDTO, int pageNum, int pageSize);

    List<StudentRespDTO> queryListByStudentName(String studentName);

    List<StudentRespDTO> queryListByAdmissionDateAndStudentClass(String studentClass);

    Map<String,List<StudentRespDTO>> queryStudentGroupByClass();

    List<StudentRespDTO> queryByCondition(String studentName, LocalDate birthday, List<Integer> registerStatusList);
}
