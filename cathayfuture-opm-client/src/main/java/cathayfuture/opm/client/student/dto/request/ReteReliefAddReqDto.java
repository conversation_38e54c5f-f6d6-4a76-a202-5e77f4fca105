package cathayfuture.opm.client.student.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 减免add reqdto
 * @date 2023/3/31 13:57
 */

@Data
@ToString
public class ReteReliefAddReqDto {

    @NotNull
    @Schema(description = "学生id")
    private Integer studentId;
    @NotBlank
    @Schema(description = "减免月份")
    private String derateMonth;
    @NotBlank
    @Schema(description = "减免金额")
    private String derateAmount;
    @Schema(description = "备注")
    private String remark;


    public BigDecimal getDerateAmount(){
        return Optional.ofNullable(derateAmount).map(BigDecimal::new).orElse(BigDecimal.ZERO);
    }
}
