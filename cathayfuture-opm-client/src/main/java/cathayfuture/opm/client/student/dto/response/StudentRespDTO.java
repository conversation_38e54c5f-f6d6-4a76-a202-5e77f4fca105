package cathayfuture.opm.client.student.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 20220824
 */
@Data
@ToString
@Schema(name = "StudentRespDTO", description = "学生信息返回体")
public class StudentRespDTO {

    @Schema(description = "主键")
    private Integer id;
    @Schema(description = "类型")
    private Integer studentType;
    @Schema(description = "姓名")
    private String studentName;
    @Schema(description = "学号")
    private String studentNo;
    @Schema(description = "生日")
    private LocalDate birthday;
    @Schema(description = "性别")
    private Integer gender;
    @Schema(description = "班级")
    private String studentClass;
    @Schema(description = "联系方式")
    private String contactPhoneNumber;
    @Schema(description = "绑定标志")
    private Boolean bindFlag;
    @Schema(description = "民族")
    private String ethnicGroup;
    @Schema(description = "身份证号")
    private String idNumber;
    @Schema(description = "国籍")
    private String nationality;
    @Schema(description = "省编码")
    private String provinceCode;
    @Schema(description = "省名称")
    private String provinceName;
    @Schema(description = "市编码")
    private String cityCode;
    @Schema(description = "市名称")
    private String cityName;
    @Schema(description = "区编码")
    private String regionCode;
    @Schema(description = "区名称")
    private String regionName;
    @Schema(description = "家庭住址")
    private String homeAddress;
    @Schema(description = "联系人")
    private String contactName;
    @Schema(description = "关系")
    private String relation;
    @Schema(description = "爸爸姓名")
    private String fatherName;
    @Schema(description = "爸爸联系方式")
    private String fatherPhoneNumber;
    @Schema(description = "妈妈姓名")
    private String motherName;
    @Schema(description = "妈妈联系方式")
    private String motherPhoneNumber;
    @Schema(description = "入园时间")
    private LocalDate admissionDate;
    @Schema(description = "业态")
    private Integer businessUnit;
    @Schema(description = "入园状态（0预报名，1在园，2退园）")
    private Integer registerStatus;
    @Schema(description = "入园状态中文（0预报名，1在园，2退园）")
    private String registerStatusStr;
    @Schema(description = "是否完成支付杂费")
    private Boolean completePayment;

    public String getBirthday() {
        return Optional.ofNullable(this.birthday).map(d -> d.format(DateTimeFormatter.ISO_LOCAL_DATE)).orElse(null);
    }

    public String getAdmissionDate() {
        return Optional.ofNullable(this.admissionDate).map(d -> d.format(DateTimeFormatter.ISO_LOCAL_DATE)).orElse(null);
    }


}
