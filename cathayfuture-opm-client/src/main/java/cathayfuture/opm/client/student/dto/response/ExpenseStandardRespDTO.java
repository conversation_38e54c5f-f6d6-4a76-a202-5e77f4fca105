package cathayfuture.opm.client.student.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 20230331
 */
@Data
@ToString
@Schema(name = "ExpenseStandardRespDTO", description = "费用标准管理信息返回体")
public class ExpenseStandardRespDTO {

    @Schema(description = "主键")
    private Integer id;
    @Schema(description = "学生id")
    private Integer studentId;
    @Schema(description = "学生姓名")
    private String studentName;
    @Schema(description = "学号")
    private String studentNo;
    @Schema(description = "身份证号")
    private String idNumber;
    @Schema(description = "性别")
    private Integer gender;
    @Schema(description = "入园时间")
    private LocalDate admissionDate;
    @Schema(description = "业态")
    private Integer businessUnit;
    @Schema(description = "费用类型（0保育费，1餐费，2定金，3代收费，4杂费）")
    private Integer expenseType;
    @Schema(description = "费用类型中文（0保育费，1餐费，2定金，3代收费，4杂费）")
    private String expenseTypeStr;
    @Schema(description = "费用标准类型（0默认值，1自定义）")
    private Integer expenseStandardType;
    @Schema(description = "费用标准类型中文（0默认值，1自定义）")
    private String expenseStandardTypeStr;
    @Schema(description = "费用标准")
    private BigDecimal expenseStandard;
    @Schema(description = "备注")
    private String remark;

    public BigDecimal getExpenseStandard() {
        return Optional.ofNullable(this.expenseStandard).orElse(BigDecimal.ZERO);
    }

    public String getExpenseStandardValue() {
        return Optional.ofNullable(this.expenseStandard).map(BigDecimal::toPlainString).orElse(null);
    }
}
