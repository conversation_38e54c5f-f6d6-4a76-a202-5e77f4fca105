package cathayfuture.opm.client.student.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 减免查询学生信息
 * @date 2023/3/31 11:09
 */
@Data
@ToString
public class RateReliefQueryStudentInfoByNameRespDto {

    @Schema(description = "主键")
    private Integer studentId;

    @Schema(description = "姓名")
    private String studentName;

    @Schema(description = "学号")
    private String studentNo;

    @Schema(description = "生日")
    private String birthday;

    @Schema(description = "标准费用")
    private String childCareFeeStandard;

    public static RateReliefQueryStudentInfoByNameRespDto build(StudentRespDTO respDTO,BigDecimal childCareFeeStandard){
        RateReliefQueryStudentInfoByNameRespDto result = new RateReliefQueryStudentInfoByNameRespDto();
        result.setStudentId(respDTO.getId());
        result.setStudentName(respDTO.getStudentName());
        result.setStudentNo(respDTO.getStudentNo());
        result.setBirthday(respDTO.getBirthday());
        result.setChildCareFeeStandard(Optional.ofNullable(childCareFeeStandard).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString());
        return result;
    }
}
