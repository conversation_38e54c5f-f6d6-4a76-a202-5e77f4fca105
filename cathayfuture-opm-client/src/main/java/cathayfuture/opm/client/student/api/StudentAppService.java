package cathayfuture.opm.client.student.api;

import cathayfuture.opm.client.order.dto.request.CreateFrontMoneyOrderReqDTO;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;

/**
 * <AUTHOR>
 * @date 20220824
 */
public interface StudentAppService {

    /**
     * 新增学生报名
     * @param studentReqDTO
     * @return
     */
    StudentRespDTO addFrontMoneyStudent(StudentReqDTO studentReqDTO);

    /**
     * 保存学生
     * @param studentReqDTO
     * @return
     */
    StudentRespDTO save(StudentReqDTO studentReqDTO);

    /**
     * 入园
     * @param studentReqDTO
     * @return
     */
    StudentRespDTO register(StudentReqDTO studentReqDTO);

    void quit(Integer studentId);

    /**
     * 校验重复注册学生数据
     * @param reqDTO
     */
    void checkRepeatData(CreateFrontMoneyOrderReqDTO reqDTO);
}
