package cathayfuture.opm.client.student.api.query;

import cathayfuture.opm.client.student.dto.request.RateReliefPageReqDTO;
import cathayfuture.opm.client.student.dto.response.RateReliefPageRespDTO;
import cathayfuture.opm.client.student.dto.response.RateReliefQueryStudentInfoByNameRespDto;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface RateReliefQueryAppService {
    IPage<RateReliefPageRespDTO> page(RateReliefPageReqDTO reqDTO, IPage page);

    List<RateReliefQueryStudentInfoByNameRespDto> queryListByStudentName(String studentName);

    Map<Integer,BigDecimal> queryMapByStudentIdsAndMonth(List<Integer> studentIds, String month);
}
