package cathayfuture.opm.client.student.dto.response;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 减免分页查询resp
 * @date 2023/3/31 10:21
 */

@Data
@ToString
public class RateReliefPageRespDTO {

    private int id;

    private int studentId;

    private int costType;

    private String derateMonth;

    private BigDecimal derateAmount;

    private String studentName;

    private String studentNo;

    private String costTypeDesc;

    private String remark;
}
