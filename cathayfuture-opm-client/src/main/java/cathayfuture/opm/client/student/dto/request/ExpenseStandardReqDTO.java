package cathayfuture.opm.client.student.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 20230331
 */
@Data
@Accessors(chain = true)
@ToString
public class ExpenseStandardReqDTO {

    @Schema(description = "主键")
    private Integer id;

    @NotBlank
    @Schema(description = "学生姓名")
    private String studentName;
    @NotBlank
    @Schema(description = "学号")
    private String studentNo;
    @NotBlank
    @Schema(description = "学生id")
    private Integer studentId;

    @Schema(description = "费用标准类型（0默认值，1自定义）")
    private Integer expenseStandardType;

    @Schema(description = "费用标准")
    private BigDecimal expenseStandard;
    @Schema(description = "费用类型")
    private Integer expenseType;

    @Schema(description = "")
    private Integer defaultId;
    @Schema(description = "备注")
    private String remark;

}
