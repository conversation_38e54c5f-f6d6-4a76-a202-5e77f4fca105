package cathayfuture.opm.client.account.api.query;

import cathayfuture.opm.client.account.dto.response.RAccountStudentRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date ********
 */
public interface RAccountStudentQueryAppService {

    /**
     * 根据account查询关系
     * @param accountId
     * @return
     */
    List<RAccountStudentRespDTO> listByAccountId(Integer accountId);

    /**
     * 根据student查询关系
     * @param studentId
     * @return
     */
    List<RAccountStudentRespDTO> listByStudentId(Integer studentId);
}
