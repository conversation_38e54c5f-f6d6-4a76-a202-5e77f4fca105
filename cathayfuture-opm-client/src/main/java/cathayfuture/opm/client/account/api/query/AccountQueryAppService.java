package cathayfuture.opm.client.account.api.query;

import cathayfuture.opm.client.account.dto.response.AccountRespDTO;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date ********
 */
public interface AccountQueryAppService {

    /**
     * 获取当前用户绑定的学生信息
     * @return
     */
    List<StudentRespDTO> listBoundStudents();

    /**
     * 根据学生编号查询绑定的帐号
     * @param studentId
     * @param includeCurrentUser 是否包含当前登陆人
     * @return
     */
    List<AccountRespDTO> listBoundAccounts(Integer studentId, Boolean includeCurrentUser);
}
