package cathayfuture.opm.client.account.api;

import cathayfuture.opm.client.account.dto.request.AccountReqDTO;
import cathayfuture.opm.client.account.dto.response.AccountRespDTO;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date ********
 */
public interface AccountAppService {

    /**
     * 微信登陆接口
     *
     * @param code
     * @param phoneCode
     * @param accountReqDTO
     * @return
     */
    AccountRespDTO wxLogin(String code, String phoneCode, AccountReqDTO accountReqDTO);

    /**
     * 绑定微信手机号
     * @param code
     * @return
     */
    String bindPhoneNumber(String code);

    /**
     * 当前用户绑定学生信息
     * @param studentReqDTOList
     * @return
     */
    List<StudentRespDTO> bindStudents(List<StudentReqDTO> studentReqDTOList);
}
