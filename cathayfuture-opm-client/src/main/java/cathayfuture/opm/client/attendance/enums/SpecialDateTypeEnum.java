package cathayfuture.opm.client.attendance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 特殊日期枚举
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum SpecialDateTypeEnum {
    /**
     * 周末
     */
    WEEKEND(0,"周末"),
    /**
     * 寒暑假
     */
    HOLIDAY(1,"寒暑假"),
    /**
     * 闭园
     */
    CLOSE(2,"闭园"),
    ;

    private final int key;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values())
                .filter(value -> Objects.equals(value.getKey(), key))
                .findFirst()
                .orElseThrow(NoSuchElementException::new).getDescription();
    }
}
