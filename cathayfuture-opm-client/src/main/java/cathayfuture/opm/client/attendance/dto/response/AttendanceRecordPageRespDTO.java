package cathayfuture.opm.client.attendance.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 考勤统计分页查询返回体
 * @date 2023/4/4 9:00
 */

@Data
@ToString
@Schema(name = "AttendanceRecordPageRespDTO", description = "考勤统计分页查询返回体")
public class AttendanceRecordPageRespDTO {

    @Schema(description = "主键")
    private Integer id;
    @Schema(description = "学生表主键")
    private Integer studentId;
    @Schema(description = "学生姓名")
    private String studentName;
    @Schema(description = "学号")
    private String studentNo;
    @Schema(description = "性别")
    private Integer gender;
    @Schema(description = "性别中文")
    private String genderdesc;
    @Schema(description = "班级")
    private String studentClass;
    @Schema(description = "上午考勤中文")
    private String amAttendanceDesc;
    @Schema(description = "中午考勤中文")
    private String noonAttendanceDesc;
    @Schema(description = "下午考勤中文")
    private String pmAttendanceDesc;
    @Schema(description = "上午考勤,0缺勤1出勤")
    private Integer amAttendance;
    @Schema(description = "中午考勤,0缺勤1出勤")
    private Integer noonAttendance;
    @Schema(description = "下午考勤,0缺勤1出勤")
    private Integer pmAttendance;
    @Schema(description = "考勤状态")
    private String statusDesc;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "考勤时间")
    private LocalDate attendanceDate;
    @Schema(description = "考勤时间str")
    private String attendanceDateStr;
}
