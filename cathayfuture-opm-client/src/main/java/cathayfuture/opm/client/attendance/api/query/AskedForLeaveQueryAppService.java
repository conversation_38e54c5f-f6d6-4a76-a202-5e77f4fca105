package cathayfuture.opm.client.attendance.api.query;

import cathayfuture.opm.client.attendance.dto.request.AskedForLeaveReqDto;
import cathayfuture.opm.client.attendance.dto.response.AskedForLeaveRespDto;
import cathayfuture.opm.client.attendance.dto.response.StudentInfoAndLongTermRespDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;


public interface AskedForLeaveQueryAppService {
    /**
     * 分页查询
     * @param studentReqDTO
     * @param page
     * @return
     */
    IPage<AskedForLeaveRespDto> page(AskedForLeaveReqDto studentReqDTO, IPage page);

    /**
     * 判断是否长期病假
     * @param stuId
     * @return
     */
    Boolean judgementLongTerm(Integer stuId, LocalDate date);

    Map<Integer,Boolean> judgementLongTermByStudentIds(List<Integer> stuIds, LocalDate localDate);
}
