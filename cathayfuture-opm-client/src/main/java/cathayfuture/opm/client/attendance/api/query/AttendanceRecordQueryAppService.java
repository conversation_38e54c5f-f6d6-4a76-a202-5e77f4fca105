package cathayfuture.opm.client.attendance.api.query;

import cathayfuture.opm.client.attendance.dto.request.AttendanceRecordPageReqDto;
import cathayfuture.opm.client.attendance.dto.response.AttendanceRecordDataIntegrityByClassRespDto;
import cathayfuture.opm.client.attendance.dto.response.AttendanceRecordPageRespDTO;
import cathayfuture.opm.client.attendance.dto.response.StudentInfoAndLongTermRespDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface AttendanceRecordQueryAppService {
    IPage<AttendanceRecordPageRespDTO> page(AttendanceRecordPageReqDto reqDto, IPage page);

    List<StudentInfoAndLongTermRespDTO> queryStudentInfoList(String studentClass,String currentDate);

    /**
     * 根据类别和学生id查询返回map<学生id，天数>
     * @param type
     * @param studentIds
     * @return
     */
    Map<Integer, Long> countAttendanceMonthByStudentIds(Integer type, List<Integer> studentIds, LocalDate date);

    Map<Integer, LocalDate> queryStudentFirstDayMap();

    /**
     * 判断当天这些学生是否有考勤记录
     * @param studentIds
     * @return
     */
    Boolean dataIntegrityByStudentIds(List<Integer> studentIds);
    /**
     * 统计当前日期班级内学生是否考勤是否填写完毕
     * @return
     */
    List<AttendanceRecordDataIntegrityByClassRespDto> queryAttendanceRecordDataIntegrityByClassRespDto();
}
