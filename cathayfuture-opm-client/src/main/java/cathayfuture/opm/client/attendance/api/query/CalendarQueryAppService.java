package cathayfuture.opm.client.attendance.api.query;

import cathayfuture.opm.client.attendance.enums.SpecialDateTypeEnum;

import java.time.LocalDate;
import java.util.List;

public interface CalendarQueryAppService {
    /**
     * 判断是否为特殊日期
     * @param date
     * @param typeEnum
     * @return
     */
    Integer judgeSpecialDate(LocalDate date, SpecialDateTypeEnum typeEnum);

    /**
     * 去掉法定假日的寒暑假 date为null时查当月，不为null查对应月
     * @return
     */
    long countWinterVacationAndSummerVacationDaysWithoutWeekend(LocalDate date);

    /**
     * 统计工作日，date为null时查当月，不为null查对应月
     * @param date
     * @return
     */
    long countWeekDays(LocalDate date);
    /**
     * 统计工作日去除闭园，date为null时查当月，不为null查对应月
     * @param date
     * @return
     */
    long countWorkDaysWithoutColse(LocalDate date);

    /**
     * 统计月天数，去除周末和法定节假日
     * @param date
     * @return
     */
    long countWithoutWeekend(LocalDate date);

    /**
     * 统计月天数，包括寒暑假+开园日，过滤掉周末和闭园日
     * @param date
     * @return
     */
    List<LocalDate> countWithoutWeekendAndWithoutClose(LocalDate date);

    /**
     * 统计月教学日
     * @param date
     * @return
     */
    List<LocalDate> countWeekDay(LocalDate date);

    List<Integer> getDayOfMonthList(LocalDate date);

    /**
     * 获取假期
     * @param date
     * @return
     */
    List<Integer> getDayOfMonthHolidayList(LocalDate date);

    String queryCurrentVersion(LocalDate targetDate);
}
