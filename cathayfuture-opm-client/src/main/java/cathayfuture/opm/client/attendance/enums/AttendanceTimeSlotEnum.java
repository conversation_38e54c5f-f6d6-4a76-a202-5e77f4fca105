package cathayfuture.opm.client.attendance.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 考勤时间段
 * <AUTHOR>
 */
@Getter
public enum AttendanceTimeSlotEnum {
    /**
     * 上午
     */
    AM,
    /**
     * 中午
     */
    NOON,
    /**
     * 下午
     */
    PM,
    /**
     * 其他
     */
    OTHER,
    ;

    public static AttendanceTimeSlotEnum getEnum(Integer amFlg,Integer noonFlg,Integer pmFlg){
        if(Objects.nonNull(amFlg)){
            return AM;
        }
        if(Objects.nonNull(noonFlg)){
            return NOON;
        }
        if(Objects.nonNull(pmFlg)){
            return PM;
        }
        return OTHER;
    }


//    public static void checkData(Integer integer, Predicate<Integer> predicate){
//        predicate.test(integer)
//    }
}
