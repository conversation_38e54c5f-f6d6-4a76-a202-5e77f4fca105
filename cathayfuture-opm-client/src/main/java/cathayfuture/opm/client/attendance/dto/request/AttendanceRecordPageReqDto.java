package cathayfuture.opm.client.attendance.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 考勤记录
 * @date 2023/4/3 16:49
 */
@Data
@ToString
@Schema(name = "AttendanceRecordPageReqDto", description = "考勤统计分页查询请求体")
public class AttendanceRecordPageReqDto {
    @Schema(description = "学生姓名")
    private String studentName;
    @Schema(description = "学号")
    private String studentNo;
    @Schema(description = "班级")
    private String studentClass;
    @Schema(description = "开始时间")
    private String startDateStr;
    @Schema(description = "结束时间")
    private String endDateStr;
    @Schema(description = "考勤状态0缺勤1出勤")
    private Integer status;
}
