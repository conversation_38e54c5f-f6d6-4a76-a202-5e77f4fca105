package cathayfuture.opm.client.attendance.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 请假分页查询reqDto
 * @date 2023/3/27 16:03
 */
@Data
@ToString
public class AskedForLeaveReqDto {

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学生编码")
    private String studentNo;

    @Schema(description = "请假时间")
    private String dateStr;

    @Schema(description = "请假状态")
    private Integer status;

    @Schema(description = "实际请假开始时间")
    private String realStartDateStr;

    @Schema(description = "实际请假结束时间")
    private String realEndDateStr;


}
