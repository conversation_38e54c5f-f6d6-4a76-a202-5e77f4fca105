package cathayfuture.opm.client.attendance.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 学生信息和长期病假返回体
 * @date 2023/4/4 16:47
 */
@Data
@ToString
@Schema(name = "StudentInfoAndLongTermRespDTO", description = "学生信息和长期病假返回体")
public class StudentInfoAndLongTermRespDTO {
    @Schema(description = "学生id")
    private Integer studentId;
    @Schema(description = "学生姓名")
    private String studentName;
    @Schema(description = "学号")
    private String studentNo;
    @Schema(description = "生日")
    private LocalDate birthday;
    @Schema(description = "性别")
    private Integer gender;
    @Schema(description = "性别中文")
    private String genderDesc;
    @Schema(description = "是否长期病假")
    private Boolean longTerm;
    @Schema(description = "当前日期")
    private LocalDate currentDate;
    @Schema(description = "当前月份")
    private String currentMonthStr;


    @Schema(description = "上午考勤,0缺勤1出勤")
    private Integer amAttendance;
    @Schema(description = "中午考勤,0缺勤1出勤")
    private Integer noonAttendance;
    @Schema(description = "下午考勤,0缺勤1出勤")
    private Integer pmAttendance;

    @Schema(description = "备注")
    private String remark;
    @Schema(description = "考勤异常信息")
    private String unusualAttendanceMessage;

    public String getBirthday() {
        return Optional.ofNullable(this.birthday).map(d -> d.format(DateTimeFormatter.ISO_LOCAL_DATE)).orElse(null);
    }

    public String getCurrentDate(){
        return Optional.ofNullable(this.currentDate).map(d -> d.format(DateTimeFormatter.ISO_LOCAL_DATE)).orElse(null);
    }
}
