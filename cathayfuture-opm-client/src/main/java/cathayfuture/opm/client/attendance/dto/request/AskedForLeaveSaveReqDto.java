package cathayfuture.opm.client.attendance.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 请假新增入参
 * @date 2023/3/28 14:51
 */
@Data
public class AskedForLeaveSaveReqDto {

    @NotNull
    @Schema(description = "学生id")
    private Integer studentId;
    @NotBlank
    @Schema(description = "请假开始时间,前端只传月份")
    private String startDateStr;
    @NotBlank
    @Schema(description = "请假结束时间，前端只传月份")
    private String endDateStr;
}
