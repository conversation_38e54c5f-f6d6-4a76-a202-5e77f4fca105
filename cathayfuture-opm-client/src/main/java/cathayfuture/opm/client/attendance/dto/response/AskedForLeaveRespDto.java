package cathayfuture.opm.client.attendance.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 请假分页查询respDto
 * @date 2023/3/27 16:11
 */

@Data
@ToString
@Schema(name = "AskForLeaveRespDto", description = "请假分页查询返回体")
public class AskedForLeaveRespDto {

    private Integer id;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学生编码")
    private String studentNo;

    @Schema(description = "请假开始时间")
    private String startDateStr;

    @Schema(description = "请假结束时间")
    private String endDateStr;

    @Schema(description = "实际请假开始时间")
    private LocalDate realStartDate;

    @Schema(description = "实际请假结束时间")
    private LocalDate realEndDate;

    @Schema(description = "实际请假开始时间")
    private String realStartDateStr;

    @Schema(description = "实际请假结束时间")
    private String realEndDateStr;

    @Schema(description = "销假开始时间")
    private LocalDate returnStartDate;

    @Schema(description = "销假结束时间")
    private LocalDate returnEndDate;

    @Schema(description = "请假状态")
    private Integer status;

    @Schema(description = "请假状态中文")
    private String statusDesc;

    @Schema(description = "学生信息id")
    private Integer stuId;

    @Schema(description = "请假开始时间")
    private LocalDate startDate;

    @Schema(description = "请假结束时间")
    private LocalDate endDate;

    @Schema(description = "请假类型(0其他，1长期)")
    private Integer type;

    @Schema(description = "请假类型(0其他，1长期)")
    private String typeDesc;
}
