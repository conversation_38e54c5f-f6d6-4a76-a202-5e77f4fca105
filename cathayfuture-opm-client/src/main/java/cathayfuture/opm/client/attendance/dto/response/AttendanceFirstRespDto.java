package cathayfuture.opm.client.attendance.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 考勤第一天
 * @date 2023/5/29 13:29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceFirstRespDto {
    private Integer studentId;
    private LocalDate firstDay;
}
