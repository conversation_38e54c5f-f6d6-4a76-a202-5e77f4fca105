package cathayfuture.opm.client.attendance.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销假入参
 * @date 2023/3/28 14:12
 */
@Data
@NoArgsConstructor
public class ReturnOperateReqDto {

    @NotNull

    @Schema(description = "主键")
    private Integer id;
    @NotBlank
    @Schema(description = "销假结束时间")
    private String returnEndDateStr;
}
