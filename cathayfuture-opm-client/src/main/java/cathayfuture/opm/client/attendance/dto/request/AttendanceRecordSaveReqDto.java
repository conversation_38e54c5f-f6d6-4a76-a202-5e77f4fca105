package cathayfuture.opm.client.attendance.dto.request;

import icu.mhb.mybatisplus.plugln.tookit.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 考勤记录新增reqdto
 * @date 2023/4/4 10:55
 */
@Data
@ToString
@Schema(name = "AttendanceRecordSaveReqDto", description = "考勤记录请求体")
public class AttendanceRecordSaveReqDto {
    @NotNull(message = "学生id不能为null")
    @Schema(description = "学生id")
    private Integer studentId;
    @NotBlank(message = "当前日期不能为空")
    @Schema(description = "当前日期")
    private String currentDate;
    @Schema(description = "上午考勤0缺勤1出勤")
    private Integer amAttendance;
    @Schema(description = "中午考勤0缺勤1出勤")
    private Integer noonAttendance;
    @Schema(description = "下午考勤0缺勤1出勤")
    private Integer pmAttendance;
    @Schema(description = "备注")
    private String remark;

}
