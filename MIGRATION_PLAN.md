# 华夏未来OPM微服务依赖移除和Spring Security集成方案

## 项目概述

本方案旨在移除华夏未来OPM系统对微服务依赖（yundt-module-starter-context、tasly-center-userapi、nacos）的同时，引入Spring Security框架建立现代化的认证授权体系。当前阶段支持固定用户名密码认证，为未来扩展奠定基础。

## 一. 架构设计决策

### 1. 认证架构现代化策略

**现状分析：**
- 移动端：JWT + WxAuthenticationInterceptor (运行良好)
- BMS后台：ExtServiceContextInterceptor + 用户中心API (需要替换)

**目标架构：**
- 统一到Spring Security框架
- 双认证机制：JWT + Username/Password
- SecurityContext与ExtServiceContext桥接
- 标准的登录/登出接口

### 2. 核心技术选型

- **Spring Security**: 现代化认证授权框架
- **固定用户管理**: 配置文件 + UserDetailsService
- **JWT集成**: 自定义Filter + AuthenticationProvider
- **向后兼容**: 保持ExtServiceContext API不变
- **保留cube-starter-lock**: 分布式锁功能继续使用

## 二. 详细实施方案

### P0 - 基础依赖清理 (3-4小时)

#### 1. POM依赖调整

**移除的依赖：**
```xml
<dependency>
    <groupId>com.dtyunxi.module</groupId>
    <artifactId>yundt-module-starter-context</artifactId>
</dependency>
<dependency>
    <groupId>com.dtyunxi.tasly</groupId>
    <artifactId>tasly-center-userapi</artifactId>
</dependency>
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
</dependency>
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
</dependency>
```

**新增的依赖：**
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-security</artifactId>
</dependency>
```

#### 2. 配置文件清理

**删除nacos配置：**
- 移除bootstrap.yml中的spring.cloud.nacos配置节

**添加固定用户配置：**
```yaml
cathay:
  auth:
    username: admin
    password: admin123
    user-id: 1
    role-ids: 1,2,3  
    user-name: 系统管理员
    post-code: ADMIN
    post-name: 超级管理员
```

#### 3. 代码清理

**修改App.java：**
- 移除`@EnableDiscoveryClient`注解
- 删除包扫描中的：`"com.dtyunxi.yundt.cube", "com.dtyunxi.tasly.center.user.api"`
- 删除@EnableFeignClients中的：`"com.dtyunxi.yundt.cube", "com.dtyunxi.tasly.center.user.api"`

**删除文件：**
- `DiscoveryConfig.java`

**清理import语句：**
- 移除所有相关的import语句

### P1 - Spring Security核心集成 (4-5小时)

#### 1. 固定用户服务

```java
@Service
public class FixedUserDetailsService implements UserDetailsService {
    
    @Value("${cathay.auth.username}")
    private String username;
    
    @Value("${cathay.auth.password}")
    private String password;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public UserDetails loadUserByUsername(String inputUsername) {
        if (!username.equals(inputUsername)) {
            throw new UsernameNotFoundException("用户不存在");
        }
        
        return User.builder()
            .username(username)
            .password(passwordEncoder.encode(password))
            .authorities("ROLE_ADMIN")
            .accountExpired(false)
            .accountLocked(false)
            .credentialsExpired(false)
            .disabled(false)
            .build();
    }
}
```

#### 2. JWT认证Filter

```java
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                    HttpServletResponse response, 
                                    FilterChain filterChain) throws ServletException, IOException {
        
        String token = extractTokenFromRequest(request);
        if (StringUtils.hasText(token)) {
            try {
                // 解析JWT并验证
                AccountRespDTO account = JwtUtils.decodeToken(token, AccountRespDTO.class);
                if (account != null) {
                    // 创建Authentication对象
                    List<SimpleGrantedAuthority> authorities = Arrays.asList(
                        new SimpleGrantedAuthority("ROLE_USER")
                    );
                    JwtAuthenticationToken authentication = 
                        new JwtAuthenticationToken(account, token, authorities);
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                }
            } catch (Exception e) {
                logger.warn("JWT token验证失败", e);
            }
        }
        
        filterChain.doFilter(request, response);
    }
    
    private String extractTokenFromRequest(HttpServletRequest request) {
        String header = request.getHeader("Authorization");
        if (StringUtils.hasText(header) && header.startsWith("Bearer ")) {
            return header.substring(7);
        }
        return header;
    }
    
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        // 只对移动端路径进行JWT验证
        return !request.getRequestURI().startsWith("/mobile/");
    }
}
```

#### 3. 自定义Authentication Token

```java
public class JwtAuthenticationToken extends AbstractAuthenticationToken {
    
    private final Object principal;
    private final String token;
    
    public JwtAuthenticationToken(Object principal, String token, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        this.token = token;
        setAuthenticated(true);
    }
    
    @Override
    public Object getCredentials() {
        return token;
    }
    
    @Override
    public Object getPrincipal() {
        return principal;
    }
}
```

#### 4. Security配置

```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Autowired
    private FixedUserDetailsService userDetailsService;
    
    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/bms/auth/**").permitAll()
                .requestMatchers("/mobile/wx/login", "/mobile/callbacks/**").permitAll()
                .requestMatchers("/bms/**").authenticated()
                .requestMatchers("/mobile/**").authenticated()
                .anyRequest().authenticated())
            .authenticationProvider(daoAuthenticationProvider())
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
            .csrf(csrf -> csrf.disable())
            .cors(cors -> cors.disable());
        
        return http.build();
    }
    
    @Bean
    public DaoAuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider provider = new DaoAuthenticationProvider();
        provider.setUserDetailsService(userDetailsService);
        provider.setPasswordEncoder(passwordEncoder());
        return provider;
    }
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @Bean
    public AuthenticationManager authenticationManager(HttpSecurity http) throws Exception {
        return http.getSharedObject(AuthenticationManagerBuilder.class).build();
    }
}
```

#### 5. BMS认证接口

```java
@RestController
@RequestMapping("/bms/auth")
public class BmsAuthController {
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @PostMapping("/login")
    public ResponseEntity<BmsLoginResponse> login(@RequestBody BmsLoginRequest request) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword())
            );
            
            // 创建token payload
            Map<String, Object> payload = new HashMap<>();
            payload.put("username", request.getUsername());
            payload.put("userId", 1L);
            payload.put("authorities", getAuthorities(authentication));
            
            // 生成JWT token
            String token = JwtUtils.createToken(payload);
            
            return ResponseEntity.ok(BmsLoginResponse.builder()
                .token(token)
                .username(request.getUsername())
                .authorities(getAuthorities(authentication))
                .build());
                
        } catch (AuthenticationException e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(BmsLoginResponse.error("用户名或密码错误"));
        }
    }
    
    @PostMapping("/logout")
    public ResponseEntity<Void> logout() {
        SecurityContextHolder.clearContext();
        return ResponseEntity.ok().build();
    }
    
    private List<String> getAuthorities(Authentication authentication) {
        return authentication.getAuthorities().stream()
            .map(GrantedAuthority::getAuthority)
            .collect(Collectors.toList());
    }
}

// 请求和响应DTO
@Data
public class BmsLoginRequest {
    private String username;
    private String password;
}

@Data
@Builder
public class BmsLoginResponse {
    private String token;
    private String username;
    private List<String> authorities;
    private String error;
    
    public static BmsLoginResponse error(String error) {
        return BmsLoginResponse.builder().error(error).build();
    }
}
```

### P2 - ExtServiceContext兼容性改造 (2-3小时)

#### 1. LocalServiceContext实现

```java
@Component
public class LocalServiceContext {
    
    private static final ThreadLocal<Long> USER_ID_HOLDER = new ThreadLocal<>();
    
    // 提供与原ServiceContext相同的API
    public static LocalServiceContext getContext() {
        return SpringContextHolder.getBean(LocalServiceContext.class);
    }
    
    public Long getRequestUserId() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            return 1L; // 固定用户ID
        }
        return null;
    }
    
    public void set(String key, Object value) {
        // 空实现，功能已由SecurityContext接管
        // 可以添加日志记录迁移过程
    }
    
    public Object get(String key) {
        // 从配置文件返回固定值或从SecurityContext获取
        return getFixedValue(key);
    }
    
    private Object getFixedValue(String key) {
        // 根据key返回对应的配置值
        // 这里可以添加具体的映射逻辑
        return null;
    }
}

// Spring上下文工具类
@Component
public class SpringContextHolder implements ApplicationContextAware {
    
    private static ApplicationContext applicationContext;
    
    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        applicationContext = context;
    }
    
    public static <T> T getBean(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }
}
```

#### 2. 重构ExtServiceContext

```java
@Slf4j
@Component
public class ExtServiceContext {
    
    // 常量定义保持不变
    public static final String ROLE_ID = "yes.req.roleId";
    public static final String USER_CODE = "yes.req.userCode";
    public static final String USER_NAME = "yes.req.userName";
    public static final String POST_CODE = "yes.req.postCode";
    public static final String POST_NAME = "yes.req.postName";
    public static final String USER_ID = "yes.req.userId";
    
    protected static final String[] CONTEXT_KEYS = new String[]{ROLE_ID, USER_CODE, USER_NAME, POST_CODE, POST_NAME};
    
    @Value("${cathay.auth.role-ids}")
    private static String configRoleIds;
    
    @Value("${cathay.auth.user-name}")
    private static String configUserName;
    
    @Value("${cathay.auth.username}")
    private static String configUserCode;
    
    @Value("${cathay.auth.post-code}")
    private static String configPostCode;
    
    @Value("${cathay.auth.post-name}")
    private static String configPostName;
    
    public static String getRoleId() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            return configRoleIds;
        }
        return null;
    }
    
    public static String getUserCode() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            return configUserCode;
        }
        return null;
    }
    
    public static String getUserName() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            log.info("当前的用户名称是,[{}]", configUserName);
            return configUserName;
        }
        return null;
    }
    
    public static String getPostCode() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            return configPostCode;
        }
        return null;
    }
    
    public static String getPostName() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            return configPostName;
        }
        return null;
    }
    
    public static String getHeaderKey(String key) {
        return "x-dtyunxi-context-" + key.toLowerCase();
    }
    
    // 保留向后兼容的方法，但标记为deprecated
    @Deprecated
    public static void put(String key, Object value) {
        log.warn("ExtServiceContext.put()已废弃，请使用Spring Security");
    }
    
    @Deprecated
    public static void init(Map<String, Object> headers) {
        log.warn("ExtServiceContext.init()已废弃，请使用Spring Security");
    }
}
```

#### 3. 移除ExtServiceContextInterceptor

**删除文件：**
- `ExtServiceContextInterceptor.java`

**修改MvcConfiguration：**
```java
@Configuration
public class MvcConfiguration implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS")
                .allowCredentials(true)
                .maxAge(3600)
                .allowedHeaders("*");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 保留微信认证拦截器，但可以考虑迁移到Spring Security
        registry.addInterceptor(wxAuthenticationInterceptor())
                .addPathPatterns("/mobile/**")
                .excludePathPatterns("/mobile/wx/login", "/mobile/callbacks/**");
        // 移除ExtServiceContextInterceptor
    }

    @Bean
    public WxAuthenticationInterceptor wxAuthenticationInterceptor() {
        return new WxAuthenticationInterceptor();
    }
}
```

### P3 - 测试验证 (2-3小时)

#### 1. 功能测试清单

**BMS登录测试：**
```bash
# 正确登录
curl -X POST http://localhost:8080/bms/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 错误密码
curl -X POST http://localhost:8080/bms/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"wrong"}'
```

**JWT认证测试：**
```bash
# 移动端接口访问
curl -X GET http://localhost:8080/mobile/some-endpoint \
  -H "Authorization: Bearer {jwt-token}"
```

**权限控制测试：**
- 未认证访问被拒绝
- 不同路径的访问控制生效
- 排除路径正常访问

**用户上下文测试：**
- `ExtServiceContext.getUserName()` 返回正确值
- `ExtServiceContext.getRoleId()` 返回配置的角色ID
- 其他上下文方法工作正常

#### 2. 兼容性测试

- 现有业务代码无修改正常运行
- 用户上下文信息正确获取
- Redis缓存机制正常工作
- 分布式锁功能不受影响

#### 3. 安全测试

- 错误凭据访问被拒绝
- JWT token验证正确
- 会话管理无状态
- CSRF攻击防护

## 三. 迁移策略

### 渐进式迁移路径

1. **阶段1 (P0)**: 移除依赖，保持现有功能
2. **阶段2 (P1)**: 引入Spring Security，并行运行
3. **阶段3 (P2)**: 迁移认证逻辑，测试验证
4. **阶段4 (P3)**: 移除旧机制，完成迁移

### 向后兼容保证

- ExtServiceContext API完全保持不变
- 业务代码零修改
- 配置驱动的用户信息管理
- 现有的JWT机制继续工作

### 回滚策略

- 保留原有代码的备份分支
- 分阶段提交，便于问题定位
- 关键节点设置回滚点

## 四. 技术优势

### 1. 架构现代化
- 从手工认证升级到Spring Security标准
- 支持多种认证方式 (JWT + 用户名密码)
- 统一的安全上下文管理

### 2. 扩展性强
- 为未来多用户功能打基础
- 支持OAuth2、LDAP等扩展
- 灵活的权限控制机制

### 3. 安全性提升
- 利用Spring Security的安全特性
- 标准化的会话管理
- CSRF、CORS等安全防护

### 4. 维护性好
- 标准化的认证授权机制
- 清晰的配置和扩展点
- 丰富的文档和社区支持

## 五. 时间预估

| 阶段 | 任务 | 预估时间 | 关键点 |
|------|------|----------|---------|
| P0 | 基础依赖清理 | 3-4小时 | 配置迁移、代码清理 |
| P1 | Spring Security集成 | 4-5小时 | 认证配置、JWT集成 |
| P2 | 兼容性改造 | 2-3小时 | ExtServiceContext重构 |
| P3 | 测试验证 | 2-3小时 | 功能测试、安全测试 |

**总计：11-15小时**

## 六. 风险评估

### 低风险项 ✅
- cube-starter-lock保留：无需修改分布式锁逻辑
- 固定用户：简化了用户管理复杂度
- JWT机制：已验证可用

### 中等风险项 ⚠️
- Spring Security配置：需要仔细调试路径匹配
- 双认证机制：需要确保移动端和BMS都正常工作
- ExtServiceContext改造：需要充分测试兼容性

### 缓解措施
- 渐进式迁移，每个阶段充分测试
- 保持向后兼容，最小化业务代码影响
- 详细的集成测试覆盖
- 设置回滚点和应急方案

## 七. 后续发展路径

### 短期目标 (当前方案)
- 移除微服务依赖
- 建立标准认证体系
- 支持固定用户认证

### 中期目标 (3-6个月)
- 支持多用户管理
- 基于数据库的用户存储
- 角色权限精细化控制

### 长期目标 (6-12个月)
- OAuth2/OIDC集成
- 单点登录(SSO)
- 审计日志和安全监控

## 八. 配置参考

### application.yml完整配置
```yaml
spring:
  main:
    allow-bean-definition-overriding: true
  aop:
    proxy-target-class: true
  application:
    name: cathayfuture-opm

# 固定用户配置
cathay:
  auth:
    username: admin
    password: admin123
    user-id: 1
    role-ids: "1,2,3"
    user-name: "系统管理员"
    post-code: "ADMIN"
    post-name: "超级管理员"

# Feign配置
feign:
  httpclient:
    enabled: false
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 150000
        readTimeout: 150000

# 其他现有配置保持不变...
```

## 九. 总结

本方案在移除微服务依赖的同时，将认证架构现代化升级到Spring Security，既满足了当前的简化需求，又为未来的扩展奠定了坚实基础。

**关键成功因素：**
1. 保持向后兼容性，最小化业务代码变更
2. 渐进式迁移，降低实施风险
3. 充分的测试覆盖，确保功能正确性
4. 清晰的架构设计，便于后续维护和扩展

**预期收益：**
1. 简化系统架构，减少外部依赖
2. 提升系统安全性和可维护性
3. 为未来功能扩展打好基础
4. 提高开发效率和系统稳定性