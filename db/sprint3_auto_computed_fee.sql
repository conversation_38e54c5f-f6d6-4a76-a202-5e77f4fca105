CREATE TABLE `cf_stu_expense_standard`
(
    `id`                      int(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `student_id`              int(11)  NOT NULL COMMENT '学生id',
    `student_no`              varchar(32)  NOT NULL COMMENT '学号',
    `expense_type`                int(4)  NOT NULL COMMENT '费用类型（0保育费，1餐费，2定金，3代收费，4杂费）',
    `expense_standard_type`               int(4)  NOT NULL COMMENT '费用标准类型(0默认值，1自定义)',
    `expense_standard`         decimal(24,2) NOT NULL COMMENT '费用标准',
    `create_time`             datetime     DEFAULT NULL COMMENT '创建时间',
    `create_person`           varchar(20)  DEFAULT NULL COMMENT '创建人',
    `update_time`             datetime     DEFAULT NULL COMMENT '更新时间',
    `update_person`           varchar(20)  DEFAULT NULL COMMENT '更新人',
    `dr`                      int(4) DEFAULT '0' COMMENT '逻辑删除标志',
    `tenant_id`               int(20) DEFAULT NULL COMMENT '租户号',
    `extension`               varchar(1024) DEFAULT NULL COMMENT '扩展字段',
    `instance_id`             int(20) COMMENT '应用实例ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='费用标准表';