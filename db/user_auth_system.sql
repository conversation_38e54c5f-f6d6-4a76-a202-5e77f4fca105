-- 用户认证系统数据库表结构
-- 创建时间: 2025-08-12
-- 作者: ChengZhenxing

-- 1. 系统用户表
CREATE TABLE `sys_user` (
    `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `user_code` varchar(50) DEFAULT NULL COMMENT '用户编码',
    `password` varchar(100) NOT NULL COMMENT '密码(BCrypt加密)',
    `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
    `oa_name` varchar(50) DEFAULT NULL COMMENT 'OA姓名',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
    `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
    `password_update_time` datetime DEFAULT NULL COMMENT '密码更新时间',
    `login_failure_count` int(11) DEFAULT '0' COMMENT '登录失败次数',
    `account_locked_time` datetime DEFAULT NULL COMMENT '账户锁定时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_person` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_person` varchar(50) DEFAULT NULL COMMENT '更新人',
    `dr` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常 1:删除)',
    `tenant_id` int(20) DEFAULT NULL COMMENT '租户ID',
    `instance_id` int(20) DEFAULT NULL COMMENT '实例ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_user_code` (`user_code`),
    UNIQUE KEY `uk_email` (`email`),
    KEY `idx_status` (`status`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';

-- 2. 系统角色表
CREATE TABLE `sys_role` (
    `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `role_code` varchar(50) NOT NULL COMMENT '角色编码',
    `role_name` varchar(50) NOT NULL COMMENT '角色名称',
    `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_person` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_person` varchar(50) DEFAULT NULL COMMENT '更新人',
    `dr` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常 1:删除)',
    `tenant_id` int(20) DEFAULT NULL COMMENT '租户ID',
    `instance_id` int(20) DEFAULT NULL COMMENT '实例ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_code` (`role_code`),
    KEY `idx_status` (`status`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统角色表';

-- 3. 用户角色关联表
CREATE TABLE `sys_user_role` (
    `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` int(20) NOT NULL COMMENT '用户ID',
    `role_id` int(20) NOT NULL COMMENT '角色ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_person` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_person` varchar(50) DEFAULT NULL COMMENT '更新人',
    `dr` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常 1:删除)',
    `tenant_id` int(20) DEFAULT NULL COMMENT '租户ID',
    `instance_id` int(20) DEFAULT NULL COMMENT '实例ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 4. 系统权限表
CREATE TABLE `sys_permission` (
    `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
    `permission_code` varchar(100) NOT NULL COMMENT '权限编码',
    `permission_name` varchar(100) NOT NULL COMMENT '权限名称',
    `permission_type` tinyint(1) NOT NULL COMMENT '权限类型(1:菜单 2:按钮 3:接口)',
    `parent_id` int(20) DEFAULT '0' COMMENT '父权限ID',
    `path` varchar(255) DEFAULT NULL COMMENT '路径',
    `component` varchar(255) DEFAULT NULL COMMENT '组件',
    `icon` varchar(100) DEFAULT NULL COMMENT '图标',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_person` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_person` varchar(50) DEFAULT NULL COMMENT '更新人',
    `dr` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常 1:删除)',
    `tenant_id` int(20) DEFAULT NULL COMMENT '租户ID',
    `instance_id` int(20) DEFAULT NULL COMMENT '实例ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_code` (`permission_code`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_status` (`status`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统权限表';

-- 5. 角色权限关联表
CREATE TABLE `sys_role_permission` (
    `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `role_id` int(20) NOT NULL COMMENT '角色ID',
    `permission_id` int(20) NOT NULL COMMENT '权限ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_person` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_person` varchar(50) DEFAULT NULL COMMENT '更新人',
    `dr` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常 1:删除)',
    `tenant_id` int(20) DEFAULT NULL COMMENT '租户ID',
    `instance_id` int(20) DEFAULT NULL COMMENT '实例ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_permission_id` (`permission_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- ========================================
-- 初始化数据
-- ========================================

-- 插入默认角色
INSERT INTO `sys_role` (`role_code`, `role_name`, `description`, `status`, `sort_order`) VALUES
('ROLE_ADMIN', '超级管理员', '系统超级管理员，拥有所有权限', 1, 1),
('ROLE_MANAGER', '业务管理员', '业务管理员，拥有业务相关权限', 1, 2);

-- 插入默认用户（密码为BCrypt加密后的值）
-- admin123 -> $2b$12$MVx18fHaQf7TQeE83VfdkufS6FtsSByhksl1p4dcsNCJUQhbG5tZa
-- manager123 -> $2b$12$JKSCJlAHO.5sb70kjhqxoeGZEcj1NNPT8aY3g/D3B3M1V5sh69/Ui
INSERT INTO `sys_user` (`username`, `user_code`, `password`, `real_name`, `oa_name`, `status`, `password_update_time`) VALUES
('admin', 'ADMIN001', '$2b$12$MVx18fHaQf7TQeE83VfdkufS6FtsSByhksl1p4dcsNCJUQhbG5tZa', '系统管理员', '系统管理员', 1, NOW()),
('manager', 'MGR001', '$2b$12$JKSCJlAHO.5sb70kjhqxoeGZEcj1NNPT8aY3g/D3B3M1V5sh69/Ui', '业务管理员', '业务管理员', 1, NOW());

-- 建立用户角色关联
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES
(1, 1), -- admin用户关联超级管理员角色
(2, 2); -- manager用户关联业务管理员角色

-- 插入基础权限
INSERT INTO `sys_permission` (`permission_code`, `permission_name`, `permission_type`, `parent_id`, `path`, `sort_order`, `status`) VALUES
-- 系统管理
('system', '系统管理', 1, 0, '/system', 1, 1),
('system:user', '用户管理', 1, 1, '/system/user', 1, 1),
('system:user:list', '用户列表', 3, 2, NULL, 1, 1),
('system:user:add', '新增用户', 2, 2, NULL, 2, 1),
('system:user:edit', '编辑用户', 2, 2, NULL, 3, 1),
('system:user:delete', '删除用户', 2, 2, NULL, 4, 1),
('system:role', '角色管理', 1, 1, '/system/role', 2, 1),
('system:role:list', '角色列表', 3, 7, NULL, 1, 1),
('system:role:add', '新增角色', 2, 7, NULL, 2, 1),
('system:role:edit', '编辑角色', 2, 7, NULL, 3, 1),
('system:role:delete', '删除角色', 2, 7, NULL, 4, 1),
-- 业务管理
('business', '业务管理', 1, 0, '/business', 2, 1),
('business:student', '学生管理', 1, 12, '/business/student', 1, 1),
('business:student:list', '学生列表', 3, 13, NULL, 1, 1),
('business:student:add', '新增学生', 2, 13, NULL, 2, 1),
('business:student:edit', '编辑学生', 2, 13, NULL, 3, 1),
('business:order', '订单管理', 1, 12, '/business/order', 2, 1),
('business:order:list', '订单列表', 3, 17, NULL, 1, 1),
('business:order:add', '新增订单', 2, 17, NULL, 2, 1),
('business:order:edit', '编辑订单', 2, 17, NULL, 3, 1);

-- 为超级管理员角色分配所有权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`)
SELECT 1, id FROM `sys_permission` WHERE `status` = 1;

-- 为业务管理员角色分配业务相关权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`)
SELECT 2, id FROM `sys_permission` WHERE `permission_code` LIKE 'business%' AND `status` = 1;
