-- 测试新增字段的功能
-- 创建时间: 2025-08-12

-- 1. 查看当前用户表结构
DESC sys_user;

-- 2. 查看现有用户数据
SELECT id, username, user_code, real_name, oa_name, email, status 
FROM sys_user 
WHERE dr = 0;

-- 3. 测试插入新用户（包含新字段）
INSERT INTO sys_user (username, user_code, password, real_name, oa_name, email, status, password_update_time) 
VALUES ('testuser', 'TEST001', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKTcn5Uy6Q7J8yNWNQynVf2.Ek/2', '测试用户', '测试用户OA', '<EMAIL>', 1, NOW());

-- 4. 验证插入结果
SELECT id, username, user_code, real_name, oa_name, email, status 
FROM sys_user 
WHERE username = 'testuser';

-- 5. 测试用户编码唯一性约束
-- 以下语句应该会失败（用户编码重复）
-- INSERT INTO sys_user (username, user_code, password, real_name, status) 
-- VALUES ('testuser2', 'TEST001', 'password', '测试用户2', 1);

-- 6. 测试根据用户编码查询
SELECT * FROM sys_user WHERE user_code = 'ADMIN001';

-- 7. 测试模糊查询
SELECT id, username, user_code, real_name, oa_name 
FROM sys_user 
WHERE user_code LIKE '%001%' OR oa_name LIKE '%管理员%';

-- 8. 清理测试数据
DELETE FROM sys_user WHERE username = 'testuser';
