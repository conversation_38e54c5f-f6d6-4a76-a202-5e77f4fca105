-- 用户认证系统数据库迁移脚本
-- 为现有的sys_user表添加user_code和oa_name字段
-- 创建时间: 2025-08-12
-- 作者: Cheng<PERSON>henxing

-- 检查表是否存在，如果不存在则先创建完整的表结构
CREATE TABLE IF NOT EXISTS `sys_user` (
    `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(100) NOT NULL COMMENT '密码(BCrypt加密)',
    `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
    `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
    `password_update_time` datetime DEFAULT NULL COMMENT '密码更新时间',
    `login_failure_count` int(11) DEFAULT '0' COMMENT '登录失败次数',
    `account_locked_time` datetime DEFAULT NULL COMMENT '账户锁定时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_person` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_person` varchar(50) DEFAULT NULL COMMENT '更新人',
    `dr` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常 1:删除)',
    `tenant_id` int(20) DEFAULT NULL COMMENT '租户ID',
    `instance_id` int(20) DEFAULT NULL COMMENT '实例ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    KEY `idx_status` (`status`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';

-- 添加user_code字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'sys_user' 
     AND COLUMN_NAME = 'user_code') = 0,
    'ALTER TABLE sys_user ADD COLUMN user_code varchar(50) DEFAULT NULL COMMENT ''用户编码'' AFTER username',
    'SELECT ''user_code字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加oa_name字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'sys_user' 
     AND COLUMN_NAME = 'oa_name') = 0,
    'ALTER TABLE sys_user ADD COLUMN oa_name varchar(50) DEFAULT NULL COMMENT ''OA姓名'' AFTER real_name',
    'SELECT ''oa_name字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加user_code的唯一索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'sys_user' 
     AND INDEX_NAME = 'uk_user_code') = 0,
    'ALTER TABLE sys_user ADD UNIQUE KEY uk_user_code (user_code)',
    'SELECT ''uk_user_code索引已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有用户的user_code和oa_name字段（如果为空）
UPDATE sys_user 
SET user_code = CASE 
    WHEN username = 'admin' THEN 'ADMIN001'
    WHEN username = 'manager' THEN 'MGR001'
    ELSE CONCAT('USER', LPAD(id, 3, '0'))
END,
oa_name = CASE 
    WHEN real_name IS NOT NULL AND real_name != '' THEN real_name
    WHEN username = 'admin' THEN '系统管理员'
    WHEN username = 'manager' THEN '业务管理员'
    ELSE username
END
WHERE (user_code IS NULL OR user_code = '') 
   OR (oa_name IS NULL OR oa_name = '');

-- 验证迁移结果
SELECT 
    '迁移完成' as status,
    COUNT(*) as total_users,
    COUNT(CASE WHEN user_code IS NOT NULL AND user_code != '' THEN 1 END) as users_with_code,
    COUNT(CASE WHEN oa_name IS NOT NULL AND oa_name != '' THEN 1 END) as users_with_oa_name
FROM sys_user 
WHERE dr = 0;

-- 显示表结构确认
SHOW CREATE TABLE sys_user;
