# Swagger使用指南 - 华夏未来OPM系统

## 问题说明

如果在Swagger中执行接口时出现403错误，这是因为系统实现了基于Redis的JWT认证机制。所有受保护的接口都需要有效的JWT token。

## 解决方案

### 步骤1：获取JWT Token

1. 在Swagger UI中找到 **"BMS认证"** 分组
2. 展开 `/bms/auth/login` 接口
3. 点击 **"Try it out"**
4. 输入登录信息：
   ```json
   {
     "username": "admin",
     "password": "admin123"
   }
   ```
   或者：
   ```json
   {
     "username": "manager", 
     "password": "manager123"
   }
   ```
5. 点击 **"Execute"**
6. 从响应中复制 `token` 字段的值

### 步骤2：配置JWT认证

1. 在Swagger UI页面顶部找到 **"Authorize"** 按钮（锁形图标）
2. 点击 **"Authorize"** 按钮
3. 在弹出的对话框中：
   - 找到 **"bearerAuth (http, Bearer)"** 部分
   - 在 **"Value"** 输入框中输入：`Bearer <你的token>`
   - 例如：`Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...`
4. 点击 **"Authorize"**
5. 点击 **"Close"**

### 步骤3：执行接口

现在你可以正常执行任何受保护的接口了：

1. 找到你想要测试的接口（如 `/bms/studentClass/queryAllClassNames`）
2. 点击 **"Try it out"**
3. 填写必要的参数（如果有）
4. 点击 **"Execute"**

## 可用的测试用户

| 用户名 | 密码 | 权限 | 描述 |
|--------|------|------|------|
| admin | admin123 | ROLE_ADMIN | 系统管理员，拥有所有权限 |
| manager | manager123 | ROLE_MANAGER | 业务管理员，拥有业务权限 |

## 接口分类

### 公开接口（无需认证）
- `/bms/auth/**` - 认证相关接口
- `/swagger-ui/**` - Swagger UI
- `/v3/api-docs/**` - API文档
- `/actuator/**` - 监控端点

### 受保护接口（需要JWT认证）
- `/bms/**` - 后台管理系统接口
- `/mobile/**` - 移动端接口

## 常见问题

### Q1: 为什么我配置了token还是403？
**A:** 检查以下几点：
1. Token是否正确复制（包含完整的JWT字符串）
2. 在Authorize对话框中是否添加了 `Bearer ` 前缀
3. Token是否已过期（JWT token有效期为7天）
4. 是否在正确的环境中测试

### Q2: Token过期了怎么办？
**A:** 重新执行步骤1获取新的token，或者使用refresh token接口：
- 接口：`/bms/auth/refresh`
- 参数：`{"refreshToken": "你的refresh_token"}`

### Q3: 登出后token还能用吗？
**A:** 不能。系统实现了真正的登出功能：
- 登出后token会从Redis中删除
- 该token立即失效，无法再访问任何接口
- 需要重新登录获取新token

## 技术说明

系统采用了基于Redis的JWT Token管理机制：

1. **登录流程**：
   - 验证用户名密码
   - 生成JWT token
   - 将token存储到Redis（7天过期）
   - 返回token和refreshToken

2. **认证流程**：
   - 验证JWT签名和过期时间
   - 检查token是否在Redis中存在
   - 两项验证都通过才允许访问

3. **登出流程**：
   - 从Redis中删除token
   - Token立即失效

这种设计既保持了JWT的无状态特性，又实现了真正的token失效控制。

## 访问地址

- **Swagger UI**: http://localhost:8080/swagger-ui/index.html
- **API文档**: http://localhost:8080/v3/api-docs

## 示例截图说明

1. **登录接口位置**：在Swagger UI中找到"BMS认证"分组
2. **Authorize按钮**：在页面右上角，锁形图标
3. **Token配置**：在弹出对话框中输入 `Bearer <token>`
4. **成功标识**：配置成功后，锁图标会变为已锁定状态

按照以上步骤操作，你就可以在Swagger中正常测试所有接口了！
