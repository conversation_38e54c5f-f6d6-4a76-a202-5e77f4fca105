package cathayfuture.opm.domain.attendance.repository;

import cathayfuture.opm.domain.attendance.AskedForLeaveEntity;

import java.util.List;

public interface AskedForLeaveRepository {
    AskedForLeaveEntity getByid(Integer id);

    Boolean add(AskedForLeaveEntity entity);

    Boolean delete(Integer id);

    Boolean updateById(AskedForLeaveEntity entity);


    List<AskedForLeaveEntity> findListByStuId(Integer stuId);

    List<AskedForLeaveEntity> findListByStuIds(List<Integer> stuIds);
}
