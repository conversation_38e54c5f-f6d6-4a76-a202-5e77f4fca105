package cathayfuture.opm.domain.attendance.ennums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 请假类别
 * @date 2023/3/27 13:39
 */
@Getter
@AllArgsConstructor
public enum AskedForLeaveTypeEnum {
    /**
     * 临时
     */
    temporary(0,"临时"),
    /**
     * 长期
     */
    long_term(1,"长期"),
    ;

    private final int key;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values())
                .filter(value -> Objects.equals(value.getKey(), key))
                .findFirst()
                .orElseThrow(NoSuchElementException::new)
                .getDescription();
    }
}
