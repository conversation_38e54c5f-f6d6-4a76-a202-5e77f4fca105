package cathayfuture.opm.domain.attendance;

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 考勤记录
 * @date 2023/3/31 17:03
 */
@Data
@TableName("of_stu_attendance_record")
public class AttendanceRecordEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 6398939715325582541L;
    /**
     * 学生表id
     */
    @TableField("student_id")
    private Integer studentId;
    /**
     * 考勤日期
     */
    @TableField("attendance_date")
    private LocalDate attendanceDate;
    /**
     * 寒暑假出勤标记
     */
    @TableField("holiday_flg")
    private Integer holidayFlg;
    /**
     * 长期病假出勤标记
     */
    @TableField("long_term_flg")
    private Integer longTermFlg;
    /**
     * 正常出勤标记
     */
    @TableField("normal_flg")
    private Integer normalFlg;

    /**
     * 正常出勤标记
     */
    @TableField("am_attendance")
    private Integer amAttendance;
    /**
     * 正常出勤标记
     */
    @TableField("noon_attendance")
    private Integer noonAttendance;
    /**
     * 正常出勤标记
     */
    @TableField("pm_attendance")
    private Integer pmAttendance;
    /**
     * 正常出勤标记
     */
    @TableField("remark")
    private String remark;
    /**
     * 正常出勤标记
     */
    @TableField("allergen")
    private String allergen;

}
