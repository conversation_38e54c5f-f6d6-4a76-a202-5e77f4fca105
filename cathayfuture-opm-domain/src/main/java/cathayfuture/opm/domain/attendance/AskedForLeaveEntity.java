package cathayfuture.opm.domain.attendance;

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 20220824
 */
@Data
@TableName("of_stu_asked_for_leave")
public class AskedForLeaveEntity extends BaseEntity implements Serializable {


    private static final long serialVersionUID = -7895288971068455457L;
    /**
     * 学生信息表id
     */
    @TableField("stu_id")
    private Integer stuId;
    /**
     * 类别（0其他，1长期）
     */
    @TableField("type")
    private Integer type;
    /**
     * 请假开始时间
     */
    @TableField("start_date")
    private LocalDate startDate;
    /**
     * 请假结束时间
     */
    @TableField("end_date")
    private LocalDate endDate;
    /**
     * 销假开始时间
     */
    @TableField("return_start_date")
    private LocalDate returnStartDate;
    /**
     * 销假结束时间
     */
    @TableField("return_end_date")
    private LocalDate returnEndDate;
    /**
     * 实际请假开始时间
     */
    @TableField("real_start_date")
    private LocalDate realStartDate;
    /**
     * 实际请假结束时间
     */
    @TableField("real_end_date")
    private LocalDate realEndDate;


    @Override
    public AskedForLeaveEntity clone() throws CloneNotSupportedException {
        return (AskedForLeaveEntity) super.clone();
    }


}
