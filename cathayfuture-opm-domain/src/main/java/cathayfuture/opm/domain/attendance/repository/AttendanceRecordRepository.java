package cathayfuture.opm.domain.attendance.repository;

import cathayfuture.opm.domain.attendance.AttendanceRecordEntity;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface AttendanceRecordRepository {
    void add(AttendanceRecordEntity entity);

    Integer batchInsert(List<AttendanceRecordEntity> entityList);

    void update(AttendanceRecordEntity entity);

    List<AttendanceRecordEntity> queryByStudentIdAndCurrentDate(Integer studentId, LocalDate currentDate);

    List<AttendanceRecordEntity> exist(List<Integer> studentId, LocalDate currentDate);

    /**
     * 获取对应月份的凯琴数据，date为null时获取的是当前月份数据，不为null时为指定日期所属月份的数据
     * @param date
     * @return
     */
    List<AttendanceRecordEntity> getListByTargetMonth(LocalDate date);

    List<LocalDate> queryListByLocalDateList(List<LocalDate> list, Integer studentId);

    Map<Integer, LocalDate> queryStudentFirstDayMap();
}
