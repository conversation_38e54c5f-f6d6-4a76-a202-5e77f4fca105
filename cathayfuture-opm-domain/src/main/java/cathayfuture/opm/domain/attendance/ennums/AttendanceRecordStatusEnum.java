package cathayfuture.opm.domain.attendance.ennums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 考勤记录状态枚举
 */
@Getter
@AllArgsConstructor
public enum AttendanceRecordStatusEnum {

    /**
     * 缺勤
     */
    ABSENCE_FROM_DUTY(0,"缺勤"),

    /**
     * 出勤
     */
    ATTENDANCE(1,"出勤"),
    ;

    private final int key;
    private final String description;

    public static String getEnumDescription(Integer key) {
        if(Objects.isNull(key)){
            return "";
        }
        return Arrays.stream(values())
                .filter(value -> Objects.equals(value.getKey(), key))
                .findFirst()
                .orElseThrow(NoSuchElementException::new)
                .getDescription();
    }

    /**
     * 长期病假默认值是缺勤，其他默认为出勤
     * @param islongterm 长期病假标记
     * @return
     */
    public static Integer getDefaultKey(Boolean islongterm){
        if(islongterm){
            return ABSENCE_FROM_DUTY.getKey();
        }
        return ATTENDANCE.getKey();
    }

    public static AttendanceRecordStatusEnum computeAttendanceRecordStatus(Integer am,Integer noon,Integer pm){
        boolean amAttendance = Objects.equals(ATTENDANCE.getKey(), am);
        boolean noonAttendance = Objects.equals(ATTENDANCE.getKey(), noon);
        boolean pmAttendance = Objects.equals(ATTENDANCE.getKey(), pm);

        boolean amIsNull = Objects.isNull(am);
        boolean noonIsNull = Objects.isNull(noon);
        boolean pmIsNull = Objects.isNull(pm);

        if(amIsNull || noonIsNull || pmIsNull){
            return null;
        }

        if(amAttendance|| noonAttendance|| pmAttendance){
            return ATTENDANCE;
        }
        if((!amAttendance) && (!noonAttendance) && (!pmAttendance)){
            return ABSENCE_FROM_DUTY;
        }
        return null;
    }

    public static AttendanceRecordStatusEnum computeFlg(Integer am,Integer noon,Integer pm,Integer flg){
        AttendanceRecordStatusEnum statusEnum = computeAttendanceRecordStatus(am, noon, pm);
        if(Objects.isNull(statusEnum) || Objects.isNull(flg)){
            return null;
        }
        //出勤且标记位是1（0否1是）
        if(Objects.equals(ATTENDANCE,statusEnum) && Objects.equals(ATTENDANCE.getKey(),flg)){
            return ATTENDANCE;
        }else {
            return ABSENCE_FROM_DUTY;
        }
    }
}
