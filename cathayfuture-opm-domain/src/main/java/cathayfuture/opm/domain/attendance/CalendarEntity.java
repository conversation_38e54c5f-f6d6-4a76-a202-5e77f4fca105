package cathayfuture.opm.domain.attendance;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 日历
 * @date 2023/4/7 14:45
 */

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@TableName("of_stu_calendar")
public class CalendarEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -8170138710339287299L;

    @TableField("date")
    private LocalDate date;
    @TableField("weekend")
    private Integer weekend;
    @TableField("holiday")
    private Integer holiday;
    @TableField("close")
    private Integer close;
    @TableField("weekday")
    private Integer weekday;
    @TableField("remark")
    private String remark;
    @TableField("version")
    private String version;
}
