package cathayfuture.opm.domain.attendance.ennums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 请假状态
 * @date 2023/3/27 13:39
 */

@Getter
@AllArgsConstructor
public enum AskedForLeaveStatusEnum {
    /**
     * 未开始
     */
    NOT_STARTED(0,"未开始"),
    /**
     * 请假中
     */
    ON_LEAVE(1,"请假中"),
    /**
     * 已结束
     */
    FINISHED(2,"已结束"),
    /**
     * 已销假
     */
    RETURNED(3,"已销假"),

    /**
     * 全部
     */
    ALL(99,"全部"),
    ;
    private final int key;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values())
                .filter(value -> Objects.equals(value.getKey(), key))
                .findFirst()
                .orElseThrow(NoSuchElementException::new).getDescription();
    }
    public static AskedForLeaveStatusEnum getEnumByKey(Integer key) {
        if(Objects.isNull(key)){
            return ALL;
        }
        return Arrays.stream(values())
                .filter(value -> Objects.equals(value.getKey(), key))
                .findFirst()
                .orElseThrow(NoSuchElementException::new);
    }

    public static AskedForLeaveStatusEnum getEnumByCurrentTime(LocalDate start,LocalDate end,LocalDate returnStartDate){
        if(Objects.nonNull(returnStartDate)){
            return RETURNED;
        }

        boolean after = LocalDate.now().isAfter(end);
        boolean before = LocalDate.now().isBefore(start);

        if(after){
            return FINISHED;
        }
        if(before){
            return NOT_STARTED;
        }

        if((!after) && (!before)){
            return ON_LEAVE;
        }
        return null;
    }

}
