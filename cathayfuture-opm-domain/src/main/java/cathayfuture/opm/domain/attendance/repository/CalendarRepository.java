package cathayfuture.opm.domain.attendance.repository;

import cathayfuture.opm.domain.attendance.CalendarEntity;

import java.time.LocalDate;
import java.util.List;

public interface CalendarRepository {
    CalendarEntity findOneByDate(LocalDate date);

    /**
     * 获取对应月份的日历数据，date为null为获取当前月数据，不是null则为date对应月份数据
     * @param date
     * @return
     */
    List<CalendarEntity> getTargetMonthDays(LocalDate date);

    List<CalendarEntity> getTargetMonthDays(LocalDate firstDay, LocalDate lastDay);

    String queryVersion(LocalDate targetDate);
}
