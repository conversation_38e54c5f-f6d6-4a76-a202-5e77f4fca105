package cathayfuture.opm.domain.order.gateway.repository;

import cathayfuture.opm.domain.order.PaymentDetailEntity;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/24/22
 */
public interface PaymentDetailRepository {

    PaymentDetailEntity add(PaymentDetailEntity paymentDetail);

    /**
     * 根据订单ID获取最新的支付明细信息
     * @param orderId
     * @return
     */
    PaymentDetailEntity getLastNotPayByOrderId(Integer orderId);

    /**
     * 根据pk更新
     * @param paymentDetailEntity
     * @return
     */
    int updateById(PaymentDetailEntity paymentDetailEntity);

    /**
     * 根据订单Id全部退款
     * @param orderId 订单Id
     * @return 退款结果
     */
    Boolean refund(Integer orderId);
}
