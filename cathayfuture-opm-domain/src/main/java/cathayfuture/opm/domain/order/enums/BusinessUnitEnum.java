package cathayfuture.opm.domain.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 描述: 业态
 *
 * <AUTHOR>
 * @date 20220826
 */
@Getter
@AllArgsConstructor
public enum BusinessUnitEnum {

    /**
     * 幼儿园
     */
    KINDERGARTEN(1, "幼儿园")
    ;


    private final int key;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }
}
