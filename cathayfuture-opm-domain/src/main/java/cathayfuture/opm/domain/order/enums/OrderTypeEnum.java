package cathayfuture.opm.domain.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/26/22
 */
@Getter
@AllArgsConstructor
public enum OrderTypeEnum {

    /**
     * 保育
     */
    CHILD_CARE(0, "BY", "保育费"),
    /**
     * 餐费
     */
    DINNER(1, "CF","餐费"),
    /**
     * 定金
     */
    FRONT_MONEY(2, "DJ","定金"),
    /**
     * 代收费
     */
    CHARGE_COLLECTION(3,"DSF","代收费"),
    /**
     * 杂费
     */
    INCIDENTALS(4,"ZF","杂费"),
    ;


    private final int key;
    private final String code;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }

    public static String getEnumCode(int key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getCode();
    }

    public static int getEnumKey(String code) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getCode(), code)).findFirst().orElseThrow(NoSuchElementException::new).getKey();
    }

    public static OrderTypeEnum getEnumByKey(int key){
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new);
    }

}
