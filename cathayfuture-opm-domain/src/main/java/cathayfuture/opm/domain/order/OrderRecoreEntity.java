package cathayfuture.opm.domain.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自动生成记录
 * @date 2023/4/20 9:08
 */

@NoArgsConstructor
@Data
@TableName("of_ord_autogeneration_recore")
public class OrderRecoreEntity extends BaseEntity{
    /**
     * 订单号
     */
    @TableField("order_code")
    private String orderCode;
    /**
     * 订单类别
     */
    @TableField("order_type")
    private Integer orderType;

    @TableField("batch_id")
    private String batchId;
    /**
     * 订单类别
     */
    @TableField("order_amount")
    private BigDecimal orderAmount;
    /**
     * 月份，包括月初月末
     */
    @TableField("date")
    private LocalDate date;
    /**
     * 余额
     */
    @TableField("surplus")
    private BigDecimal surplus;

    /**
     * 减免费用
     */
    @TableField("derate_amount")
    private BigDecimal derateAmount;

    /**
     * 全部寒暑假天数
     */
    @TableField("all_holidays")
    private Integer allHolidays;
    /**
     * 全部开园日天数
     */
    @TableField("all_weekdays")
    private Integer allWeekdays;
    /**
     * 寒暑假比例月初不需要计算寒暑假，所以应该是0
     */
    @TableField("holiday_percentage")
    private String holidayPercentage;
    /**
     * 开园日比例
     */
    @TableField("weekday_percentage")
    private String weekdayPercentage;
    /**
     * 开园日出勤天数
     */
    @TableField("weekdays_attendance")
    private Integer weekdaysAttendance;
    /**
     * 寒暑假出勤天数
     */
    @TableField("holidays_attendance")
    private Integer holidaysAttendance;
    /**
     * 出勤总天数
     */
    @TableField("attendance")
    private Integer attendance;
    /**
     * 学生id
     */
    @TableField("student_id")
    private Integer studentId;
    /**
     * 长期病假，0否1是
     */
    @TableField("long_term_flg")
    private Integer longTermFlg;
    /**
     * 版本号
     */
    @TableField("version")
    private String version;
    /**
     * 收费标准，餐费是每天，保育费是每月
     */
    @TableField("charging_standard")
    private BigDecimal chargingStandard;

    /**
     * 上个月余额（对于当前date来说是上个月，对于预缴来说是上上月）
     */
    @TableField("one_month_ago_surplus")
    private BigDecimal oneMonthAgoSurplus;
    /**
     * 月份总天数（排除法定节假日和周末）
     */
    @TableField("all_days")
    private Integer allDays;






}
