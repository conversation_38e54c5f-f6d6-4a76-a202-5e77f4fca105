package cathayfuture.opm.domain.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自动生成记录详情
 * @date 2023/4/20 9:09
 */

@NoArgsConstructor
@TableName("of_ord_autogeneration_record_detail")
public class OrderRecordDetailEntity extends BaseEntity{
    /**
     * 订单号
     */
    @TableField("order_code")
    private String orderCode;
    /**
     * 代表月份和月初和月末
     */
    @TableField("date")
    private LocalDate date;
    /**
     * 月份工作日数量
     */
    @TableField("weekdays")
    private Integer weekdays;
    /**
     * 法月份定假日数量
     */
    @TableField("official_holidays")
    private Integer officialHolidays;
    /**
     * 月份寒暑假数量
     */
    @TableField("semesterferien")
    private Integer semesterferiens;
    /**
     * 月份闭园日数量
     */
    @TableField("close")
    private Integer close;

}
