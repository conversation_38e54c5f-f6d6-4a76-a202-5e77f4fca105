package cathayfuture.opm.domain.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/26/22
 */
@Getter
@AllArgsConstructor
public enum PaymentStatusEnum {
    /**
     * 未支付
     */
    NO_PAY(0, "未支付"),
    /**
     * 已支付
     */
    PAID(1,"已支付"),

    /**
     * 已退款
     */
    REFUND(2,"已退款"),

    /**
     * 无需支付
     */
    NEEDLESS(3,"无需支付")
    ;

    private final int key;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }

}
