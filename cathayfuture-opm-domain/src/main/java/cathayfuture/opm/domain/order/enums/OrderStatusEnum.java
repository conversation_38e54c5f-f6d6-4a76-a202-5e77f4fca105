package cathayfuture.opm.domain.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/26/22
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    /**
     * 正常状态
     */
    NORMAL(0, "正常"),
    /**
     * 作废状态
     */
    DELETED(1, "作废");

    private final int key;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }


}
