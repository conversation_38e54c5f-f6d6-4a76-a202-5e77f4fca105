package cathayfuture.opm.domain.order;

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单月结操作人记录表
 * @date 2023/6/9 16:23
 */
@Data
@TableName("of_order_operation_record")
public class OrderOperationRecordEntity extends BaseEntity {
    /**
     * 订单类别
     */
    @TableField("type")
    private Integer type;
    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;
    /**
     * 批次号
     */
    @TableField("batch_code")
    private String batchCode;
    /**
     * 操作类型
     */
    @TableField("operation_type")
    private Integer operationType;
}
