package cathayfuture.opm.domain.order.enums;

import cathayfuture.opm.domain.attendance.ennums.AttendanceRecordFlgEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import sun.awt.image.PixelConverter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.Objects;

/**
 * 自动生成订单比例
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ProportionEnum {
    /**
     * 20%
     */
    TWENTY_PERCENT(0,"20%",new BigDecimal("0.2")),
    /**
     * 50%
     */
    FIFTY_PERCENT(1,"50%",new BigDecimal("0.5")),
    /**
     * 100%
     */
    ONE_HUNDRED_PERCENT(2,"100%",new BigDecimal("1")),
    /**
     * null，当月入园的取此值
     */
    ZERO_PERCENT(3,"0%",BigDecimal.ZERO)
    ;

    private final int key;
    private final String description;
    private final BigDecimal value;

    /**
     * 保育费获取寒暑假比例，假期出勤天数>0,寒暑假按照100%收费；月份的日历寒暑假总天数=0，按照0%收费 ;否则 按照20%收费
     * @param days 寒暑假出勤
     * @param admissionDate  学生注册日期
     * @param targetDate
     * @param holidays 在targetDate所在月份里的寒暑假天数
     * @return
     */
    public static ProportionEnum getHolidayProportion(BigDecimal days, LocalDate admissionDate,
                                                             LocalDate targetDate,BigDecimal holidays){
        // 月份里没有一天 寒暑假 的话，比率为0%
        if(holidays.compareTo(BigDecimal.ZERO)==0){
            return ZERO_PERCENT;
        }
/*        LocalDate thisMonthFirstDay = targetDate
                .with(TemporalAdjusters.firstDayOfMonth());*/
        //当月入园
 /*       if(thisMonthFirstDay.getMonthValue() == admissionDate.getMonthValue()
                && thisMonthFirstDay.getYear() == admissionDate.getYear()){
            return ZERO_PERCENT;
        }*/


        if(days.compareTo(BigDecimal.ZERO)==0){
            return TWENTY_PERCENT;
        }


        return ONE_HUNDRED_PERCENT;
    }

    /**
     * 非长期病假状态下：一天没来按 50%计算；只要来一天按 100%计算；
     * @param days 上学日出勤天数
     * @param admissionDate 学生注册日期
     * @param targetDate  目标日期（测试用）
     * @param weekdays  该targetDate所在月份的总上学日（日历）
     * @return
     */
    public static ProportionEnum getWeekdayProportion(BigDecimal days,LocalDate admissionDate,
                                                      LocalDate targetDate,BigDecimal weekdays){
        //todo: 月份里没有任意一天 上学日 的话，比率为0%
        if(weekdays.compareTo(BigDecimal.ZERO)==0){
            return ZERO_PERCENT;
        }
   /*     LocalDate thisMonthFirstDay = targetDate
                .with(TemporalAdjusters.firstDayOfMonth());*/
   /*     if(thisMonthFirstDay.getMonthValue() == admissionDate.getMonthValue()
                && thisMonthFirstDay.getYear() == admissionDate.getYear()){
            return ZERO_PERCENT;
        }*/
        if(days.compareTo(BigDecimal.ZERO)==0){
            return FIFTY_PERCENT;
        }

        return ONE_HUNDRED_PERCENT;
    }

    /**
     * 长期病假计算比例
     * @param allDays 当前月寒暑假+开园日 出勤
     * @param part 对应部分出勤
     * @param flgEnum 日期类型（寒暑假/开园日）
     * @return
     */
    public static ProportionEnum getProportionByLongTerm(BigDecimal allDays, BigDecimal part,
                                                         AttendanceRecordFlgEnum flgEnum, BigDecimal calendarDays){
        //todo: 月份里没有任意一天 寒暑假/上学日 的话，比率为0%
        if(calendarDays.compareTo(BigDecimal.ZERO)==0){
            return ZERO_PERCENT;
        }


        if(allDays.compareTo(BigDecimal.ZERO)==0){
            return TWENTY_PERCENT;
        }
        if (Objects.equals(AttendanceRecordFlgEnum.HOLIDAY,flgEnum) ) {
            if( part.compareTo(BigDecimal.ZERO) == 0) {
                return TWENTY_PERCENT;
            }
        }

        if (Objects.equals(AttendanceRecordFlgEnum.NORMAL,flgEnum) ) {
            if( part.compareTo(BigDecimal.ZERO) == 0){
                return FIFTY_PERCENT;
            }
        }
        return ONE_HUNDRED_PERCENT;
    }
}
