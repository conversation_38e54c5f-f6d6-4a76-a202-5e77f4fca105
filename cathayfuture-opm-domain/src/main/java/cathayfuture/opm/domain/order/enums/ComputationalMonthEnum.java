package cathayfuture.opm.domain.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ComputationalMonthEnum {
    /**
     * 一个月前
     */
    A_MONTH_AGO(0,"一个月前",1),
    /**
     * 两个月前
     */
    TWO_MONTHS_AGO(2,"两个月前",2),
    /**
     * 两个月前
     */
    CURRENT_MONTH(3,"当前月",0),
    ;

    private final int key;
    private final String description;
    private final long  monthsToSubtract;


}
