package cathayfuture.opm.domain.order;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 支付明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("cf_ord_payment_detail")
public class PaymentDetailEntity extends BaseEntity{

    /**
     * 订单ID
     */
    @TableField("order_id")
    private Integer orderId;

    /**
     * 支付类型
     */
    @TableField("payment_type")
    private Integer paymentType;

    /**
     * 支付方式
     */
    @TableField("payment_mode")
    private Integer paymentMode;

    /**
     * pos机号
     */
    @TableField("pos_no")
    private String posNo;

    /**
     * 支付状态
     */
    @TableField("payment_status")
    private Integer paymentStatus;

    /**
     * 支付金额
     */
    @TableField("pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付交易流水号
     */
    @TableField("transaction_id")
    private String transactionId;

    /**
     * 支付人ID
     */
    @TableField("payer_id")
    private Integer payerId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 扩展字段
     */
    @TableField("extension")
    private String extension;

    /**
     * 乐观锁
     */
    @TableField("revision")
    @Version
    private Integer revision;
}
