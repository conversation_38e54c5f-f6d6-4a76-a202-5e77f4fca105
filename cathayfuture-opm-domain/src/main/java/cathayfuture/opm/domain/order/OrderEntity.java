package cathayfuture.opm.domain.order;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("cf_ord_order")
public class OrderEntity extends BaseEntity {

    /**
     * 订单编码
     */
    @TableField("code")
    private String code;

    /**
     * 订单类型（1保育费，2餐费，3代收费，4定金，5杂费）
     */
    @TableField("type")
    private Integer type;

    /**
     * 订单状态（正常，已作废）
     */
    @TableField("status")
    private Integer status;

    /**
     * 批次号
     */
    @TableField("batch_code")
    private String batchCode;

    /**
     * 作废原因
     */
    @TableField("delete_reason")
    private String deleteReason;

    /**
     * 学生ID
     */
    @TableField("student_id")
    private Integer studentId;

    /**
     * 学生编码
     */
    @TableField("student_code")
    private String studentCode;

    /**
     * 学生名称
     */
    @TableField("student_name")
    private String studentName;

    /**
     * 年级
     */
    @TableField("grade")
    private String grade;

    /**
     * 联系方式
     */
    @TableField("contact_info")
    private String contactInfo;

    /**
     * 费用标准
     */
    @TableField("charging_standard")
    private BigDecimal chargingStandard;

    /**
     * 收费项目
     */
    @TableField("charge_item")
    private String chargeItem;

    /**
     * 上期预缴余额
     */
    @TableField("previous_remaining_sum")
    private BigDecimal previousRemainingSum;

    /**
     * 上期出勤天数
     */
    @TableField("previous_attendance_days")
    private Integer previousAttendanceDays;

    /**
     * 上期收费比例
     */
    @TableField("previous_charge_percentage")
    private String previousChargePercentage;

    /**
     * 上期应缴费用
     */
    @TableField("previous_payable_charge")
    private BigDecimal previousPayableCharge;

    /**
     * 上期余额
     */
    @TableField("previous_actual_surplus")
    private BigDecimal previousActualSurplus;

    /**
     * 下期出勤天数
     */
    @TableField("next_attendance_days")
    private Integer nextAttendanceDays;

    /**
     * 下期应收费用
     */
    @TableField("next_payable_charge")
    private BigDecimal nextPayableCharge;

    /**
     * 收费金额（本期应缴费用，下期应收餐费）
     */
    @TableField("charge_amount")
    private BigDecimal chargeAmount;

    /**
     * 支付金额
     */
    @TableField("pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付时间
     */
    @TableField("payment_time")
    private Date paymentTime;

    /**
     * 支付渠道（线上，线下）
     */
    @TableField("payment_channel")
    private Integer paymentChannel;

    /**
     * 支付方式（0线上-微信，1线下-微信，2支付宝，3现金，4银行卡，5余额，6活动减免）
     */
    @TableField("payment_mode")
    private String paymentMode;

    /**
     * 支付状态（未支付，已支付）
     */
    @TableField("payment_status")
    private Integer paymentStatus;

    /**
     * 明细备注
     */
    @TableField("detail_remark")
    private String detailRemark;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 付款方的账号ID
     */
    @TableField("payer")
    private Integer payer;

    /**
     * 发给微信的商户订单号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 费用科目（1,保育费，2,餐费，3,代收费，4,定金，5,杂费）
     */
    @TableField("subject")
    private Integer subject;

    /**
     * 业态
     */
    @TableField("business_forms")
    private Integer businessForms;

    /**
     * 扩展字段
     */
    @TableField("extension")
    private String extension;

    /**
     * 乐观锁
     */
    @TableField("revision")
    @Version
    private Integer revision;

    /**
     * 线下收款人
     */
    @TableField("collection_person")
    private String collectionPerson;

    /**
     * 退款时间
     */
    @TableField("refund_time")
    private LocalDate refundTime;

    /**
     * 退款原因
     */
    @TableField("refund_reason")
    private String refundReason;

    /**
     * 作废时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 作废人员
     */
    @TableField("delete_person")
    private String deletePerson;

    /**
     * 数据来源
     */
    @TableField("data_source")
    private Integer dataSource;
}