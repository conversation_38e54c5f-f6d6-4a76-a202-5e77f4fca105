package cathayfuture.opm.domain.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 描述: 科目
 *
 * <AUTHOR>
 * @date 8/26/22
 */
@Getter
@AllArgsConstructor
public enum SubjectEnum {

    /**
     * 保育
     */
    CHILD_CARE(0, "保育"),
    /**
     * 餐费
     */
    DINNER(1, "餐费"),
    /**
     * 定金
     */
    FRONT_MONEY(2, "定金"),
    /**
     * 代收费
     */
    CHARGE_COLLECTION(3,"代收费"),
    /**
     * 杂费
     */
    INCIDENTALS(4,"杂费"),
    ;


    private final int key;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }
}
