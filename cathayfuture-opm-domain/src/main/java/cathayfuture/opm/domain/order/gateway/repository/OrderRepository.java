package cathayfuture.opm.domain.order.gateway.repository;

import cathayfuture.opm.domain.order.OrderEntity;

import java.time.LocalDate;
import java.util.List;

public interface OrderRepository {


    OrderEntity add(OrderEntity order);

    /**
     *
     * @param orderId
     * @return
     */
    OrderEntity getById(Integer orderId);

    /**
     *
     * @param code
     * @return
     */
    OrderEntity getByCode(String code);

    /**
     *
     * @param orderEntity
     * @return
     */
    int updateById(OrderEntity orderEntity);

    Boolean remove(Integer orderId);

    /**
     * 根据商户订单号查询
     * @param tradeNo
     * @return
     */
    OrderEntity getByTradeNo(String tradeNo);
    Boolean delete(Integer orderId, String deleteReason);

    Integer addBatch(List<OrderEntity> orderEntityList);

    /**
     * 根据订单id退款
     * @param orderId 订单id
     * @param refundTime 退款时间
     * @param refundReason 退款原因
     * @return 退款结果，是否成功
     */
    Boolean refund(Integer orderId, String refundTime, String refundReason);

    List<OrderEntity> getListByCreateTime(LocalDate startDate, LocalDate endDate,Integer type);
}
