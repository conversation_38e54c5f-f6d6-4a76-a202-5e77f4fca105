package cathayfuture.opm.domain.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 订单来源
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderSourceEnum {

    /**
     * 手工导入
     */
    MANUAL_OPERATION(0,"手工导入"),

    /**
     * 系统生成
     */
    SYSTEM_OPERATION(1,"系统生成"),
    ;

    private final int key;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }
}
