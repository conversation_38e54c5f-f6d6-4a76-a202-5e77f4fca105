package cathayfuture.opm.domain.order;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/24/22
 */
@Getter
@Setter
public abstract class BaseEntity {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    private Integer tenantId;

    /**
     * 应用实例ID
     */
    @TableField(value = "instance_id", fill = FieldFill.INSERT)
    private Integer instanceId;

    /**
     * 创建人
     */
    @TableField(value = "create_person",fill = FieldFill.INSERT)
    private String createPerson;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_person", fill = FieldFill.INSERT_UPDATE)
    private String updatePerson;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @TableField("dr")
    @TableLogic
    private Integer dr;
}
