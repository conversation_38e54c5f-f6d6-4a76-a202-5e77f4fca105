package cathayfuture.opm.domain.order.gateway.repository;

import cathayfuture.opm.domain.order.OrderMonthlyStatementEntity;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;

import java.time.LocalDate;
import java.util.List;

/**
 *  订单月结
 * <AUTHOR>
 */
public interface OrderMonthlyStatementRepository {

    /**
     * 查询月结
     * @param studentId
     * @param month
     * @return
     */
    List<OrderMonthlyStatementEntity> findListByStudentIdsAndMonth(List<Integer> studentIds, LocalDate month, OrderTypeEnum type);

    /**
     * 批量新增
     * @param list
     */
    void batchInsert(List<OrderMonthlyStatementEntity> list);
}
