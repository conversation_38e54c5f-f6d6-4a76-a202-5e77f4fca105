package cathayfuture.opm.domain.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2022/8/27
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("cf_ord_id_sequence")
public class IdSequenceEntity extends BaseEntity{

    @TableField("prefix")
    private String prefix;

    @TableField("sequence")
    private Integer sequence;
}
