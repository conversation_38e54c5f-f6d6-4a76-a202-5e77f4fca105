package cathayfuture.opm.domain.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 描述:
 *
 * <AUTHOR>
 * @since 8/30/22
 */
@AllArgsConstructor
@Getter
public enum ExcelUploadTypeEnum {
    /**
     * 保育费订单
     */
    BY_ORDER("BY_ORDER", false),
    /**
     * 餐费订单
     */
    CF_ORDER("CF_ORDER", false),
    /**
     * 代收费订单
     */
    DSF_ORDER("DSF_ORDER", false),
    /**
     * 杂费订单
     */
    ZF_ORDER("ZF_ORDER", false),
    ;


    private final String code;
    private final Boolean async;

    public static ExcelUploadTypeEnum getByCode(String code) {
        return Arrays.stream(ExcelUploadTypeEnum.values()).filter(s -> Objects.equals(s.getCode(), code)).findFirst().orElse(null);
    }
}
