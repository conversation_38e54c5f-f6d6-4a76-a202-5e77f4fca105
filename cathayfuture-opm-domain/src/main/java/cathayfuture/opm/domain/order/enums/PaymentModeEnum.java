package cathayfuture.opm.domain.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/26/22
 */
@Getter
@AllArgsConstructor
public enum PaymentModeEnum {
    /**
     * 线上微信
     */
    WECHAT_ONLINE(0, "线上微信"),
    /**
     * 线下微信
     */
    WECHAT_OFFLINE(1,"线下微信"),
    /**
     * 支付宝
     */
    ALIPAY(2,"支付宝"),
    /**
     *
     */
    CASH(3,"现金"),
    /**
     * 银行卡
     */
    BANK_CARD(4, "银行卡"),
    /**
     *
     */
    REMAINING_SUM(5, "余额"),
    /**
     * 活动减免
     */
    ACTIVITY_REDUCTION(6, "活动减免"),
    ;

    private final int key;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }

}
