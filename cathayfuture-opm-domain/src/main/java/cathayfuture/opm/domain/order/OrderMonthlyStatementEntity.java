package cathayfuture.opm.domain.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单月结
 * @date 2023/5/23 10:54
 */

@Data
@NoArgsConstructor
@TableName("of_order_monthly_statement")
public class OrderMonthlyStatementEntity extends BaseEntity{
    /**
     * 月份
     */
    @TableField("month")
    private LocalDate month;
    /**
     * 订单编码
     */
    @TableField("order_code")
    private String orderCode;
    /**
     * 订单类型（0保育费，1餐费，2定金，3代收费，4杂费）
     */
    @TableField("order_type")
    private Integer orderType;
    /**
     * 学生id
     */
    @TableField("student_id")
    private Integer studentId;
    /**
     * 学生状态（0预在园，1在园，2退园）
     */
    @TableField("student_status")
    private Integer studentStatus;
    /**
     * 寒暑假出勤
     */
    @TableField("holidays")
    private Integer holidays;
    /**
     * 上学日出勤
     */
    @TableField("weekdays")
    private Integer weekdays;
    /**
     * 长期病假标记（0否1是）
     */
    @TableField("long_term_flg")
    private Integer longTermFlg;
    /**
     * 本月结余
     */
    @TableField("current_actual_surplus")
    private BigDecimal currentActualSurplus;
    /**
     * 上月结余
     */
    @TableField("previous_actual_surplus")
    private BigDecimal previousActualSurplus;
    /**
     * 实际应缴费用（根据考勤实际应缴费）
     */
    @TableField("actual_payable_charge")
    private BigDecimal actualPayableCharge;
    /**
     * 订单金额
     */
    @TableField("charge_amount")
    private BigDecimal chargeAmount;
    /**
     * 日历版本号
     */
    @TableField("calendar_version")
    private String calendarVersion;

    /**
     * 费用标准
     */
    @TableField("charging_standard")
    private BigDecimal chargingStandard;
    /**
     * 比率
     */
    @TableField("percentage")
    private String percentage;
    /**
     * 减免
     */
    @TableField("rate_relief")
    private BigDecimal rateRelief;
    /**
     * 批次号
     */
    @TableField("batch_code")
    private String batchCode;

}
