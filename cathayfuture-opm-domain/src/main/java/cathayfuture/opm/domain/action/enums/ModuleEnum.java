package cathayfuture.opm.domain.action.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * Action 模块
 * <AUTHOR>
 * @date 20221013
 */
@Getter
@AllArgsConstructor
public enum ModuleEnum {

    STUDENT("STUDENT", "学生模块"),
    ASKED_FOR_LEAVE("ASKED_FOR_LEAVE","请假管理"),
    RATE_RELIEF("RATE_RELIEF","减免管理"),
    ATTENDANCE_RECORD("ATTENDANCE_RECORD","考勤记录"),
    UNKNOWN("UNKNOWN", "未知"),
    ;
    private final String key;
    private final String description;

    public static String getEnumDescription(String key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }

    public static ModuleEnum getByOrdinal(int ordinal) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.ordinal(), ordinal)).findFirst().orElse(UNKNOWN);
    }
}
