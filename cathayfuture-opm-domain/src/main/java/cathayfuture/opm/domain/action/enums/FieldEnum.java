package cathayfuture.opm.domain.action.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Action 模块
 * <AUTHOR>
 * @date 20221013
 */
@Getter
@AllArgsConstructor
public enum FieldEnum {
    STUDENTNAME("studentName", "学生姓名"),
    STUDENTNO("studentNo", "学号"),
    STUDENTCLASS("studentClass", "班级"),
    ETHNICGROUP("ethnicGroup", "民族"),
    IDNUMBER("idNumber", "身份证号"),
    GENDER("gender", "性别"),
    BIRTHDAY("birthday", "生日"),
    NATIONALITY("nationality", "国籍"),
    CONTACTNAME("contactName", "联系人姓名"),
    CONTACTPHONENUMBER("contactPhoneNumber", "联系手机号"),
    RELATION("relation", "关系"),
    FATHERNAME("fatherName", "父亲姓名"),
    FATHERPHONENUMBER("fatherPhoneNumber", "父亲电话"),
    MOTHERNAME("motherName", "母亲姓名"),
    MOTHERPHONENUMBER("motherPhoneNumber", "母亲电话"),
    ADMISSIONDATE("admissionDate", "入园时间"),
    BUSINESSUNIT("businessUnit", "业态"),
    PROVINCENAME("provinceName", "省名称"),
    CITYNAME("cityName", "市名称"),
    REGIONNAME("regionName", "区名称"),
    HOMEADDRESS("homeAddress", "家庭住址"),
    UNKNOWN("UNKNOWN", "未知"),
    REGISTERSTATUS("registerStatus","学生状态"),
    ;

    private final String key;
    private final String description;

    public static String getEnumDescription(String key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }

    public static FieldEnum getByOrdinal(int ordinal) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.ordinal(), ordinal)).findFirst().orElse(UNKNOWN);
    }

    public static List<String> getAllPropertyList(){
        return Arrays.stream(values()).map(FieldEnum::getKey).collect(Collectors.toList());
    }
}
