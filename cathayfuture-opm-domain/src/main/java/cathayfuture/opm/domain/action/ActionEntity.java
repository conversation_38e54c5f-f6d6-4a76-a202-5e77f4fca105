package cathayfuture.opm.domain.action;

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 20221012
 */
@Data
@TableName("cf_sys_action")
public class ActionEntity extends BaseEntity {

    @TableField("module")
    private String module;
    @TableField("action_id")
    private String actionId;
    @TableField("action_code")
    private String actionCode;
    @TableField("business_id")
    private Integer businessId;
    @TableField("property")
    private String property;
    @TableField("new_val")
    private String newVal;
    @TableField("old_val")
    private String oldVal;

}
