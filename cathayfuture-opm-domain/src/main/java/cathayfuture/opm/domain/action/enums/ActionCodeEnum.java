package cathayfuture.opm.domain.action.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * Action 模块
 * <AUTHOR>
 * @date 20221013
 */
@Getter
@AllArgsConstructor
public enum ActionCodeEnum {
    /**
     * 编辑/修改
     */
    EDIT("EDIT", "编辑/修改"),
    /**
     * 未知
     */
    UNKNOWN("UNKNOWN", "未知"),
    /**
     * 入园操作
     */
    REGISTER("REGISTER","入园操作"),
    /**
     * 退园操作
     */
    QUIT("QUIT","退园操作"),
    /**
     * 新增
     */
    ADD("ADD","新增"),
    /**
     * 删除
     */
    DELETE("DELETE","删除"),
    /**
     * 销假
     */
    RETURN_OPERATE("RETURN_OPERATE","销假")
    ;


    private final String key;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }

    public static ActionCodeEnum getByOrdinal(int ordinal) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.ordinal(), ordinal)).findFirst().orElse(UNKNOWN);
    }
}
