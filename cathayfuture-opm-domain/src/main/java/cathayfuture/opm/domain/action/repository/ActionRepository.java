package cathayfuture.opm.domain.action.repository;
import cathayfuture.opm.domain.action.ActionEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20220824
 */
public interface ActionRepository {

    /**
     * 根据Id查询操作历史
     * @param id
     * @return
     */
    ActionEntity getById(Integer id);

    /**
     * 新增
     * @param ActionEntity
     * @return
     */
    ActionEntity add(ActionEntity ActionEntity);


    /**
     * 根据Id更新
     * @param ActionEntity
     * @return
     */
    int updateById(ActionEntity ActionEntity);


    /**
     * 批量存储
     * @param actionEntityList
     * @return
     */
    Integer addBatch(List<ActionEntity> actionEntityList);
}
