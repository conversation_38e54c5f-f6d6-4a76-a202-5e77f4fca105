package cathayfuture.opm.domain.account;

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ********
 */
@Data
@TableName("cf_acct_r_account_student")
public class RAccountStudentEntity extends BaseEntity {

    @TableField("account_id")
    private Integer accountId;

    @TableField("student_id")
    private Integer studentId;

}
