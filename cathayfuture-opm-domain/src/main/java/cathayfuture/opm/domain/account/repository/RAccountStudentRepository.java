package cathayfuture.opm.domain.account.repository;

import cathayfuture.opm.domain.account.RAccountStudentEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date ********
 */
public interface RAccountStudentRepository {

    /**
     * 根据account查询关系
     * @param accountId
     * @return
     */
    List<RAccountStudentEntity> listByAccountId(Integer accountId);

    /**
     * 批量插入用户、学生绑定关系
     * @param entityList
     * @return
     */
    int insertBatch(List<RAccountStudentEntity> entityList);

    /**
     * 根据student查询关系
     * @param studentId
     * @return
     */
    List<RAccountStudentEntity> listByStudentId(Integer studentId);
}
