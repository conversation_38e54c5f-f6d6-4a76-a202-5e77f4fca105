package cathayfuture.opm.domain.account.repository;

import cathayfuture.opm.domain.account.AccountEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date ********
 */
public interface AccountRepository{

    /**
     * 根据openId查询
     * @param openId
     * @return
     */
    AccountEntity selectByOpenId(String openId);

    List<AccountEntity> selectByIds(List<Integer> ids);

    /**
     * 插入数据
     * @param account
     * @return
     */
    AccountEntity insert(AccountEntity account);
    
    /**
     * 根据Id更新数据
     * @param account
     * @return
     */
    AccountEntity updateById(AccountEntity account);
}
