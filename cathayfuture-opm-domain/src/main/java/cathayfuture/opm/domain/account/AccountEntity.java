package cathayfuture.opm.domain.account;

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ********
 */
@Data
@TableName("cf_acct_account")
public class AccountEntity extends BaseEntity {

    @TableField("open_id")
    private String openId;

    @TableField("union_id")
    private String unionId ;

    @TableField("nick_name")
    private String nickName;
    @TableField("avatar_url")
    private String avatarUrl;

    @TableField("phone_number")
    private String phoneNumber;

}
