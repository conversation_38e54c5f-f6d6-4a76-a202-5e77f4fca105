package cathayfuture.opm.domain.student;

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 20220331
 */
@Data
@TableName("cf_stu_expense_standard")
public class ExpenseStandardEntity extends BaseEntity {

    @TableField("student_no")
    private String studentNo;
    @TableField("student_id")
    private Integer studentId;
    @TableField("expense_type")
    private Integer expenseType;
    @TableField("expense_standard_type")
    private Integer expenseStandardType;
    @TableField("expense_standard")
    private BigDecimal expenseStandard;
    @TableField("default_id")
    private Integer defaultId;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

}
