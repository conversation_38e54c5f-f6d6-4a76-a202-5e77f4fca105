package cathayfuture.opm.domain.student;

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 费用标准默认值
 * @date 2023/4/11 11:21
 */

@Data
@TableName("of_stu_expense_default")
public class ExpenseDefaultEntity extends BaseEntity {

    @TableField("default_value")
    private BigDecimal defaultValue;
    @TableField("type")
    private Integer type;
}
