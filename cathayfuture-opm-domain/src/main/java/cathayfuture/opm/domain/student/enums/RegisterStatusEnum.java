package cathayfuture.opm.domain.student.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 入园标记
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RegisterStatusEnum {
    /**
     * 预报名
     */
    APPLICATION(0,"预报名"),
    /**
     * 在园
     */
    ENTER(1,"在园"),
    /**
     * 退园
     */
    QUIT(2,"退园")
    ;

    private final int key;
    private final String description;

    public static String getEnumDescription(int key) {
        if(Objects.isNull(key)){
            return "";
        }
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }

    public static List<Integer> getActiveList(){
        return Arrays.stream(values())
                .filter(x->!Objects.equals(RegisterStatusEnum.QUIT,x))
                .map(RegisterStatusEnum::getKey)
                .collect(Collectors.toList());
    }





}
