package cathayfuture.opm.domain.student.repository;

import cathayfuture.opm.domain.student.StudentEntity;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 20220824
 */
public interface StudentRepository {

    /**
     * 根据Id查询学生
     * @param id
     * @return
     */
    StudentEntity getById(Integer id);

    /**
     * 根据Ids查询
     * @param studentIdList
     * @return
     */
    List<StudentEntity> listByIds(List<Integer> studentIdList);

    /**
     * 根据联系人手机号查询
     * @param contactPhoneNumber
     * @return
     */
    List<StudentEntity> listByContactPhoneNumber(String contactPhoneNumber);

    List<StudentEntity> listByType(Integer type);

    /**
     * 新增学生
     * @param studentEntity
     * @return
     */
    StudentEntity add(StudentEntity studentEntity);

    /**
     * 根据学号查询count
     * @param studentNo
     * @param excludeId
     * @return
     */
    int countByStudentNo(String studentNo, Integer excludeId);

    /**
     * 根据身份证号查询count
     * @param idNumber
     * @param excludeId
     * @return
     */
    int countByIdNumber(String idNumber, Integer excludeId);


    /**
     * 根据Id更新
     * @param studentEntity
     * @return
     */
    int updateById(StudentEntity studentEntity);

    List<StudentEntity> queryByStudentName(String studentName);

    List<StudentEntity> queryListByAdmissionDateAndStudentClass(String studentClass);

    /**
     * 获取所有已入园的学生
     * @return
     */
    List<StudentEntity> queryRegisterStudents();

    List<StudentEntity> queryRegisterStudentsByTarget(LocalDate targetDate);

    List<StudentEntity> queryRegisterStudentsById(Integer studentId);

    List<StudentEntity> queryRegisterStudentsByIds(List<Integer> studentIds);

    Map<String,List<StudentEntity>> groupByClass();

    List<StudentEntity> queryByCondition(String studentName, LocalDate birthday, List<Integer> registerStatusList);
}
