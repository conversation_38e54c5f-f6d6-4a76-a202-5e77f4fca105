package cathayfuture.opm.domain.student;

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 20220824
 */
@Data
@TableName("cf_stu_student")
public class StudentEntity extends BaseEntity {

    @TableField("student_type")
    private Integer studentType;
    @TableField("student_name")
    private String studentName;
    @TableField("student_no")
    private String studentNo;
    @TableField("student_class")
    private String studentClass;
    @TableField("ethnic_group")
    private String ethnicGroup;
    @TableField("id_number")
    private String idNumber;
    @TableField("gender")
    private Integer gender;
    @TableField("birthday")
    private LocalDate birthday;
    @TableField("nationality")
    private String nationality;

    @TableField("province_code")
    private String provinceCode;
    @TableField("province_name")
    private String provinceName;
    @TableField("city_code")
    private String cityCode;
    @TableField("city_name")
    private String cityName;
    @TableField("region_code")
    private String regionCode;
    @TableField("region_name")
    private String regionName;
    @TableField("home_address")
    private String homeAddress;

    @TableField("contact_name")
    private String contactName;
    @TableField("contact_phone_number")
    private String contactPhoneNumber;
    @TableField("relation")
    private String relation;
    @TableField(value = "father_name")
    private String fatherName;
    @TableField("father_phone_number")
    private String fatherPhoneNumber;
    @TableField("mother_name")
    private String motherName;
    @TableField("mother_phone_number")
    private String motherPhoneNumber;

    @TableField("admission_date")
    private LocalDate admissionDate;

    @TableField("business_unit")
    private Integer businessUnit;

    @TableField("register_status")
    private Integer registerStatus;
}
