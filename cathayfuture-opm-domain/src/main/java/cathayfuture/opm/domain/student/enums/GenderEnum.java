package cathayfuture.opm.domain.student.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 性别
 * <AUTHOR>
 * @date 20220902
 */
@Getter
@AllArgsConstructor
public enum  GenderEnum {

    /**
     * 女
     */
    FEMALE(2, "女"),

    /**
     * 男
     */
    MALE(1, "男"),

    /**
     * 未知
     */
    UNKNOWN(0, "未知"),
    ;


    private final int key;
    private final String description;

    public static String getEnumDescription(Integer key) {
        if(Objects.isNull(key)){
            return "";
        }
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }

    public static GenderEnum getByOrdinal(int ordinal) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.ordinal(), ordinal)).findFirst().orElse(UNKNOWN);
    }
}
