package cathayfuture.opm.domain.student.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 描述: 学生类型
 *
 * <AUTHOR>
 * @date 20220829
 */
@Getter
@AllArgsConstructor
public enum StudentTypeEnum {

    /**
     * 未签约
     */
    NOT_SIGNED(0, "未签约"),
    /**
     * 已签约
     */
    SIGNED(1, "已签约"),
    ;


    private final int key;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }
}
