package cathayfuture.opm.domain.student.repository;

import cathayfuture.opm.domain.student.ExpenseStandardEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20230406
 */
public interface ExpenseStandardRepository {

    /**
     * 根据Id查询
     * @param id
     * @return
     */
    ExpenseStandardEntity getById(Integer id);

    List<ExpenseStandardEntity> findListByStudentIds(List<Integer> StudentId);

    Boolean edit(ExpenseStandardEntity expenseStandard);

    Boolean add(ExpenseStandardEntity expenseStandard);
}
