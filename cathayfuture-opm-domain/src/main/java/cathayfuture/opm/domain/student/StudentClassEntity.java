package cathayfuture.opm.domain.student;

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 班级
 * @date 2023/4/4 14:31
 */
@Data
@TableName("of_stu_class")
public class StudentClassEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -6855120496887952757L;
    @TableField("name")
    private String name;
}
