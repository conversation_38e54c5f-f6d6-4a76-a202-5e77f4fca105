package cathayfuture.opm.domain.student.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 描述: 保育费标准类型
 *
 * <AUTHOR>
 * @date 20230331
 */
@Getter
@AllArgsConstructor
public enum ExpenseStandardTypeEnum {

    /**
     * 默认值
     */
    NOT_SIGNED(0, "默认值"),
    /**
     * 自定义
     */
    SIGNED(1, "自定义"),
    ;


    private final int key;
    private final String description;

    public static String getEnumDescription(int key) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getKey(), key)).findFirst().orElseThrow(NoSuchElementException::new).getDescription();
    }
}
