package cathayfuture.opm.domain.student;

import cathayfuture.opm.domain.order.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 费用减免
 * @date 2023/3/31 9:57
 */
@Data
@TableName("of_stu_rate_relief")
public class RateReliefEntity extends BaseEntity {

    @TableField("student_id")
    private int studentId;

    @TableField("cost_type")
    private int costType;

    @TableField("derate_month")
    private String derateMonth;

    @TableField("derate_amount")
    private BigDecimal derateAmount;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
