package cathayfuture.opm.infra.system.repository;

import cathayfuture.opm.domain.system.SysRoleEntity;
import cathayfuture.opm.domain.system.repository.SysRoleRepository;
import cathayfuture.opm.infra.system.repository.mapper.SysRoleMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * 系统角色Repository实现
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@Repository
public class SysRoleRepositoryImpl implements SysRoleRepository {

    private final SysRoleMapper mapper;

    public SysRoleRepositoryImpl(SysRoleMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public SysRoleEntity findByRoleCode(String roleCode) {
        if (!StringUtils.hasText(roleCode)) {
            return null;
        }
        
        LambdaQueryWrapper<SysRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleEntity::getRoleCode, roleCode)
               .eq(SysRoleEntity::getDr, 0);
        
        return mapper.selectOne(wrapper);
    }

    @Override
    public SysRoleEntity findById(Integer id) {
        if (id == null) {
            return null;
        }
        
        LambdaQueryWrapper<SysRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleEntity::getId, id)
               .eq(SysRoleEntity::getDr, 0);
        
        return mapper.selectOne(wrapper);
    }

    @Override
    public List<SysRoleEntity> findByUserId(Integer userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        
        return mapper.selectByUserId(userId);
    }

    @Override
    public SysRoleEntity save(SysRoleEntity role) {
        if (role == null) {
            return null;
        }
        
        if (role.getId() == null) {
            // 新增
            mapper.insert(role);
        } else {
            // 更新
            mapper.updateById(role);
        }
        
        return role;
    }

    @Override
    public boolean updateById(SysRoleEntity role) {
        if (role == null || role.getId() == null) {
            return false;
        }
        
        return mapper.updateById(role) > 0;
    }

    @Override
    public boolean deleteById(Integer id) {
        if (id == null) {
            return false;
        }
        
        // 逻辑删除
        SysRoleEntity role = new SysRoleEntity();
        role.setId(id);
        role.setDr(1);
        
        return mapper.updateById(role) > 0;
    }


    @Override
    public List<SysRoleEntity> findAllEnabled() {
        LambdaQueryWrapper<SysRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleEntity::getStatus, SysRoleEntity.Status.ENABLED.getCode())
               .eq(SysRoleEntity::getDr, 0)
               .orderByAsc(SysRoleEntity::getSortOrder)
               .orderByAsc(SysRoleEntity::getRoleCode);
        
        return mapper.selectList(wrapper);
    }

    @Override
    public boolean existsByRoleCode(String roleCode, Integer excludeId) {
        if (!StringUtils.hasText(roleCode)) {
            return false;
        }
        
        LambdaQueryWrapper<SysRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleEntity::getRoleCode, roleCode)
               .eq(SysRoleEntity::getDr, 0);
        
        if (excludeId != null) {
            wrapper.ne(SysRoleEntity::getId, excludeId);
        }
        
        return mapper.selectCount(wrapper) > 0;
    }

    @Override
    public List<SysRoleEntity> findByIds(List<Integer> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<SysRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysRoleEntity::getId, roleIds)
               .eq(SysRoleEntity::getDr, 0)
               .orderByAsc(SysRoleEntity::getSortOrder);
        
        return mapper.selectList(wrapper);
    }
}
