package cathayfuture.opm.infra.system.repository;

import cathayfuture.opm.domain.system.SysUserEntity;
import cathayfuture.opm.domain.system.repository.SysUserRepository;
import cathayfuture.opm.infra.system.repository.mapper.SysUserMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * 系统用户Repository实现
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@Repository
public class SysUserRepositoryImpl implements SysUserRepository {

    private final SysUserMapper mapper;

    public SysUserRepositoryImpl(SysUserMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public SysUserEntity findByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }

        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserEntity::getUsername, username)
               .eq(SysUserEntity::getDr, 0);

        return mapper.selectOne(wrapper);
    }

    @Override
    public SysUserEntity findByUserCode(String userCode) {
        if (!StringUtils.hasText(userCode)) {
            return null;
        }

        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserEntity::getUserCode, userCode)
               .eq(SysUserEntity::getDr, 0);

        return mapper.selectOne(wrapper);
    }

    @Override
    public SysUserEntity findByEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return null;
        }
        
        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserEntity::getEmail, email)
               .eq(SysUserEntity::getDr, 0);
        
        return mapper.selectOne(wrapper);
    }

    @Override
    public SysUserEntity findByPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return null;
        }
        
        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserEntity::getPhone, phone)
               .eq(SysUserEntity::getDr, 0);
        
        return mapper.selectOne(wrapper);
    }

    @Override
    public SysUserEntity findById(Integer id) {
        if (id == null) {
            return null;
        }
        
        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserEntity::getId, id)
               .eq(SysUserEntity::getDr, 0);
        
        return mapper.selectOne(wrapper);
    }

    @Override
    public SysUserEntity save(SysUserEntity user) {
        if (user == null) {
            return null;
        }
        
        if (user.getId() == null) {
            // 新增
            mapper.insert(user);
        } else {
            // 更新
            mapper.updateById(user);
        }
        
        return user;
    }

    @Override
    public boolean updateById(SysUserEntity user) {
        if (user == null || user.getId() == null) {
            return false;
        }
        
        return mapper.updateById(user) > 0;
    }

    @Override
    public boolean deleteById(Integer id) {
        if (id == null) {
            return false;
        }
        
        // 逻辑删除
        SysUserEntity user = new SysUserEntity();
        user.setId(id);
        user.setDr(1);
        
        return mapper.updateById(user) > 0;
    }

    @Override
    public List<SysUserEntity> findByCondition(String username, String userCode, String realName, String oaName, Integer status) {
        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserEntity::getDr, 0);

        if (StringUtils.hasText(username)) {
            wrapper.like(SysUserEntity::getUsername, username);
        }

        if (StringUtils.hasText(userCode)) {
            wrapper.like(SysUserEntity::getUserCode, userCode);
        }

        if (StringUtils.hasText(realName)) {
            wrapper.like(SysUserEntity::getRealName, realName);
        }

        if (StringUtils.hasText(oaName)) {
            wrapper.like(SysUserEntity::getOaName, oaName);
        }

        if (status != null) {
            wrapper.eq(SysUserEntity::getStatus, status);
        }

        wrapper.orderByDesc(SysUserEntity::getCreateTime);

        return mapper.selectList(wrapper);
    }

    @Override
    public List<SysUserEntity> findAllEnabled() {
        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserEntity::getStatus, SysUserEntity.Status.ENABLED.getCode())
               .eq(SysUserEntity::getDr, 0)
               .orderByAsc(SysUserEntity::getUsername);

        return mapper.selectList(wrapper);
    }

    @Override
    public boolean existsByUsername(String username, Integer excludeId) {
        if (!StringUtils.hasText(username)) {
            return false;
        }

        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserEntity::getUsername, username)
               .eq(SysUserEntity::getDr, 0);

        if (excludeId != null) {
            wrapper.ne(SysUserEntity::getId, excludeId);
        }

        return mapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean existsByUserCode(String userCode, Integer excludeId) {
        if (!StringUtils.hasText(userCode)) {
            return false;
        }

        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserEntity::getUserCode, userCode)
               .eq(SysUserEntity::getDr, 0);

        if (excludeId != null) {
            wrapper.ne(SysUserEntity::getId, excludeId);
        }

        return mapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean existsByEmail(String email, Integer excludeId) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        
        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserEntity::getEmail, email)
               .eq(SysUserEntity::getDr, 0);
        
        if (excludeId != null) {
            wrapper.ne(SysUserEntity::getId, excludeId);
        }
        
        return mapper.selectCount(wrapper) > 0;
    }

    @Override
    public List<SysUserEntity> findByRoleId(Integer roleId) {
        if (roleId == null) {
            return Collections.emptyList();
        }
        
        return mapper.selectByRoleId(roleId);
    }
}
