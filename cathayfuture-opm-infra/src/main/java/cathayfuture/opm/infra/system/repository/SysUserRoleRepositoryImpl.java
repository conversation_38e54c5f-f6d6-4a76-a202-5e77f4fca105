package cathayfuture.opm.infra.system.repository;

import cathayfuture.opm.domain.system.SysUserRoleEntity;
import cathayfuture.opm.domain.system.repository.SysUserRoleRepository;
import cathayfuture.opm.infra.system.repository.mapper.SysUserRoleMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户角色关联Repository实现
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@Repository
public class SysUserRoleRepositoryImpl implements SysUserRoleRepository {

    private final SysUserRoleMapper mapper;

    public SysUserRoleRepositoryImpl(SysUserRoleMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public List<SysUserRoleEntity> findByUserId(Integer userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<SysUserRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRoleEntity::getUserId, userId)
               .eq(SysUserRoleEntity::getDr, 0);
        
        return mapper.selectList(wrapper);
    }

    @Override
    public List<SysUserRoleEntity> findByRoleId(Integer roleId) {
        if (roleId == null) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<SysUserRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRoleEntity::getRoleId, roleId)
               .eq(SysUserRoleEntity::getDr, 0);
        
        return mapper.selectList(wrapper);
    }

    @Override
    public SysUserRoleEntity save(SysUserRoleEntity userRole) {
        if (userRole == null) {
            return null;
        }
        
        if (userRole.getId() == null) {
            // 新增
            mapper.insert(userRole);
        } else {
            // 更新
            mapper.updateById(userRole);
        }
        
        return userRole;
    }

    @Override
    public boolean saveBatch(List<SysUserRoleEntity> userRoles) {
        if (userRoles == null || userRoles.isEmpty()) {
            return true;
        }
        
        try {
            for (SysUserRoleEntity userRole : userRoles) {
                mapper.insert(userRole);
            }
            return true;
        } catch (Exception e) {
            log.error("批量保存用户角色关联失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteByUserId(Integer userId) {
        if (userId == null) {
            return false;
        }
        
        // 逻辑删除
        SysUserRoleEntity userRole = new SysUserRoleEntity();
        userRole.setDr(1);
        
        LambdaQueryWrapper<SysUserRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRoleEntity::getUserId, userId)
               .eq(SysUserRoleEntity::getDr, 0);
        
        return mapper.update(userRole, wrapper) > 0;
    }

    @Override
    public boolean deleteByRoleId(Integer roleId) {
        if (roleId == null) {
            return false;
        }
        
        // 逻辑删除
        SysUserRoleEntity userRole = new SysUserRoleEntity();
        userRole.setDr(1);
        
        LambdaQueryWrapper<SysUserRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRoleEntity::getRoleId, roleId)
               .eq(SysUserRoleEntity::getDr, 0);
        
        return mapper.update(userRole, wrapper) > 0;
    }

    @Override
    public boolean deleteByUserIdAndRoleId(Integer userId, Integer roleId) {
        if (userId == null || roleId == null) {
            return false;
        }
        
        // 逻辑删除
        SysUserRoleEntity userRole = new SysUserRoleEntity();
        userRole.setDr(1);
        
        LambdaQueryWrapper<SysUserRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRoleEntity::getUserId, userId)
               .eq(SysUserRoleEntity::getRoleId, roleId)
               .eq(SysUserRoleEntity::getDr, 0);
        
        return mapper.update(userRole, wrapper) > 0;
    }

    @Override
    public boolean existsByUserIdAndRoleId(Integer userId, Integer roleId) {
        if (userId == null || roleId == null) {
            return false;
        }
        
        LambdaQueryWrapper<SysUserRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRoleEntity::getUserId, userId)
               .eq(SysUserRoleEntity::getRoleId, roleId)
               .eq(SysUserRoleEntity::getDr, 0);
        
        return mapper.selectCount(wrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRolesToUser(Integer userId, List<Integer> roleIds) {
        if (userId == null) {
            return false;
        }
        
        try {
            // 1. 先删除用户原有的角色关联
            deleteByUserId(userId);
            
            // 2. 如果有新角色，则添加新的关联
            if (roleIds != null && !roleIds.isEmpty()) {
                List<SysUserRoleEntity> userRoles = roleIds.stream()
                        .map(roleId -> {
                            SysUserRoleEntity userRole = new SysUserRoleEntity();
                            userRole.setUserId(userId);
                            userRole.setRoleId(roleId);
                            return userRole;
                        })
                        .collect(Collectors.toList());
                
                return saveBatch(userRoles);
            }
            
            return true;
        } catch (Exception e) {
            log.error("为用户 {} 分配角色 {} 失败", userId, roleIds, e);
            throw e;
        }
    }
}
