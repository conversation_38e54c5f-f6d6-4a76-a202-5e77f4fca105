package cathayfuture.opm.infra.student.repository;

import cathayfuture.opm.client.student.exception.ExpenseDefaultAddException;
import cathayfuture.opm.domain.student.ExpenseDefaultEntity;
import cathayfuture.opm.domain.student.repository.ExpenseDefaultRepository;
import cathayfuture.opm.infra.common.basequery.wrapper.ExtQueryWrapper;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cathayfuture.opm.infra.student.repository.mapper.ExpenseDefaultMapper;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 标准费用默认值
 * @date 2023/4/11 13:35
 */
@Repository
@Slf4j
public class ExpenseDefaultRepositoryImpl implements ExpenseDefaultRepository {

    private ExpenseDefaultMapper mapper;

    public ExpenseDefaultRepositoryImpl(ExpenseDefaultMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public ExpenseDefaultEntity getById(Integer id){
        return mapper.selectById(id);
    }

    @Override
    public ExpenseDefaultEntity getByType(Integer type){
        List<ExpenseDefaultEntity> entityList = mapper.selectList(ExtQueryWrapper.newInstance(ExpenseDefaultEntity.class)
                .eq(LambdaUtils.column(ExpenseDefaultEntity::getType), type)
        );

        return entityList.stream()
                .findFirst()
                .orElse(new ExpenseDefaultEntity());
    }



    @Override
    public Boolean change(ExpenseDefaultEntity entity){
        log.info("ExpenseDefaultRepository.change()入参，entity：{}",JSONUtil.toJsonStr(entity));
        //不能修改，应该先删除，在新增
/*        LambdaUpdateWrapper<ExpenseDefaultEntity> updateWrapper = new UpdateWrapper<ExpenseDefaultEntity>().lambda();
        updateWrapper.eq(ExpenseDefaultEntity::getType,entity.getType());

        ExpenseDefaultEntity orderInfo = new ExpenseDefaultEntity();
        orderInfo.setDr(1);
        int count = mapper.update(orderInfo, updateWrapper);*/


        int count = mapper.deleteById(entity.getId());
        if (count <= 0) {
            log.error("ExpenseDefaultRepository.change()第一步删除失败，entity：{}",JSONUtil.toJsonStr(entity));
            throw new ExpenseDefaultAddException("ExpenseDefaultRepository.change()第一步删除失败，entity：["+ JSONUtil.toJsonStr(entity)+"]");
        }
        int insert = mapper.insert(entity);
        if(insert>0){
            return Boolean.TRUE;
        }else {
            return Boolean.FALSE;
        }
    }
}
