package cathayfuture.opm.infra.student.repository;

import cathayfuture.opm.domain.student.ExpenseStandardEntity;
import cathayfuture.opm.domain.student.repository.ExpenseStandardRepository;
import cathayfuture.opm.infra.student.repository.mapper.ExpenseStandardMapper;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cathayfuture.opm.infra.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 20230406
 */
@Repository
public class ExpenseStandardRepositoryImpl implements ExpenseStandardRepository {

    private ExpenseStandardMapper mapper;

    public ExpenseStandardRepositoryImpl(ExpenseStandardMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public ExpenseStandardEntity getById(Integer id) {
        return mapper.selectById(id);
    }

    @Override
    public List<ExpenseStandardEntity> findListByStudentIds(List<Integer> studentIds){
        if(CollectionUtil.isEmpty(studentIds)){
            throw new BizException("ExpenseStandardRepository.findListByStudentNo 参数 studentIds 为空");
        }
        LambdaQueryWrapper<ExpenseStandardEntity> queryWrapper = Wrappers.lambdaQuery(ExpenseStandardEntity.class)
                .in(ExpenseStandardEntity::getStudentId, studentIds);
        return mapper.selectList(queryWrapper);
    }

    @Override
    public Boolean edit(ExpenseStandardEntity expenseStandard) {
        int updateNum = mapper.updateById(expenseStandard);
        return updateNum ==  1;
    }

    @Override
    public Boolean add(ExpenseStandardEntity expenseStandard){
        int insert = mapper.insert(expenseStandard);
        return insert ==  1;
    }
}
