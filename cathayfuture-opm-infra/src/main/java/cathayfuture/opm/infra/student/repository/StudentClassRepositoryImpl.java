package cathayfuture.opm.infra.student.repository;

import cathayfuture.opm.domain.student.StudentClassEntity;
import cathayfuture.opm.domain.student.repository.StudentClassRepository;
import cathayfuture.opm.infra.common.basequery.wrapper.ExtQueryWrapper;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cathayfuture.opm.infra.student.repository.mapper.StudentClassMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 班级
 * @date 2023/4/4 14:33
 */
@Repository
public class StudentClassRepositoryImpl implements StudentClassRepository {

    private StudentClassMapper mapper;

    public StudentClassRepositoryImpl(StudentClassMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public List<StudentClassEntity> findAllList(){
        List<StudentClassEntity> list = mapper.selectList(ExtQueryWrapper.newInstance(StudentClassEntity.class)
                .isNotNull(LambdaUtils.column(StudentClassEntity::getName))
        );
        return list;
    }
}
