package cathayfuture.opm.infra.student.repository;

import cathayfuture.opm.client.student.exception.StudentNotExistException;
import cathayfuture.opm.domain.attendance.ennums.CommonBooleanEnum;
import cathayfuture.opm.domain.order.BaseEntity;
import cathayfuture.opm.domain.student.StudentEntity;
import cathayfuture.opm.domain.student.enums.RegisterStatusEnum;
import cathayfuture.opm.domain.student.repository.StudentRepository;
import cathayfuture.opm.infra.common.basequery.wrapper.ExtQueryWrapper;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cathayfuture.opm.infra.student.repository.mapper.StudentMapper;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 20220824
 */
@Repository
public class StudentRepositoryImpl implements StudentRepository {

    private StudentMapper mapper;

    public StudentRepositoryImpl(StudentMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public StudentEntity getById(Integer id) {
        return mapper.selectById(id);
    }

    @Override
    public List<StudentEntity> listByIds(List<Integer> studentIdList) {
        LambdaQueryWrapper<StudentEntity> queryWrapper = Wrappers.lambdaQuery(StudentEntity.class)
                .in(BaseEntity::getId, studentIdList);
        return mapper.selectList(queryWrapper);
    }

    @Override
    public List<StudentEntity> listByContactPhoneNumber(String contactPhoneNumber) {
        LambdaQueryWrapper<StudentEntity> queryWrapper = Wrappers.lambdaQuery(StudentEntity.class)
                .eq(StudentEntity::getContactPhoneNumber, contactPhoneNumber);
        return mapper.selectList(queryWrapper);
    }

    @Override
    public List<StudentEntity> listByType(Integer type) {
        LambdaQueryWrapper<StudentEntity> queryWrapper = Wrappers.lambdaQuery(StudentEntity.class)
                .eq(StudentEntity::getStudentType, type);
        return mapper.selectList(queryWrapper);
    }

    @Override
    public StudentEntity add(StudentEntity studentEntity) {
        mapper.insert(studentEntity);
        return studentEntity;
    }

    @Override
    public int countByStudentNo(String studentNo, Integer excludeId) {
        LambdaQueryWrapper<StudentEntity> queryWrapper = Wrappers.lambdaQuery(StudentEntity.class)
                .eq(StudentEntity::getStudentNo, studentNo)
                .ne(Objects.nonNull(excludeId), BaseEntity::getId, excludeId);
        return mapper.selectCount(queryWrapper);
    }

    @Override
    public int countByIdNumber(String idNumber, Integer excludeId) {
        LambdaQueryWrapper<StudentEntity> queryWrapper = Wrappers.lambdaQuery(StudentEntity.class)
                .eq(StudentEntity::getIdNumber, idNumber)
                .ne(Objects.nonNull(excludeId), BaseEntity::getId, excludeId);
        return mapper.selectCount(queryWrapper);
    }

    @Override
    public int updateById(StudentEntity studentEntity) {
        LambdaUpdateWrapper<StudentEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(StudentEntity::getFatherName,studentEntity.getFatherName());
        updateWrapper.set(StudentEntity::getFatherPhoneNumber,studentEntity.getFatherPhoneNumber());
        updateWrapper.set(StudentEntity::getMotherName,studentEntity.getMotherName());
        updateWrapper.set(StudentEntity::getMotherPhoneNumber,studentEntity.getMotherPhoneNumber());
        updateWrapper.set(StudentEntity::getAdmissionDate,studentEntity.getAdmissionDate());
        updateWrapper.set(StudentEntity::getBusinessUnit,studentEntity.getBusinessUnit());
        updateWrapper.set(StudentEntity::getProvinceCode,studentEntity.getProvinceCode());
        updateWrapper.set(StudentEntity::getProvinceName,studentEntity.getProvinceName());
        updateWrapper.set(StudentEntity::getCityCode,studentEntity.getCityCode());
        updateWrapper.set(StudentEntity::getCityName,studentEntity.getCityName());
        updateWrapper.set(StudentEntity::getRegionCode,studentEntity.getRegionCode());
        updateWrapper.set(StudentEntity::getRegionName,studentEntity.getRegionName());
        updateWrapper.set(StudentEntity::getHomeAddress,studentEntity.getHomeAddress());



        updateWrapper.eq(StudentEntity::getId, studentEntity.getId());
        return mapper.update(studentEntity, updateWrapper);
    }

    @Override
    public List<StudentEntity> queryByStudentName(String studentName){
        LambdaQueryWrapper<StudentEntity> queryWrapper = Wrappers.lambdaQuery(StudentEntity.class)
                .like(StringUtils.isNotBlank(studentName),StudentEntity::getStudentName, studentName);
        return mapper.selectList(queryWrapper);
    }

    @Override
    public List<StudentEntity> queryListByAdmissionDateAndStudentClass(String studentClass){
        List<StudentEntity> entityList = mapper.selectList(ExtQueryWrapper.newInstance(StudentEntity.class)
                .eq(LambdaUtils.column(StudentEntity::getStudentClass), studentClass)
                .le(LambdaUtils.column(StudentEntity::getAdmissionDate), LocalDate.now())
                .eq(LambdaUtils.column(StudentEntity::getRegisterStatus), RegisterStatusEnum.ENTER.getKey())
        );
        return entityList;
    }

    @Override
    public List<StudentEntity> queryRegisterStudents(){
        LocalDate lastDayOfMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<StudentEntity> entityList = mapper.selectList(ExtQueryWrapper.newInstance(StudentEntity.class)
                .le(LambdaUtils.column(StudentEntity::getAdmissionDate), lastDayOfMonth)
                .eq(LambdaUtils.column(StudentEntity::getRegisterStatus), RegisterStatusEnum.ENTER.getKey())
        );
        return entityList;
    }

    @Override
    public List<StudentEntity> queryRegisterStudentsByTarget(LocalDate targetDate){
        List<StudentEntity> entityList = mapper.selectList(ExtQueryWrapper.newInstance(StudentEntity.class)
                .le(LambdaUtils.column(StudentEntity::getAdmissionDate), targetDate)
                .eq(LambdaUtils.column(StudentEntity::getRegisterStatus), RegisterStatusEnum.ENTER.getKey())
        );
        return entityList;
    }



    @Override
    public List<StudentEntity> queryRegisterStudentsById(Integer studentId){

        LocalDate lastDayOfMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<StudentEntity> entityList = mapper.selectList(ExtQueryWrapper.newInstance(StudentEntity.class)
                .le(LambdaUtils.column(StudentEntity::getAdmissionDate), lastDayOfMonth)
                .eq(LambdaUtils.column(StudentEntity::getRegisterStatus), RegisterStatusEnum.ENTER.getKey())
                .eq(LambdaUtils.column(StudentEntity::getId), studentId)
        );
        if(CollectionUtil.isEmpty(entityList)){
            throw new StudentNotExistException("当前学生未入园，请更换其他学生");
        }
        return entityList;
    }

    @Override
    public List<StudentEntity> queryRegisterStudentsByIds(List<Integer> studentIds){

        LocalDate lastDayOfMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<StudentEntity> entityList = mapper.selectList(ExtQueryWrapper.newInstance(StudentEntity.class)
                .le(LambdaUtils.column(StudentEntity::getAdmissionDate), lastDayOfMonth)
                .eq(LambdaUtils.column(StudentEntity::getRegisterStatus), RegisterStatusEnum.ENTER.getKey())
                .in(LambdaUtils.column(StudentEntity::getId), studentIds)
        );
        if(CollectionUtil.isEmpty(entityList)){
            throw new StudentNotExistException("当前学生未入园，请更换其他学生");
        }
        return entityList;
    }

    @Override
    public Map<String,List<StudentEntity>> groupByClass(){
        List<StudentEntity> entityList = mapper.selectList(ExtQueryWrapper.newInstance(StudentEntity.class)
                .le(LambdaUtils.column(StudentEntity::getAdmissionDate), LocalDate.now())
                .eq(LambdaUtils.column(StudentEntity::getRegisterStatus), RegisterStatusEnum.ENTER.getKey())
                .orderByAsc(LambdaUtils.column(StudentEntity::getId))
        );
        return entityList.stream()
                .collect(Collectors.groupingBy(StudentEntity::getStudentClass));

    }

    @Override
    public List<StudentEntity> queryByCondition(String studentName, LocalDate birthday, List<Integer> registerStatusList){
        List<StudentEntity> entityList = mapper.selectList(ExtQueryWrapper.newInstance(StudentEntity.class)
                .eq(LambdaUtils.column(StudentEntity::getStudentName), studentName)
                .eq(LambdaUtils.column(StudentEntity::getBirthday), birthday)
                .eq(LambdaUtils.column(StudentEntity::getDr), CommonBooleanEnum.NO.getKey())
                .in(LambdaUtils.column(StudentEntity::getRegisterStatus), registerStatusList)
        );
        return entityList;
    }
}
