package cathayfuture.opm.infra.account.repository;

import cathayfuture.opm.domain.account.AccountEntity;
import cathayfuture.opm.domain.account.repository.AccountRepository;
import cathayfuture.opm.domain.order.BaseEntity;
import cathayfuture.opm.infra.account.repository.mapper.AccountMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date ********
 */
@Repository
public class AccountRepositoryImpl implements AccountRepository {

    private AccountMapper mapper;

    public AccountRepositoryImpl(AccountMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public AccountEntity selectByOpenId(String openId) {
        LambdaQueryWrapper<AccountEntity> queryWrapper = Wrappers.lambdaQuery(AccountEntity.class)
                .eq(AccountEntity::getOpenId, openId);
        return mapper.selectOne(queryWrapper);
    }

    @Override
    public List<AccountEntity> selectByIds(List<Integer> ids) {
        return mapper.selectBatchIds(ids);
    }

    @Override
    public AccountEntity insert(AccountEntity account) {
        mapper.insert(account);
        return account;
    }

    @Override
    public AccountEntity updateById(AccountEntity account) {
        mapper.updateById(account);
        return account;
    }
}
