package cathayfuture.opm.infra.account.repository;

import cathayfuture.opm.domain.account.RAccountStudentEntity;
import cathayfuture.opm.domain.account.repository.RAccountStudentRepository;
import cathayfuture.opm.domain.order.BaseEntity;
import cathayfuture.opm.infra.account.repository.mapper.RAccountStudentMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date ********
 */
@Repository
public class RAccountStudentRepositoryImpl implements RAccountStudentRepository {

    private RAccountStudentMapper mapper;

    public RAccountStudentRepositoryImpl(RAccountStudentMapper mapper) {
        this.mapper = mapper;
    }


    @Override
    public List<RAccountStudentEntity> listByAccountId(Integer accountId) {
        LambdaQueryWrapper<RAccountStudentEntity> queryWrapper = Wrappers.lambdaQuery(RAccountStudentEntity.class)
                .eq(RAccountStudentEntity::getAccountId, accountId);
        return mapper.selectList(queryWrapper);
    }

    @Override
    public int insertBatch(List<RAccountStudentEntity> entityList) {
        int count = 0;
        for (RAccountStudentEntity e : entityList) {
            count += mapper.insert(e);
        }
        return count;
    }

    @Override
    public List<RAccountStudentEntity> listByStudentId(Integer studentId) {
        LambdaQueryWrapper<RAccountStudentEntity> queryWrapper = Wrappers.lambdaQuery(RAccountStudentEntity.class)
                .eq(RAccountStudentEntity::getStudentId, studentId);
        return mapper.selectList(queryWrapper);
    }
}
