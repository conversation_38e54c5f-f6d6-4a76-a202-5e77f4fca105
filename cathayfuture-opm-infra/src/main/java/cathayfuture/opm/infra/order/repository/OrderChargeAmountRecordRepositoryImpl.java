package cathayfuture.opm.infra.order.repository;

import cathayfuture.opm.domain.order.OrderChargeAmountRecordEntity;
import cathayfuture.opm.domain.order.gateway.repository.OrderChargeAmountRecordRepository;
import cathayfuture.opm.infra.order.repository.mapper.OrderChargeAmountRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单金额记录表
 * @date 2023/6/1 16:58
 */
@Repository
@Slf4j
public class OrderChargeAmountRecordRepositoryImpl implements OrderChargeAmountRecordRepository {

    private OrderChargeAmountRecordMapper mapper;

    public OrderChargeAmountRecordRepositoryImpl(OrderChargeAmountRecordMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public Boolean batchInsert(List<OrderChargeAmountRecordEntity> list){

        int num = mapper.batchInsert(list);
        if(Objects.equals(num,list.size())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

}
