package cathayfuture.opm.infra.order.projection;

import cathayfuture.opm.client.order.exception.OrderPrePayException;
import cathayfuture.opm.client.utils.WxAccountHolder;
import cathayfuture.opm.domain.order.OrderEntity;
import cathayfuture.opm.domain.order.gateway.projection.PaymentProjection;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
@Slf4j
public class PaymentProjectionImpl implements PaymentProjection {

    private static final int WX_PRE_PAY_BODY_MAX_LENGTH = 127;
    private static final String ELLIPSIS = "...";

    @Value("${payment-center.user-no}")
    private String userNo;
    @Value("${payment-center.pay-product-code}")
    private String payProductCode;
    @Value("${payment-center.pay-type-code}")
    private String payTypeCode;
    @Value("${payment-center.pay-way-code}")
    private String payWayCode;
    @Value("${payment-center.pre-pay-url}")
    private String prePayUrl;
    @Value("${payment-center.notify-url}")
    private String notifyUrl;
    @Value("${payment-center.query-pay-result-url}")
    private String queryPayResultUrl;

    @Override
    public String prePay(OrderEntity order) {
        Map<String, Object> prePayParamMap = new HashMap<>();
        prePayParamMap.put("userNo", userNo);
        prePayParamMap.put("payProductCode", payProductCode);
        prePayParamMap.put("payTypeCode", payTypeCode);
        prePayParamMap.put("payWayCode", payWayCode);
        prePayParamMap.put("tenantOrderNo", order.getTradeNo());
        prePayParamMap.put("body", subPrePayBody(order.getChargeItem()));
        prePayParamMap.put("notifyUrl", notifyUrl);
        prePayParamMap.put("totalFee", Optional.ofNullable(order.getChargeAmount()).orElse(BigDecimal.ZERO));
        prePayParamMap.put("spbillCreateIp", "127.0.0.1");
        prePayParamMap.put("openId", WxAccountHolder.get().getOpenId());
        prePayParamMap.put("expireDate", DateUtil.format(DateUtil.offsetHour(DateUtil.date(), 2), DatePattern.PURE_DATETIME_FORMAT));
        String prePayParam = JSONUtil.toJsonStr(prePayParamMap);
        log.info("开始发起预支付，请求参数[{}]", prePayParam);
        String response = HttpUtil.post(prePayUrl, prePayParam);
        log.info("结束发起预支付，返回值[{}]", response);
        JSONObject responseObj = JSONUtil.parseObj(response);
        if (Objects.equals(responseObj.getInt("resultCode"), HttpStatus.HTTP_OK)) {
            return JSONUtil.toJsonStr(responseObj.getJSONObject("data").put("prePayFlag", true));
        } else {
            throw new OrderPrePayException(responseObj.getStr("resultMsg"));
        }
    }

    @Override
    public String queryPayResult(OrderEntity order) {
        if (StringUtils.isEmpty(order.getTradeNo())) {
            return null;
        }
        Map<String, Object> queryPayResultParamMap = new HashMap<>();
        queryPayResultParamMap.put("tenantNo", userNo);
        queryPayResultParamMap.put("tenantOrderNo", order.getTradeNo());
        String prePayParam = JSONUtil.toJsonStr(queryPayResultParamMap);
        log.info("开始查询支付结果，请求参数[{}]，orderId", prePayParam, order.getId());
        String response = HttpUtil.get(StrUtil.format(queryPayResultUrl, queryPayResultParamMap));
        log.info("结束查询支付结果，返回值[{}]，orderId", response, order.getId());
        JSONObject responseObj = JSONUtil.parseObj(response);
        if (Objects.equals(responseObj.getInt("resultCode"), HttpStatus.HTTP_OK)) {
            return JSONUtil.toJsonStr(responseObj.getJSONObject("data"));
        } else {
            return null;
        }
    }

    private String subPrePayBody(String body) {
        try {
            if (body.getBytes(CharsetUtil.UTF_8).length > WX_PRE_PAY_BODY_MAX_LENGTH) {
                while (body.getBytes(CharsetUtil.UTF_8).length > (WX_PRE_PAY_BODY_MAX_LENGTH - ELLIPSIS.getBytes(CharsetUtil.UTF_8).length)) {
                    body = StrUtil.subPre(body, body.length() - 1);
                }
                body += ELLIPSIS;
            }
            return body;
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }
        return body;
    }
}
