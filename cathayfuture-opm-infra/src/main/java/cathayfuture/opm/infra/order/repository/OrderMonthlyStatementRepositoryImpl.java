package cathayfuture.opm.infra.order.repository;

import cathayfuture.opm.client.order.exception.OrderMonthlyStatementException;
import cathayfuture.opm.domain.order.OrderMonthlyStatementEntity;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import cathayfuture.opm.domain.order.gateway.repository.OrderMonthlyStatementRepository;
import cathayfuture.opm.infra.common.basequery.wrapper.ExtQueryWrapper;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cathayfuture.opm.infra.order.repository.mapper.OrderMonthlyStatementMapper;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 月结
 * @date 2023/5/23 11:05
 */
@Repository
@Slf4j
public class OrderMonthlyStatementRepositoryImpl implements OrderMonthlyStatementRepository {

    private OrderMonthlyStatementMapper mapper;

    public OrderMonthlyStatementRepositoryImpl(OrderMonthlyStatementMapper mapper) {
        this.mapper = mapper;
    }

    private DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public List<OrderMonthlyStatementEntity> findListByStudentIdsAndMonth(List<Integer> studentIds, LocalDate month, OrderTypeEnum typeEnum){

        List<OrderMonthlyStatementEntity> list = mapper.selectList(ExtQueryWrapper.newInstance(OrderMonthlyStatementEntity.class)
                .in(LambdaUtils.column(OrderMonthlyStatementEntity::getStudentId), studentIds)
                .eq(LambdaUtils.column(OrderMonthlyStatementEntity::getMonth), month)
                .eq(LambdaUtils.column(OrderMonthlyStatementEntity::getOrderType), typeEnum.getKey())
        );
        return list;
    }

    @Override
    public void batchInsert(List<OrderMonthlyStatementEntity> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        int num = mapper.batchInsert(list);
        if(list.size() != num){
            throw new OrderMonthlyStatementException("批量新增订单结余失败");
        }
    }

}
