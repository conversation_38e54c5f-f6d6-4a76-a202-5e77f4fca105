package cathayfuture.opm.infra.order.repository;

import cathayfuture.opm.client.order.exception.OrderDeleteException;
import cathayfuture.opm.client.order.exception.OrderRefundException;
import cathayfuture.opm.domain.order.OrderEntity;
import cathayfuture.opm.domain.order.enums.OrderStatusEnum;
import cathayfuture.opm.domain.order.enums.PaymentStatusEnum;
import cathayfuture.opm.domain.order.gateway.repository.OrderRepository;
import cathayfuture.opm.infra.common.ExtServiceContext;
import cathayfuture.opm.infra.common.LocalServiceContext;
import cathayfuture.opm.infra.order.repository.mapper.OrderMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Repository
public class OrderRepositoryImpl implements OrderRepository {
    private OrderMapper orderMapper;


    public OrderRepositoryImpl(OrderMapper orderMapper) {
        this.orderMapper = orderMapper;
    }

    @Override
    public OrderEntity add(OrderEntity order) {
        orderMapper.insert(order);
        return order;
    }

    @Override
    public OrderEntity getById(Integer orderId) {
        return orderMapper.selectById(orderId);
    }

    @Override
    public OrderEntity getByCode(String code) {
        LambdaQueryWrapper<OrderEntity> queryWrapper = Wrappers.lambdaQuery(OrderEntity.class)
                .eq(OrderEntity::getCode, code);
        return orderMapper.selectOne(queryWrapper);
    }

    @Override
    public int updateById(OrderEntity orderEntity) {
        return orderMapper.updateById(orderEntity);
    }

    @Override
    public Boolean remove(Integer orderId) {
        int num = orderMapper.deleteById(orderId);
        if (num == 1) {
            return true;
        }
        return false;
    }

    @Override
    public OrderEntity getByTradeNo(String tradeNo) {
        LambdaQueryWrapper<OrderEntity> queryWrapper = Wrappers.lambdaQuery(OrderEntity.class)
                .eq(OrderEntity::getTradeNo, tradeNo);
        return orderMapper.selectOne(queryWrapper);
    }

    @Override
    public Boolean delete(Integer orderId, String deleteReason) {
        OrderEntity orderEntityInDb = orderMapper.selectById(orderId);
        if (Objects.nonNull(orderEntityInDb)) {
            if (!Objects.equals(orderEntityInDb.getPaymentStatus(), (PaymentStatusEnum.NO_PAY.getKey()))) {
                throw new OrderDeleteException("该订单已支付！");
            }
            if (!Objects.equals(orderEntityInDb.getStatus(), OrderStatusEnum.NORMAL.getKey())) {
                throw new OrderDeleteException("该订单已作废！");
            }
            OrderEntity orderEntity = new OrderEntity();
            orderEntity.setUpdatePerson(ExtServiceContext.getUserCode());
            orderEntity.setDeletePerson(LocalServiceContext.getContext().getRequestUserCode());
            orderEntity.setDeleteTime(new Date());
            orderEntity.setStatus(OrderStatusEnum.DELETED.getKey());
            Optional.ofNullable(deleteReason)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(orderEntity::setDeleteReason);
            orderEntity.setUpdateTime(new Date());
            int updateNum = orderMapper.update(orderEntity, Wrappers.lambdaUpdate(OrderEntity.class)
                    .eq(OrderEntity::getId, orderId)
                    .eq(OrderEntity::getPaymentStatus, PaymentStatusEnum.NO_PAY.getKey())
                    .eq(OrderEntity::getStatus, OrderStatusEnum.NORMAL.getKey()));
            return updateNum == 1;
        } else {
            throw new OrderDeleteException("在系统中找不到订单，订单号为:"+orderId);
        }
    }

    @Override
    public Integer addBatch(List<OrderEntity> orderEntityList) {
        return orderMapper.insertBatchSomeColumn(orderEntityList);
    }

    @Override
    public Boolean refund(Integer orderId, String refundTime, String refundReason) {
        OrderEntity orderEntityInDb = orderMapper.selectById(orderId);
        if (Objects.nonNull(orderEntityInDb)) {
            if (!Objects.equals(orderEntityInDb.getPaymentStatus(), (PaymentStatusEnum.PAID.getKey()))) {
                throw new OrderRefundException("该订单不是已支付状态，不能进行退款！");
            }
            if (!Objects.equals(orderEntityInDb.getStatus(), OrderStatusEnum.NORMAL.getKey())) {
                throw new OrderRefundException("该订单已作废，不能进行退款！");
            }
            OrderEntity orderEntity = new OrderEntity();
            orderEntity.setPaymentStatus(PaymentStatusEnum.REFUND.getKey());
            orderEntity.setRefundTime(LocalDate.parse(refundTime, DateTimeFormatter.ISO_LOCAL_DATE));
            orderEntity.setRefundReason(refundReason);
            int updateNum = orderMapper.update(orderEntity, Wrappers.lambdaUpdate(OrderEntity.class)
                    .eq(OrderEntity::getId, orderId)
                    .eq(OrderEntity::getPaymentStatus, PaymentStatusEnum.PAID.getKey())
                    .eq(OrderEntity::getStatus, OrderStatusEnum.NORMAL.getKey()));
            return updateNum == 1;
        } else {
            throw new OrderRefundException("在系统中找不到订单，订单号为:"+orderId);
        }
    }

    @Override
    public List<OrderEntity> getListByCreateTime(LocalDate startDate, LocalDate endDate,Integer type){
        // 获取当前月份的月初时间
        LocalDateTime startDateTime = startDate.withDayOfMonth(1).atTime(LocalTime.MIN);
        // 将时间设为00:00:00
        LocalDateTime formattedStartDateTime = startDateTime.with(LocalTime.MIN);

        // 获取当前月份的月末时间
        LocalDateTime endDateTime = endDate.with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX);
        // 将时间设为23:59:59
        LocalDateTime formattedEndDateTime = endDateTime.with(LocalTime.MAX);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedStartDateTimeStr = formattedStartDateTime.format(formatter);
        String formattedEndDateTimeStr = formattedEndDateTime.format(formatter);

        LambdaQueryWrapper<OrderEntity> queryWrapper = Wrappers.lambdaQuery(OrderEntity.class)
                .ge(OrderEntity::getCreateTime, formattedStartDateTimeStr)
                .le(OrderEntity::getCreateTime, formattedEndDateTimeStr)
                .eq(OrderEntity::getType,type)
                .eq(OrderEntity::getStatus,OrderStatusEnum.NORMAL.getKey())
                .isNull(OrderEntity::getDeleteTime);
        return orderMapper.selectList(queryWrapper);
    }
}
