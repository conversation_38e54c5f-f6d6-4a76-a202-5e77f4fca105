package cathayfuture.opm.infra.order.repository;

import cathayfuture.opm.domain.order.gateway.repository.IdSequenceRepository;
import cathayfuture.opm.infra.order.repository.mapper.IdSequenceMapper;
import org.springframework.stereotype.Repository;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2022/8/27
 */
@Repository
public class IdSequenceRepositoryImpl implements IdSequenceRepository {

    private IdSequenceMapper idSequenceMapper;

    public IdSequenceRepositoryImpl(IdSequenceMapper idSequenceMapper) {
        this.idSequenceMapper = idSequenceMapper;
    }

    @Override
    public Integer getSequence(String prefix) {
        return idSequenceMapper.getSequence(prefix);
    }

    @Override
    public void incrSequence(String prefix, Integer increment) {
        idSequenceMapper.incrSequence(prefix, increment);
    }
}
