package cathayfuture.opm.infra.order.repository;

import cathayfuture.opm.client.order.exception.OrderCreateException;
import cathayfuture.opm.domain.order.OrderOperationRecordEntity;
import cathayfuture.opm.domain.order.gateway.repository.OrderOperationRecordRepository;
import cathayfuture.opm.infra.order.repository.mapper.OrderOperationRecordMapper;
import cn.hutool.core.collection.CollectionUtil;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 月结订单生成操作人记录表
 * @date 2023/6/9 16:28
 */

@Repository
public class OrderOperationRecordRepositoryImpl implements OrderOperationRecordRepository {

    private OrderOperationRecordMapper mapper;

    public OrderOperationRecordRepositoryImpl(OrderOperationRecordMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public Boolean batchInsert(List<OrderOperationRecordEntity> list){
        if(CollectionUtil.isEmpty(list)){
            return Boolean.TRUE;
        }
        int num = mapper.batchInsert(list);
        if(list.size() != num){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
