package cathayfuture.opm.infra.order.repository;

import cathayfuture.opm.domain.order.BaseEntity;
import cathayfuture.opm.domain.order.PaymentDetailEntity;
import cathayfuture.opm.domain.order.enums.PaymentStatusEnum;
import cathayfuture.opm.domain.order.gateway.repository.PaymentDetailRepository;
import cathayfuture.opm.infra.order.repository.mapper.PaymentDetailMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Objects;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/24/22
 */
@Repository
public class PaymentDetailRepositoryImpl implements PaymentDetailRepository {

    private PaymentDetailMapper paymentDetailMapper;

    public PaymentDetailRepositoryImpl(PaymentDetailMapper paymentDetailMapper) {
        this.paymentDetailMapper = paymentDetailMapper;
    }

    @Override
    public PaymentDetailEntity add(PaymentDetailEntity paymentDetail) {
        paymentDetailMapper.insert(paymentDetail);
        return paymentDetail;
    }

    @Override
    public PaymentDetailEntity getLastNotPayByOrderId(Integer orderId) {
        LambdaQueryWrapper<PaymentDetailEntity> queryWrapper = Wrappers.lambdaQuery(PaymentDetailEntity.class)
                .eq(PaymentDetailEntity::getOrderId, orderId)
                .eq(PaymentDetailEntity::getPaymentStatus, PaymentStatusEnum.NO_PAY.getKey())
                .orderByDesc(BaseEntity::getId);
        List<PaymentDetailEntity> paymentDetailEntities = paymentDetailMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(paymentDetailEntities)) {
            return null;
        }
        return paymentDetailEntities.get(0);
    }

    @Override
    public int updateById(PaymentDetailEntity paymentDetailEntity) {
        return paymentDetailMapper.updateById(paymentDetailEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refund(Integer orderId) {
        LambdaQueryWrapper<PaymentDetailEntity> queryWrapper = Wrappers.lambdaQuery(PaymentDetailEntity.class)
                .eq(PaymentDetailEntity::getOrderId, orderId)
                .eq(PaymentDetailEntity::getPaymentStatus, PaymentStatusEnum.PAID.getKey());
        List<PaymentDetailEntity> paymentDetailEntities = paymentDetailMapper.selectList(queryWrapper);

        PaymentDetailEntity updatingPaymentDetailEntity = new PaymentDetailEntity();
        updatingPaymentDetailEntity.setPaymentStatus(PaymentStatusEnum.REFUND.getKey());
        int updateNum = paymentDetailMapper.update(updatingPaymentDetailEntity, Wrappers.lambdaUpdate(PaymentDetailEntity.class)
                .eq(PaymentDetailEntity::getOrderId, orderId)
                .eq(PaymentDetailEntity::getPaymentStatus, PaymentStatusEnum.PAID.getKey()));
        if (Objects.equal(updateNum, paymentDetailEntities.size())) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }
}
