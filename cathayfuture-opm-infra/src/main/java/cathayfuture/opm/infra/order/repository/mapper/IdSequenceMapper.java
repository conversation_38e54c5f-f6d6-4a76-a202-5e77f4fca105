package cathayfuture.opm.infra.order.repository.mapper;

import cathayfuture.opm.domain.order.IdSequenceEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2022/8/27
 */
public interface IdSequenceMapper extends BaseMapper<IdSequenceEntity> {

    @Insert("insert into cf_ord_id_sequence (prefix,sequence) values(#{prefix},#{increment}) on duplicate key update sequence = sequence+#{increment}")
    void incrSequence(@Param("prefix") String prefix, @Param("increment") Integer increment);

    @Select("select sequence from cf_ord_id_sequence where prefix = #{prefix}")
    Integer getSequence(@Param("prefix") String prefix);
}
