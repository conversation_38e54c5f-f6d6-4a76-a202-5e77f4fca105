package cathayfuture.opm.infra.order.repository;

import cathayfuture.opm.domain.order.OrderEntity;
import cathayfuture.opm.domain.order.OrderRecoreEntity;
import cathayfuture.opm.domain.order.gateway.repository.OrderRecoreRepository;
import cathayfuture.opm.infra.common.basequery.wrapper.ExtQueryWrapper;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cathayfuture.opm.infra.order.repository.mapper.OrderRecoreMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/4/20 14:16
 */

@Repository
public class OrderRecoreRepositoryImpl implements OrderRecoreRepository {
    private OrderRecoreMapper mapper;

    public OrderRecoreRepositoryImpl(OrderRecoreMapper mapper) {
        this.mapper = mapper;
    }

    public OrderRecoreEntity getByTypeAndDate(Integer type, LocalDate date){
        if(Objects.isNull(date)){
            date = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        }

        List<OrderRecoreEntity> orderRecoreEntities = mapper.selectList(ExtQueryWrapper.newInstance(OrderRecoreEntity.class)
                .eq(LambdaUtils.column(OrderRecoreEntity::getOrderType), type)
        );

        return orderRecoreEntities.stream()
                .findFirst()
                .orElse(new OrderRecoreEntity());
    }


    @Override
    public Integer addBatch(List<OrderRecoreEntity> list) {
        return mapper.insertBatchSomeColumn(list);
    }
}
