package cathayfuture.opm.infra.action.repository;

import cathayfuture.opm.domain.action.ActionEntity;
import cathayfuture.opm.domain.action.repository.ActionRepository;
import cathayfuture.opm.infra.action.repository.mapper.ActionMapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 20220824
 */
@Repository
public class ActionRepositoryImpl implements ActionRepository {

    private ActionMapper mapper;

    public ActionRepositoryImpl(ActionMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public ActionEntity getById(Integer id) {
        return mapper.selectById(id);
    }


    @Override
    public ActionEntity add(ActionEntity actionEntity) {
        mapper.insert(actionEntity);
        return actionEntity;
    }

    @Override
    public int updateById(ActionEntity actionEntity) {
        return mapper.updateById(actionEntity);
    }

    @Override
    public Integer addBatch(List<ActionEntity> actionEntityList) {
        return mapper.insertBatchSomeColumn(actionEntityList);
    }
}
