package cathayfuture.opm.infra.attendance.repository;

import cathayfuture.opm.domain.attendance.AskedForLeaveEntity;
import cathayfuture.opm.domain.attendance.repository.AskedForLeaveRepository;
import cathayfuture.opm.infra.attendance.repository.mapper.AskedForLeaveMapper;
import cathayfuture.opm.infra.common.basequery.wrapper.ExtQueryWrapper;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cn.hutool.core.collection.CollectionUtil;
import org.springframework.stereotype.Repository;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 请假Repository
 * @date 2023/3/27 15:02
 */
@Repository
public class AskedForLeaveRepositoryImpl implements AskedForLeaveRepository {

    private AskedForLeaveMapper mapper;

    public AskedForLeaveRepositoryImpl(AskedForLeaveMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public AskedForLeaveEntity getByid(Integer id){
       return mapper.selectById(id);
    }

    @Override
    public Boolean add(AskedForLeaveEntity entity){
        int insert = mapper.insert(entity);
        return insert == 1;
    }

    @Override
    public Boolean delete(Integer id){
        AskedForLeaveEntity entity = new AskedForLeaveEntity();
        entity.setId(id);
        entity.setDr(1);
        int i = mapper.deleteById(id);
        return i==1;
    }

    @Override
    public Boolean updateById(AskedForLeaveEntity entity){
        int i = mapper.updateById(entity);
        return i==1;
    }

    @Override
    public List<AskedForLeaveEntity> findListByStuId(Integer stuId){
        List<AskedForLeaveEntity> entityList = mapper.selectList(ExtQueryWrapper.newInstance(AskedForLeaveEntity.class)
                .eq(LambdaUtils.column(AskedForLeaveEntity::getStuId), stuId)
        );
        return entityList;
    }
    @Override
    public List<AskedForLeaveEntity> findListByStuIds(List<Integer> stuIds){
        List<AskedForLeaveEntity> entityList = mapper.selectList(ExtQueryWrapper.newInstance(AskedForLeaveEntity.class)
                .in(LambdaUtils.column(AskedForLeaveEntity::getStuId), stuIds)
                .orderByAsc(LambdaUtils.column(AskedForLeaveEntity::getCreateTime))
        );
        return entityList;
    }

    private List<String> getBetweenMonth(String minDate,String maxDate){
        List<String> list = new ArrayList<>();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();

        try {
            min.setTime(sdf.parse(minDate));
            min.set(min.get(Calendar.YEAR),min.get(Calendar.MONTH),1);

            max.setTime(sdf.parse(maxDate));
            max.set(max.get(Calendar.YEAR),max.get(Calendar.MONTH),2);

            Calendar current = min;
            while (current.before(max)){
                list.add(sdf.format(current.getTime()));
                current.add(Calendar.MONTH,1);
            }


        } catch (ParseException e) {
            e.printStackTrace();
        }
        return list;
    }
}
