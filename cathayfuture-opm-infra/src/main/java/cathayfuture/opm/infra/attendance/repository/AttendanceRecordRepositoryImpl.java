package cathayfuture.opm.infra.attendance.repository;

import cathayfuture.opm.client.attendance.dto.response.AttendanceFirstRespDto;
import cathayfuture.opm.client.attendance.exception.AttendanceRecordSaveException;
import cathayfuture.opm.domain.attendance.AttendanceRecordEntity;
import cathayfuture.opm.domain.attendance.repository.AttendanceRecordRepository;
import cathayfuture.opm.infra.attendance.repository.mapper.AttendanceRecordMapper;
import cathayfuture.opm.infra.common.basequery.ExtBaseMapper;
import cathayfuture.opm.infra.common.basequery.wrapper.ExtQueryWrapper;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 考勤记录
 * @date 2023/3/31 17:12
 */
@Repository
public class AttendanceRecordRepositoryImpl implements AttendanceRecordRepository {

    private AttendanceRecordMapper mapper;

    public AttendanceRecordRepositoryImpl(AttendanceRecordMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public void add(AttendanceRecordEntity entity){
        mapper.insert(entity);
    }

    @Override
    public Integer batchInsert(List<AttendanceRecordEntity> entityList){
        if(CollectionUtil.isEmpty(entityList)){
            return 0;
        }
        return  mapper.batchInsert(entityList);
    }

    @Override
    public void update(AttendanceRecordEntity entity){
        if(Objects.isNull(entity.getId())){
            throw new AttendanceRecordSaveException("AttendanceRecordRepository.update()_参数id是null");
        }
        mapper.updateById(entity);
    }

    @Override
    public List<AttendanceRecordEntity> exist(List<Integer> studentIds, LocalDate currentDate){
        List<AttendanceRecordEntity> entityList = mapper.selectList(ExtQueryWrapper.newInstance(AttendanceRecordEntity.class)
                .in(LambdaUtils.column(AttendanceRecordEntity::getStudentId), studentIds)
                .eq(LambdaUtils.column(AttendanceRecordEntity::getAttendanceDate), currentDate)
        );
        return entityList;
    }

    @Override
    public List<AttendanceRecordEntity> queryByStudentIdAndCurrentDate(Integer studentId ,LocalDate currentDate){
        return exist(Lists.newArrayList(studentId),currentDate);
    }

    @Override
    public List<AttendanceRecordEntity> getListByTargetMonth(LocalDate date){
        LocalDate targetDay = LocalDate.now();
        if(Objects.nonNull(date)){
            targetDay = date;
        }
        LocalDate firstDay = targetDay.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastDay = targetDay.with(TemporalAdjusters.lastDayOfMonth());
        List<AttendanceRecordEntity> entities = mapper.selectList(ExtQueryWrapper.newInstance(AttendanceRecordEntity.class)
                .ge(LambdaUtils.column(AttendanceRecordEntity::getAttendanceDate), firstDay)
                .le(LambdaUtils.column(AttendanceRecordEntity::getAttendanceDate), lastDay)
        );
        return entities;
    }

    @Override
    public List<LocalDate> queryListByLocalDateList(List<LocalDate> list,Integer studentId){
        List<AttendanceRecordEntity> entities = mapper.selectList(ExtQueryWrapper.newInstance(AttendanceRecordEntity.class)
                .in(LambdaUtils.column(AttendanceRecordEntity::getAttendanceDate), list)
        );
        return entities.stream()
                .map(AttendanceRecordEntity::getAttendanceDate)
                .collect(Collectors.toList());

    }

    @Override
    public Map<Integer, LocalDate> queryStudentFirstDayMap(){
        List<AttendanceFirstRespDto> dtos = mapper.queryFirstDayMap();
        Map<Integer, LocalDate> collect = dtos.stream()
                .collect(Collectors.toMap(AttendanceFirstRespDto::getStudentId, AttendanceFirstRespDto::getFirstDay, (o1, o2) -> o2));
        return collect;
    }

}
