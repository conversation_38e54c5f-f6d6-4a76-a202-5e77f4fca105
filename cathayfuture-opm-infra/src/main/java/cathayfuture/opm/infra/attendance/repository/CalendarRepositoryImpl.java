package cathayfuture.opm.infra.attendance.repository;

import cathayfuture.opm.domain.attendance.CalendarEntity;
import cathayfuture.opm.domain.attendance.repository.CalendarRepository;
import cathayfuture.opm.infra.attendance.repository.mapper.CalendarMapper;
import cathayfuture.opm.infra.common.basequery.wrapper.ExtQueryWrapper;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 假期日历
 * @date 2023/4/7 14:59
 */
@Repository
public class CalendarRepositoryImpl implements CalendarRepository {

    private CalendarMapper calendarMapper;

    public CalendarRepositoryImpl(CalendarMapper calendarMapper) {
        this.calendarMapper = calendarMapper;
    }

    @Override
    public CalendarEntity findOneByDate(LocalDate date){
        List<CalendarEntity> list = calendarMapper.selectList(ExtQueryWrapper.newInstance(CalendarEntity.class)
                .eq(LambdaUtils.column(CalendarEntity::getDate), date)
                .orderByDesc(LambdaUtils.column(CalendarEntity::getVersion))
        );
        return list.stream()
                .findFirst()
                .orElse(new CalendarEntity());
    }

    @Override
    public List<CalendarEntity> getTargetMonthDays(LocalDate date){
        LocalDate targetDay = LocalDate.now();
        LocalDate firstDay = targetDay.with(TemporalAdjusters.firstDayOfMonth());
        if(Objects.nonNull(date)){
            targetDay = date;
            firstDay = date;
        }
        LocalDate lastDay = targetDay.with(TemporalAdjusters.lastDayOfMonth());

        List<CalendarEntity> entities = calendarMapper.selectList(ExtQueryWrapper.newInstance(CalendarEntity.class)
                .ge(LambdaUtils.column(CalendarEntity::getDate), firstDay)
                .le(LambdaUtils.column(CalendarEntity::getDate), lastDay)
        );
        return entities;
    }

    @Override
    public List<CalendarEntity> getTargetMonthDays(LocalDate firstDay,LocalDate lastDay){
        List<CalendarEntity> entities = calendarMapper.selectList(ExtQueryWrapper.newInstance(CalendarEntity.class)
                .ge(LambdaUtils.column(CalendarEntity::getDate), firstDay)
                .le(LambdaUtils.column(CalendarEntity::getDate), lastDay)
        );
        return entities;
    }

    @Override
    public String queryVersion(LocalDate targetDate){
        CalendarEntity entity = calendarMapper.selectList(ExtQueryWrapper.newInstance(CalendarEntity.class)
                .eq(LambdaUtils.column(CalendarEntity::getDate), targetDate)
        )
                .stream()
                .sorted(Comparator.comparing(CalendarEntity::getVersion).reversed())
                .limit(1)
                .findAny()
                .orElse(new CalendarEntity());

        return entity.getVersion();
    }

}
