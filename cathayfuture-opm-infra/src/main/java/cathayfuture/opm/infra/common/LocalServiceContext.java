package cathayfuture.opm.infra.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

/**
 * 本地服务上下文，替代原来的ServiceContext
 * 从Spring Security的SecurityContext和配置文件获取用户信息
 * 
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@Component
public class LocalServiceContext {
    
    @Value("${cathay.auth.user-id}")
    private Long configUserId;
    
    @Value("${cathay.auth.username}")
    private String configUserCode;
    
    @Value("${cathay.auth.user-name}")
    private String configUserName;
    
    @Value("${cathay.auth.role-ids}")
    private String configRoleIds;
    
    @Value("${cathay.auth.post-code}")
    private String configPostCode;
    
    @Value("${cathay.auth.post-name}")
    private String configPostName;
    
    // 提供与原ServiceContext相同的API
    public static LocalServiceContext getContext() {
        return SpringContextHolder.getBean(LocalServiceContext.class);
    }
    
    public Long getRequestUserId() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            return configUserId; // 固定用户ID
        }
        return null;
    }
    
    public String getRequestUserCode() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            return configUserCode;
        }
        return null;
    }
    
    public String getRequestTenantIdString() {
        // 返回固定的租户ID
        return "1";
    }
    
    public Long getRequestInstanceId() {
        // 返回固定的实例ID
        return 1L;
    }
    
    public void set(String key, Object value) {
        // 空实现，功能已由SecurityContext接管
        log.debug("LocalServiceContext.set() called with key: {}, value: {}", key, value);
    }
    
    public Object get(String key) {
        // 从配置文件返回固定值或从SecurityContext获取
        return getFixedValue(key);
    }
    
    public void remove(String key) {
        // 空实现，功能已由SecurityContext接管
        log.debug("LocalServiceContext.remove() called with key: {}", key);
    }
    
    private Object getFixedValue(String key) {
        // 根据key返回对应的配置值
        switch (key) {
            case ExtServiceContext.ROLE_ID:
                return configRoleIds;
            case ExtServiceContext.USER_CODE:
                return configUserCode;
            case ExtServiceContext.USER_NAME:
                return configUserName;
            case ExtServiceContext.POST_CODE:
                return configPostCode;
            case ExtServiceContext.POST_NAME:
                return configPostName;
            case ExtServiceContext.USER_ID:
                return String.valueOf(configUserId);
            default:
                return null;
        }
    }
}
