package cathayfuture.opm.infra.common.basequery.method;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlScriptUtils;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

import java.util.List;

public class UpdateAllColumnsMethod extends AbstractMethod
{
	@Override
	public MappedStatement injectMappedStatement(Class<?> mapperClass,Class<?> modelClass, TableInfo tableInfo)
	{
		SqlMethod sqlMethod = SqlMethod.UPDATE;
		
		String sql = String.format(sqlMethod.getSql(),
				tableInfo.getTableName(),
				sqlSet(true, true, tableInfo, true, ENTITY, ENTITY_DOT),
				sqlWhereEntityWrapper(true, tableInfo), sqlComment());
		
		SqlSource sqlSource = languageDriver.createSqlSource(configuration,sql, modelClass);
		return this.addUpdateMappedStatement(mapperClass, modelClass,"updateAllColumns", sqlSource);
	}
	
	protected String sqlSet(boolean logic, boolean ew, TableInfo table,
			boolean judgeAliasNull, String alias, String prefix) 
	{
		String sqlScript = getAllSqlSet(table,logic, prefix);
        if (judgeAliasNull) {
            sqlScript = SqlScriptUtils.convertIf(sqlScript, String.format("%s != null", alias), true);
        }
        if (ew) {
            sqlScript += NEWLINE;
            sqlScript += SqlScriptUtils.convertIf(SqlScriptUtils.unSafeParam(U_WRAPPER_SQL_SET),
                String.format("%s != null and %s != null", WRAPPER, U_WRAPPER_SQL_SET), false);
        }
        sqlScript = SqlScriptUtils.convertSet(sqlScript);
        return sqlScript;
	}

	public static String getAllSqlSet(TableInfo tableInfo,boolean ignoreLogicDelFiled, final String prefix)
	{
		final String newPrefix = prefix == null ? TableInfo.EMPTY : prefix;
		List<TableFieldInfo> fieldList = tableInfo.getFieldList();
		String sql="";
		for(TableFieldInfo field:fieldList)
		{
			if (ignoreLogicDelFiled)
			{
				if(tableInfo.isLogicDelete() && field.isLogicDelete())
				{
					continue;
				}
			}
			sql+=getSqlSet(field,newPrefix)+TableInfo.NEWLINE;

		}
		return sql;
	}

	private static String getSqlSet(TableFieldInfo tableFieldInfo, final String prefix)
	{
		final boolean ignoreIf=false;
		final String newPrefix = prefix == null ? TableFieldInfo.EMPTY : prefix;
		// 默认: column=
		String sqlSet = tableFieldInfo.getColumn() + TableFieldInfo.EQUALS;
		if (StringUtils.isNotBlank(tableFieldInfo.getUpdate())) {
			sqlSet += String.format(tableFieldInfo.getUpdate(), tableFieldInfo.getColumn());
		} else {
			sqlSet += SqlScriptUtils.safeParam(newPrefix + tableFieldInfo.getEl());
		}
		sqlSet += tableFieldInfo.COMMA;
		if (ignoreIf) {
			return sqlSet;
		}
		if (tableFieldInfo.isWithUpdateFill()) {
			// 不进行 if 包裹
			return sqlSet;
		}
		return sqlSet;
	}
}
