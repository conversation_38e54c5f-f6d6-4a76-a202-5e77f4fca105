package cathayfuture.opm.infra.common.basequery.method;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.apache.ibatis.executor.keygen.Jdbc3KeyGenerator;
import org.apache.ibatis.executor.keygen.KeyGenerator;
import org.apache.ibatis.executor.keygen.NoKeyGenerator;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

public class BatchInsertMethod extends AbstractMethod 
{
	@Override
	public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
		SqlMethod sqlMethod = SqlMethod.INSERT_ONE;
		String columnScript =prepareFieldSql(tableInfo);
		final String valueSql = prepareValuesSql(tableInfo);
		final String sqlResult = String.format(sqlMethod.getSql(), tableInfo.getTableName(), columnScript, valueSql);
		SqlSource sqlSource = languageDriver.createSqlSource(configuration, sqlResult, modelClass);
		String keyProperty = null;
		String keyColumn = null;
		KeyGenerator keyGenerator = new NoKeyGenerator();
		// 表包含主键处理逻辑,如果不包含主键当普通字段处理
		if (StringUtils.isNotBlank(tableInfo.getKeyProperty())) {
			if (tableInfo.getIdType() == IdType.AUTO) {
				/** 自增主键 */
				keyGenerator = new Jdbc3KeyGenerator();
				keyProperty = tableInfo.getKeyProperty();
				keyColumn = tableInfo.getKeyColumn();
			} else {
				if (null != tableInfo.getKeySequence()) {
					keyGenerator = TableInfoHelper.genKeyGenerator(getMethod(sqlMethod), tableInfo, builderAssistant);
					keyProperty = tableInfo.getKeyProperty();
					keyColumn = tableInfo.getKeyColumn();
				}
			}
		}
		return this.addInsertMappedStatement(mapperClass, modelClass, "batchInsert", sqlSource, keyGenerator, keyProperty, keyColumn);
	}

    private String prepareFieldSql(TableInfo tableInfo) {
        StringBuilder fieldSql = new StringBuilder();
        if(tableInfo.getKeyColumn()!=null) {
			fieldSql.append(tableInfo.getKeyColumn()).append(",");
		}
        tableInfo.getFieldList().stream().filter(f->!f.equals("id")).forEach(x -> {
            fieldSql.append(x.getColumn()).append(",");
        });
        fieldSql.delete(fieldSql.length() - 1, fieldSql.length());
        fieldSql.insert(0, "(");
        fieldSql.append(")");
        return fieldSql.toString();
    }

	
	
	private String prepareValuesSql(TableInfo tableInfo) {
		final StringBuilder valueSql = new StringBuilder();
		valueSql.append(
				"<foreach collection=\"list\" item=\"item\" open=\"(\" separator=\"),(\" close=\")\">");
		if(tableInfo.getKeyColumn()!=null) {
			valueSql.append("#{item.").append(tableInfo.getKeyProperty()).append("},");
		}
		tableInfo.getFieldList().stream().filter(f->!f.equals("id")).forEach(x -> valueSql.append("#{item.").append(x.getProperty()).append("},"));
		valueSql.delete(valueSql.length() - 1, valueSql.length());
		valueSql.append("</foreach>");
		return valueSql.toString();
	}
	
	
}
