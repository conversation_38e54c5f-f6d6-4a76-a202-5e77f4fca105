package cathayfuture.opm.infra.common.utils;



import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.format.DatePrinter;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

public class DateUtil
{
	// --------------------------------------------------------------------------------------------------------------------------------
	// Normal
	/**
	 * 年月格式：yyyy-MM
	 */
	public static final String NORM_MONTH_PATTERN = "yyyy-MM";

	/**
	 * 简单年月格式：yyyyMM
	 */
	public static final String SIMPLE_MONTH_PATTERN = "yyyyMM";

	/**
	 * 标准日期格式：yyyy-MM-dd
	 */
	public static final String NORM_DATE_PATTERN = "yyyy-MM-dd";

	/**
	 * 标准时间格式：HH:mm:ss
	 */
	public static final String NORM_TIME_PATTERN = "HH:mm:ss";

	/**
	 * 标准日期时间格式，精确到分：yyyy-MM-dd HH:mm
	 */
	public static final String NORM_DATETIME_MINUTE_PATTERN = "yyyy-MM-dd HH:mm";

	/**
	 * 标准日期时间格式，精确到秒：yyyy-MM-dd HH:mm:ss
	 */
	public static final String NORM_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

	/**
	 * 标准日期时间格式，精确到毫秒：yyyy-MM-dd HH:mm:ss.SSS
	 */
	public static final String NORM_DATETIME_MS_PATTERN = "yyyy-MM-dd HH:mm:ss.SSS";

	/**
	 * ISO8601日期时间格式，精确到毫秒：yyyy-MM-dd HH:mm:ss,SSS
	 */
	public static final String ISO8601_PATTERN = "yyyy-MM-dd HH:mm:ss,SSS";

	/**
	 * 标准日期格式：yyyy年MM月dd日
	 */
	public static final String CHINESE_DATE_PATTERN = "yyyy年MM月dd日";
	/**
	 * 标准日期格式：yyyy年MM月dd日 HH时mm分ss秒
	 */
	public static final String CHINESE_DATE_TIME_PATTERN = "yyyy年MM月dd日HH时mm分ss秒";

	// --------------------------------------------------------------------------------------------------------------------------------
	// Pure
	/**
	 * 标准日期格式：yyyyMMdd
	 */
	public static final String PURE_DATE_PATTERN = "yyyyMMdd";

	/**
	 * 标准日期格式：HHmmss
	 */
	public static final String PURE_TIME_PATTERN = "HHmmss";

	/**
	 * 标准日期格式：yyyyMMddHHmmss
	 */
	public static final String PURE_DATETIME_PATTERN = "yyyyMMddHHmmss";

	/**
	 * 标准日期格式：yyyyMMddHHmmssSSS
	 */
	public static final String PURE_DATETIME_MS_PATTERN = "yyyyMMddHHmmssSSS";

	// --------------------------------------------------------------------------------------------------------------------------------
	// Others
	/**
	 * HTTP头中日期时间格式：EEE, dd MMM yyyy HH:mm:ss z
	 */
	public static final String HTTP_DATETIME_PATTERN = "EEE, dd MMM yyyy HH:mm:ss z";
	/**
	 * JDK中日期时间格式：EEE MMM dd HH:mm:ss zzz yyyy
	 */
	public static final String JDK_DATETIME_PATTERN = "EEE MMM dd HH:mm:ss zzz yyyy";
	/**
	 * UTC时间：yyyy-MM-dd'T'HH:mm:ss
	 */
	public static final String UTC_SIMPLE_PATTERN = "yyyy-MM-dd'T'HH:mm:ss";

	/**
	 * UTC时间：yyyy-MM-dd'T'HH:mm:ss'Z'
	 */
	public static final String UTC_PATTERN = "yyyy-MM-dd'T'HH:mm:ss'Z'";
	/**
	 * UTC时间：yyyy-MM-dd'T'HH:mm:ssZ
	 */
	public static final String UTC_WITH_ZONE_OFFSET_PATTERN = "yyyy-MM-dd'T'HH:mm:ssZ";

	/**
	 * UTC时间：yyyy-MM-dd'T'HH:mm:ss.SSS'Z'
	 */
	public static final String UTC_MS_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

	/**
	 * UTC时间：yyyy-MM-dd'T'HH:mm:ssZ
	 */
	public static final String UTC_MS_WITH_ZONE_OFFSET_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";

	public static Date parseStr(String dateCharSequence){
		try {
			return parse(dateCharSequence);
		}
		catch (Exception e){
			e.printStackTrace();
			return null;
		}
	}

	public static Date parse(CharSequence dateCharSequence) throws ParseException
	{
		if (StrUtil.isBlank(dateCharSequence))
		{
			return null;
		}
		String dateStr = dateCharSequence.toString();
		// 去掉两边空格并去掉中文日期中的“日”和“秒”，以规范长度
		dateStr = StrUtil.removeAll(dateStr.trim(), '日', '秒');
		int length = dateStr.length();

		if (NumberUtil.isNumber(dateStr))
		{
			// 纯数字形式
			if (length == PURE_DATETIME_PATTERN.length())
			{
				return parse(dateStr, PURE_DATETIME_PATTERN);
			}
			else if (length == PURE_DATETIME_MS_PATTERN.length())
			{
				return parse(dateStr, PURE_DATETIME_MS_PATTERN);
			}
			else if (length == PURE_DATE_PATTERN.length())
			{
				return parse(dateStr, PURE_DATE_PATTERN);
			}
			else if (length == PURE_TIME_PATTERN.length())
			{
				return parse(dateStr, PURE_TIME_PATTERN);
			}
		}
		else
		{
			if (length == NORM_MONTH_PATTERN.length())
			{
				return parse(dateStr, NORM_MONTH_PATTERN);
			}
			else if (length == NORM_DATE_PATTERN.length())
			{
				return parse(dateStr, NORM_DATE_PATTERN);
			}
			else if (length == NORM_TIME_PATTERN.length())
			{
				return parse(dateStr, NORM_TIME_PATTERN);
			}
			else if (length == NORM_DATETIME_MINUTE_PATTERN.length())
			{
				return parse(dateStr, NORM_DATETIME_MINUTE_PATTERN);
			}
			else if (length == NORM_DATETIME_PATTERN.length())
			{
				return parse(dateStr, NORM_DATETIME_PATTERN);
			}
			else if (length == NORM_DATETIME_MS_PATTERN.length())
			{
				return parse(dateStr, NORM_DATETIME_MS_PATTERN);
			}
		}
		if (StrUtil.contains(dateStr, 'T')) {
			if (StrUtil.contains(dateStr, 'Z')) {
				if (length == "yyyy-MM-dd'T'HH:mm:ss'Z'".length() - 4) {
					return parse(dateStr, UTC_PATTERN);
				}

				if (length == "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'".length() - 4) {
					return parse(dateStr, UTC_MS_PATTERN);
				}
			} else {
				if (length == "yyyy-MM-dd'T'HH:mm:ssZ".length() + 2 || length == "yyyy-MM-dd'T'HH:mm:ssZ".length() + 3) {
					return parse(dateStr,UTC_WITH_ZONE_OFFSET_PATTERN);
				}

				if (length == "yyyy-MM-dd'T'HH:mm:ss.SSSZ".length() + 2 || length == "yyyy-MM-dd'T'HH:mm:ss.SSSZ".length() + 3) {
					return parse(dateStr, UTC_MS_WITH_ZONE_OFFSET_PATTERN);
				}
			}
		}
		// 没有更多匹配的时间格式
		throw new DateException("No format fit for date String [{}] !" + dateStr);
	}

	public static Date parse(CharSequence dateCharSequence, String pattern) throws ParseException
	{
		return new SimpleDateFormat(pattern).parse(StrUtil.str(dateCharSequence));
	}

	public static String format(Date date, String format) {
		if (null != date && !cn.hutool.core.util.StrUtil.isBlank(format)) {
			SimpleDateFormat sdf = new SimpleDateFormat(format);
			if (date instanceof DateTime) {
				TimeZone timeZone = ((DateTime)date).toCalendar().getTimeZone();
				if (null != timeZone) {
					sdf.setTimeZone(timeZone);
				}
			}

			return format((Date)date, (DateFormat)sdf);
		} else {
			return null;
		}
	}

	public static String format(Date date, DatePrinter format) {
		return null != format && null != date ? format.format(date) : null;
	}

	public static String format(Date date, DateFormat format) {
		return null != format && null != date ? format.format(date) : null;
	}

	public static String format(Date date, DateTimeFormatter format) {
		return null != format && null != date ? format.format(date.toInstant()) : null;
	}

	public static String formatDateTime(Date date) {
		return null == date ? null : DatePattern.NORM_DATETIME_FORMAT.format(date);
	}

	public static String formatDate(Date date) {
		return null == date ? null : DatePattern.NORM_DATE_FORMAT.format(date);
	}

	public static String formatTime(Date date) {
		return null == date ? null : DatePattern.NORM_TIME_FORMAT.format(date);
	}

    public static Date calcDate(Date date,int field,int sub) {
		Calendar calendar=Calendar.getInstance();
		if(date!=null) {
			calendar.setTime(date);
		}
		calendar.add(field,sub);
		return calendar.getTime();
    }
}
