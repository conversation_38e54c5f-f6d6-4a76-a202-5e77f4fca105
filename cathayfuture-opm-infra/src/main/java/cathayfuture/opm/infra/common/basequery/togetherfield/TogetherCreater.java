package cathayfuture.opm.infra.common.basequery.togetherfield;

import cathayfuture.opm.infra.common.basequery.DBTransUtil;
import cathayfuture.opm.infra.common.beantools.BeanTools;
import cathayfuture.opm.infra.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;


import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class TogetherCreater
{
	public static void makeTogetherWrapper(QueryWrapper queryWrapper,TogetherFields togetherFields,String groupbyField,Class togetherClass)
	{
	
		if(togetherFields==null||togetherFields.getFields()==null||togetherFields.getFields().size()==0)
		{
			throw new RuntimeException("没有传入togetherFields");
		}

		List<String> columnList=new ArrayList<String>();
		if(StrUtil.isNotBlank(groupbyField))
		{
			columnList.add(getColumnName(togetherClass,groupbyField)+" as "+groupbyField);
		}
		for(int i=0;i<togetherFields.getFields().size();i++)
		{
			TogetherFields.TogetherField field = togetherFields.getFields().get(i);
			String propertyFieldName=getColumnName(togetherClass,field.getFieldName());
			StringBuffer sql=new StringBuffer();
			if(StrUtil.isNotBlank(field.getCast()))
			{
				propertyFieldName="cast("+propertyFieldName+" as "+field.getCast()+")";
			}
			if(field.getTogetherFun()==null)
			{
				sql.append(propertyFieldName);
			}
			else
			{
				StringBuffer propertyStr=new StringBuffer();
				if(field.isCondition())
				{
					Field conditionField= BeanTools.getClassFieldWithOutCglib(togetherClass,field.getCondtionField());
					String elseValue=String.valueOf(field.getElseValue());
					if(field.getElseValue() instanceof String)
					{
						elseValue="'"+field.getElseValue()+"'";
					}
					propertyStr.append("if(")
						.append(DBTransUtil.propertyToColumn(conditionField))
						.append(field.getCondtionOperator())
						.append(field.getCondtionValue())
						.append(",");
					propertyStr.append(propertyFieldName);
					propertyStr.append(",");
					propertyStr.append(elseValue);
					propertyStr.append(")");
				}
				else
				{
					propertyStr.append(propertyFieldName);
				}
				
			
				if(field.getTogetherFun()==TogetherFun.SUM)
				{
					StringBuffer sumStr=new StringBuffer();
					sumStr.append(field.getFunction()).append("(");
					sumStr.append("if("+propertyStr.toString()+" is null,0,"+propertyStr.toString()+")");
					sumStr.append(")");
					
					sql.append("if("+sumStr.toString()+" is null,0,"+sumStr.toString()+")");
				}
				else
				{
					sql.append(field.getFunction()).append("(");
					sql.append(propertyStr.toString());
					sql.append(")");
				}
			
			}
			
			sql.append(" as ").append("\"").append(field.getAsField()==null?field.getFieldName():field.getAsField()).append("\"");
			
			columnList.add(sql.toString());
		}
		queryWrapper.select(columnList.toArray(new String[0]));
		if(StrUtil.isNotBlank(groupbyField))
		{
			queryWrapper.groupBy(getColumnName(togetherClass,groupbyField));
		}
	}

	private static String getColumnName(Class clazz,String name){
		Field propertyField= BeanTools.getClassFieldWithOutCglib(clazz,name);
		if(propertyField==null){
			return name;
		}
		return DBTransUtil.propertyToColumn(propertyField);
	}

}
