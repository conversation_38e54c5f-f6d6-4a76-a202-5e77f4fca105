package cathayfuture.opm.infra.common;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.extension.injector.methods.InsertBatchSomeColumn;

import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/31/22
 */
public class MySqlInjector extends DefaultSqlInjector {
    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass) {
        // TODO Auto-generated method stub
        List<AbstractMethod> methodList = super.getMethodList(mapperClass);
        methodList.add(new InsertBatchSomeColumn().setPredicate(t -> !t.isLogicDelete() || !"reversion".equals(t.getProperty()))); // 添加InsertBatchSomeColumn方法
        return methodList;
    }
}
