package cathayfuture.opm.infra.common.basequery.wrapper;


import cathayfuture.opm.infra.common.basequery.DBTransUtil;
import cathayfuture.opm.infra.common.basequery.togetherfield.TogetherFields;
import cathayfuture.opm.infra.common.basequery.togetherfield.TogetherFun;
import cathayfuture.opm.infra.common.beantools.BeanTools;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class TogetherWrapper<T> extends ExtQueryWrapper<T>
{
	
	
	protected TogetherWrapper()
	{
		
	}
	
	public static <T> TogetherWrapper<T> newInstance(TogetherFields togetherFields, Class<T> clazz)
	{
		TogetherWrapper<T> wrapper=new TogetherWrapper<T>();
		wrapper.setEntityClass(clazz);
		wrapper.makeTogetherWrapper(togetherFields);
		return wrapper;
	}

	protected String getTableAliasDot(Class clazz){
		if(clazz==null){
			return "";
		}
		return getTableAlias()+".";
	}


	protected void makeTogetherWrapper(TogetherFields togetherFields)
	{
	
		if(togetherFields==null||togetherFields.getFields()==null||togetherFields.getFields().size()==0)
		{
			throw new RuntimeException("没有传入togetherFields");
		}

		Class togetherClass=this.getEntityClass();
		List<String> columnList=new ArrayList<String>();
		if(CollectionUtil.isNotEmpty(togetherFields.getGroupByField()))
		{
			for (int i = 0; i < togetherFields.getGroupByField().size(); i++) {
				Class clazz=togetherFields.getGroupByTable().get(i)==null?togetherClass:togetherFields.getGroupByTable().get(i);
				columnList.add(getTableAliasDot(togetherFields.getGroupByTable().get(i))+getColumnName(clazz,togetherFields.getGroupByField().get(i))+" as "+togetherFields.getGroupByAlias().get(i));
			}
		}
		for(int i=0;i<togetherFields.getFields().size();i++)
		{
			TogetherFields.TogetherField field = togetherFields.getFields().get(i);
			String propertyFieldName=getColumnName(togetherClass,field.getFieldName());
			StringBuffer sql=new StringBuffer();
			if(StrUtil.isNotBlank(field.getCast()))
			{
				propertyFieldName="cast("+propertyFieldName+" as "+field.getCast()+")";
			}
			if(field.getTogetherFun()==null)
			{
				sql.append(propertyFieldName);
			}
			else
			{
				StringBuffer propertyStr=new StringBuffer();
				if(field.isCondition())
				{
					Field conditionField= BeanTools.getClassFieldWithOutCglib(togetherClass,field.getCondtionField());
					String elseValue=String.valueOf(field.getElseValue());
					if(field.getElseValue() instanceof String)
					{
						elseValue="'"+field.getElseValue()+"'";
					}
					propertyStr.append("if(")
						.append(getFieldColumnName(conditionField))
						.append(field.getCondtionOperator())
						.append(field.getCondtionValue())
						.append(",");
					propertyStr.append(propertyFieldName);
					propertyStr.append(",");
					propertyStr.append(elseValue);
					propertyStr.append(")");
				}
				else
				{
					propertyStr.append(propertyFieldName);
				}
				
			
				if(field.getTogetherFun()== TogetherFun.SUM)
				{
					StringBuffer sumStr=new StringBuffer();
					sumStr.append(field.getFunction()).append("(");
					sumStr.append("if("+propertyStr.toString()+" is null,0,"+propertyStr.toString()+")");
					sumStr.append(")");
					
					sql.append("if("+sumStr.toString()+" is null,0,"+sumStr.toString()+")");
				}
				else
				{
					sql.append(field.getFunction()).append("(");
					sql.append(propertyStr.toString());
					sql.append(")");
				}
			
			}
			
			sql.append(" as ").append("\"").append(field.getAsField()==null?field.getFieldName():field.getAsField()).append("\"");
			
			columnList.add(sql.toString());
		}
		this.select(columnList.toArray(new String[0]));
		if(CollectionUtil.isNotEmpty(togetherFields.getGroupByField()))
		{

			String[] groupBys=new String[togetherFields.getGroupByField().size()];
			for (int i = 0; i < togetherFields.getGroupByField().size(); i++) {
				Class clazz=togetherFields.getGroupByTable().get(i)==null?togetherClass:togetherFields.getGroupByTable().get(i);
				groupBys[i]=getTableAliasDot(togetherFields.getGroupByTable().get(i))+getColumnName(clazz,togetherFields.getGroupByField().get(i));
			}
			this.groupBy(groupBys);
		}
	}

	private static String getColumnName(Class clazz,String name){
		Field propertyField= BeanTools.getClassFieldWithOutCglib(clazz,name);
		if(propertyField==null){
			return name;
		}
		return DBTransUtil.propertyToColumn(propertyField);
	}

	public static String propertyToColumn(String property, Class clz) 
	{
		if (clz != null) {
			try {
				Field field = BeanTools.getClassFieldWithOutCglib(clz, property);
				if(field==null)
				{
					throw new RuntimeException("在"+clz.getName()+"中没有"+property);
				}
				return getFieldColumnName(field);
			}catch (Exception e) {
				e.printStackTrace();
			}
		}
		return "" +  StrUtil.toUnderlineCase(property) + "";
	}
	
	
	//增删改用,不带as转换
	public static String getFieldColumnName(Field field)
	{
		return "" + StrUtil.toUnderlineCase(field.getName()) + "";
	}
}
