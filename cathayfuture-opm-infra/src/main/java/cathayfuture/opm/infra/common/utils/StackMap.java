package cathayfuture.opm.infra.common.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.Stack;

public class StackMap<K,V> {
    private Map<K, Stack<V>> map=new HashMap<>();

    public int size() {
        return map.size();
    }

    public boolean isEmpty() {
        return map.isEmpty();
    }

    public boolean containsKey(K key) {
        return map.containsKey(key);
    }

    public Stack<V> get(K key) {
        return map.get(key);
    }

    public V put(K key, V value) {
        if(map.get(key)==null){
            map.put(key,new Stack<V>());
        }
        map.get(key).push(value);
        return value;
    }

    public V pop(K key) {
        if(map.get(key)==null){
           return null;
        }
        return map.get(key).pop();
    }
}
