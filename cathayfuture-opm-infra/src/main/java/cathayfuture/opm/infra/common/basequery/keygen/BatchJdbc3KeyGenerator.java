//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cathayfuture.opm.infra.common.basequery.keygen;

import cathayfuture.opm.infra.common.beantools.BeanTools;
import org.apache.ibatis.binding.MapperMethod.ParamMap;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.ExecutorException;
import org.apache.ibatis.executor.keygen.Jdbc3KeyGenerator;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.reflection.ArrayUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.defaults.DefaultSqlSession.StrictMap;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;
import org.apache.ibatis.type.TypeHandlerRegistry;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.AbstractMap.SimpleImmutableEntry;
import java.util.*;
import java.util.Map.Entry;

/**
 *
 * PreparedStatementHandler中的该方法 判断是否 Jdbc3KeyGenerator
 *
 *      protected Statement instantiateStatement(Connection connection) throws SQLException {
 *         String sql = this.boundSql.getSql();
 *         if (this.mappedStatement.getKeyGenerator() instanceof Jdbc3KeyGenerator) {
 *             String[] keyColumnNames = this.mappedStatement.getKeyColumns();
 *             return keyColumnNames == null ? connection.prepareStatement(sql, 1) : connection.prepareStatement(sql, keyColumnNames);
 *         } else {
 *             return this.mappedStatement.getResultSetType() == ResultSetType.DEFAULT ? connection.prepareStatement(sql) : connection.prepareStatement(sql, this.mappedStatement.getResultSetType().getValue(), 1007);
 *         }
 *     }
 */
public class BatchJdbc3KeyGenerator extends Jdbc3KeyGenerator {
    private static final String SECOND_GENERIC_PARAM_NAME = "param2";
    public static final Jdbc3KeyGenerator INSTANCE = new Jdbc3KeyGenerator();
    private static final String MSG_TOO_MANY_KEYS = "Too many keys are generated. There are only %d target objects. You either specified a wrong 'keyProperty' or encountered a driver bug like #1523.";

    public BatchJdbc3KeyGenerator() {
    }

    @Override
    public void processBefore(Executor executor, MappedStatement ms, Statement stmt, Object parameter) {
    }

    @Override
    public void processAfter(Executor executor, MappedStatement ms, Statement stmt, Object parameter) {
        this.processBatch(ms, stmt, parameter);
    }

    @Override
    public void processBatch(MappedStatement ms, Statement stmt, Object parameter) {
        String[] keyProperties = ms.getKeyProperties();
        if (keyProperties != null && keyProperties.length != 0) {
            try {
                ResultSet rs = stmt.getGeneratedKeys();
                Throwable var6 = null;

                try {
                    ResultSetMetaData rsmd = rs.getMetaData();
                    Configuration configuration = ms.getConfiguration();
                    if (rsmd.getColumnCount() >= keyProperties.length) {
                        this.assignKeys(configuration, rs, rsmd, keyProperties, parameter);
                    }
                } catch (Throwable var17) {
                    var6 = var17;
                    throw var17;
                } finally {
                    if (rs != null) {
                        if (var6 != null) {
                            try {
                                rs.close();
                            } catch (Throwable var16) {
                                var6.addSuppressed(var16);
                            }
                        } else {
                            rs.close();
                        }
                    }

                }

            } catch (Exception var19) {
                throw new ExecutorException("Error getting generated key or setting result to parameter object. Cause: " + var19, var19);
            }
        }
    }

    private void assignKeys(Configuration configuration, ResultSet rs, ResultSetMetaData rsmd, String[] keyProperties, Object parameter) throws SQLException {
        if (!(parameter instanceof ParamMap) && !(parameter instanceof StrictMap)) {
            if (parameter instanceof ArrayList && !((ArrayList)parameter).isEmpty() && ((ArrayList)parameter).get(0) instanceof ParamMap) {
                this.assignKeysToParamMapList(configuration, rs, rsmd, keyProperties, (ArrayList)parameter);
            } else {
                boolean list=false;
                if(parameter instanceof Map){
                    Map parameterMap=(Map)parameter;
                    if(parameterMap.size()==1){
                        Object param=new ArrayList(parameterMap.values()).get(0);
                        if(param instanceof List){
                            this.assignKeysToParamMapList(configuration, rs, rsmd, keyProperties, (ArrayList)param);
                            list=true;
                        }
                    }
                }
                if(!list) {
                    this.assignKeysToParam(configuration, rs, rsmd, keyProperties, parameter);
                }
            }
        } else {
            this.assignKeysToParamMap(configuration, rs, rsmd, keyProperties, (Map)parameter);
        }

    }

    private void assignKeysToParam(Configuration configuration, ResultSet rs, ResultSetMetaData rsmd, String[] keyProperties, Object parameter) throws SQLException {
        Collection<?> params = collectionize(parameter);
        if (!params.isEmpty()) {
            List<KeyAssigner> assignerList = new ArrayList();

            for(int i = 0; i < keyProperties.length; ++i) {
                assignerList.add(new KeyAssigner(configuration, rsmd, i + 1, (String)null, keyProperties[i]));
            }

            Iterator iterator = params.iterator();

            while(rs.next()) {
                if (!iterator.hasNext()) {
                    throw new ExecutorException(String.format("Too many keys are generated. There are only %d target objects. You either specified a wrong 'keyProperty' or encountered a driver bug like #1523.", params.size()));
                }

                Object param = iterator.next();
                assignerList.forEach((x) -> {
                    x.assign(rs, param);
                });
            }

        }
    }

    private void assignKeysToParamMapList(Configuration configuration, ResultSet rs, ResultSetMetaData rsmd, String[] keyProperties, ArrayList paramMapList) throws SQLException {
        if(paramMapList==null || paramMapList.isEmpty()){
            return ;
        }

        if(paramMapList.get(0) instanceof ParamMap) {
            Iterator<ParamMap<?>> iterator = paramMapList.iterator();
            List<KeyAssigner> assignerList = new ArrayList();

            for (long counter = 0L; rs.next(); ++counter) {
                if (!iterator.hasNext()) {
                    throw new ExecutorException(String.format("Too many keys are generated. There are only %d target objects. You either specified a wrong 'keyProperty' or encountered a driver bug like #1523.", counter));
                }

                ParamMap<?> paramMap = (ParamMap) iterator.next();
                if (assignerList.isEmpty()) {
                    for (int i = 0; i < keyProperties.length; ++i) {
                        assignerList.add(this.getAssignerForParamMap(configuration, rsmd, i + 1, paramMap, keyProperties[i], keyProperties, false).getValue());
                    }
                }

                assignerList.forEach((x) -> {
                    x.assign(rs, paramMap);
                });
            }
        }
        else {
            for (long counter = 0L; rs.next(); ++counter) {
                BeanTools.setProperty(paramMapList,keyProperties[0],rs.getObject(1));
            }
        }
    }

    private void assignKeysToParamMap(Configuration configuration, ResultSet rs, ResultSetMetaData rsmd, String[] keyProperties, Map<String, ?> paramMap) throws SQLException {
        if (!paramMap.isEmpty()) {
            Map<String, Entry<Iterator<?>, List<KeyAssigner>>> assignerMap = new HashMap();

            for(int i = 0; i < keyProperties.length; ++i) {
                Entry<String, KeyAssigner> entry = this.getAssignerForParamMap(configuration, rsmd, i + 1, paramMap, keyProperties[i], keyProperties, true);
                Entry<Iterator<?>, List<KeyAssigner>> iteratorPair = (Entry)assignerMap.computeIfAbsent(entry.getKey(), (k) -> {
                    return entry(collectionize(paramMap.get(k)).iterator(), new ArrayList());
                });
                ((List)iteratorPair.getValue()).add(entry.getValue());
            }

            for(long counter = 0L; rs.next(); ++counter) {
                Iterator var13 = assignerMap.values().iterator();

                while(var13.hasNext()) {
                    Entry<Iterator<?>, List<KeyAssigner>> pair = (Entry)var13.next();
                    if (!((Iterator)pair.getKey()).hasNext()) {
                        throw new ExecutorException(String.format("Too many keys are generated. There are only %d target objects. You either specified a wrong 'keyProperty' or encountered a driver bug like #1523.", counter));
                    }

                    Object param = ((Iterator)pair.getKey()).next();
                    ((List<KeyAssigner>)pair.getValue()).forEach((x) -> {
                        x.assign(rs, param);
                    });
                }
            }

        }
    }

    private Entry<String, KeyAssigner> getAssignerForParamMap(Configuration config, ResultSetMetaData rsmd, int columnPosition, Map<String, ?> paramMap, String keyProperty, String[] keyProperties, boolean omitParamName) {
        Set<String> keySet = paramMap.keySet();
        boolean singleParam = !keySet.contains("param2");
        int firstDot = keyProperty.indexOf(46);
        if (firstDot == -1) {
            if (singleParam) {
                return this.getAssignerForSingleParam(config, rsmd, columnPosition, paramMap, keyProperty, omitParamName);
            } else {
                throw new ExecutorException("Could not determine which parameter to assign generated keys to. Note that when there are multiple parameters, 'keyProperty' must include the parameter name (e.g. 'param.id'). Specified key properties are " + ArrayUtil.toString(keyProperties) + " and available parameters are " + keySet);
            }
        } else {
            String paramName = keyProperty.substring(0, firstDot);
            if (keySet.contains(paramName)) {
                String argParamName = omitParamName ? null : paramName;
                String argKeyProperty = keyProperty.substring(firstDot + 1);
                return entry(paramName, new KeyAssigner(config, rsmd, columnPosition, argParamName, argKeyProperty));
            } else if (singleParam) {
                return this.getAssignerForSingleParam(config, rsmd, columnPosition, paramMap, keyProperty, omitParamName);
            } else {
                throw new ExecutorException("Could not find parameter '" + paramName + "'. Note that when there are multiple parameters, 'keyProperty' must include the parameter name (e.g. 'param.id'). Specified key properties are " + ArrayUtil.toString(keyProperties) + " and available parameters are " + keySet);
            }
        }
    }

    private Entry<String, KeyAssigner> getAssignerForSingleParam(Configuration config, ResultSetMetaData rsmd, int columnPosition, Map<String, ?> paramMap, String keyProperty, boolean omitParamName) {
        String singleParamName = nameOfSingleParam(paramMap);
        String argParamName = omitParamName ? null : singleParamName;
        return entry(singleParamName, new KeyAssigner(config, rsmd, columnPosition, argParamName, keyProperty));
    }

    private static String nameOfSingleParam(Map<String, ?> paramMap) {
        return (String)paramMap.keySet().iterator().next();
    }

    private static Collection<?> collectionize(Object param) {
        if (param instanceof Collection) {
            return (Collection)param;
        } else {
            return param instanceof Object[] ? Arrays.asList((Object[])((Object[])param)) : Arrays.asList(param);
        }
    }

    private static <K, V> Entry<K, V> entry(K key, V value) {
        return new SimpleImmutableEntry(key, value);
    }

    private class KeyAssigner {
        private final Configuration configuration;
        private final ResultSetMetaData rsmd;
        private final TypeHandlerRegistry typeHandlerRegistry;
        private final int columnPosition;
        private final String paramName;
        private final String propertyName;
        private TypeHandler<?> typeHandler;

        protected KeyAssigner(Configuration configuration, ResultSetMetaData rsmd, int columnPosition, String paramName, String propertyName) {
            this.configuration = configuration;
            this.rsmd = rsmd;
            this.typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            this.columnPosition = columnPosition;
            this.paramName = paramName;
            this.propertyName = propertyName;
        }

        protected void assign(ResultSet rs, Object param) {
            if (this.paramName != null) {
                param = ((ParamMap)param).get(this.paramName);
            }

            MetaObject metaParam = this.configuration.newMetaObject(param);

            try {
                if (this.typeHandler == null) {
                    if (!metaParam.hasSetter(this.propertyName)) {
                        throw new ExecutorException("No setter found for the keyProperty '" + this.propertyName + "' in '" + metaParam.getOriginalObject().getClass().getName() + "'.");
                    }

                    Class<?> propertyType = metaParam.getSetterType(this.propertyName);
                    this.typeHandler = this.typeHandlerRegistry.getTypeHandler(propertyType, JdbcType.forCode(this.rsmd.getColumnType(this.columnPosition)));
                }

                if (this.typeHandler != null) {
                    Object value = this.typeHandler.getResult(rs, this.columnPosition);
                    metaParam.setValue(this.propertyName, value);
                }

            } catch (SQLException var5) {
                throw new ExecutorException("Error getting generated key or setting result to parameter object. Cause: " + var5, var5);
            }
        }
    }
}
