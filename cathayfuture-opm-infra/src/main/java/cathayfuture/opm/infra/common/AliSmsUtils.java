package cathayfuture.opm.infra.common;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ljk
 */
@Slf4j
@Component
public class AliSmsUtils {

    private static String accessKeyId;
    private static String accessKeySecret;
    private static String signName;
    private static String templateCode;
    private static String templateParamName;

    @Value("${sms.ali.accessKeyId}")
    public void setAccessKeyId(String accessKeyId) {
        AliSmsUtils.accessKeyId = accessKeyId;
    }

    @Value("${sms.ali.accessKeySecret}")
    public void setAccessKeySecret(String accessKeySecret) {
        AliSmsUtils.accessKeySecret = accessKeySecret;
    }

    @Value("${sms.ali.signName}")
    public void setSignName(String signName) {
        AliSmsUtils.signName = signName;
    }

    @Value("${sms.ali.templateCode}")
    public void setTemplateCode(String templateCode) {
        AliSmsUtils.templateCode = templateCode;
    }

    @Value("${sms.ali.templateParamName}")
    public void setTemplateParamName(String templateParamName) {
        AliSmsUtils.templateParamName = templateParamName;
    }

    private static Client client;

    public static void initClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = "dysmsapi.aliyuncs.com";
        try {
            client = new Client(config);
        } catch (Exception e) {
            log.error("初始化ali短信client失败", e);
            throw e;
        }

    }

    private static Client getClient() throws Exception {
        if (client == null) {
            synchronized (AliSmsUtils.class) {
                if (client == null) {
                    initClient();
                }
            }
        }
        return client;
    }

    public static void sendSmsVerificationCode(String phoneNumber, String smsVerificationCode) {
        try {
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phoneNumber)
                    .setSignName(signName)
                    .setTemplateCode(templateCode)
                    .setTemplateParam(new JSONObject().fluentPut(templateParamName, smsVerificationCode).toJSONString());
            RuntimeOptions runtime = new RuntimeOptions();
            log.info("开始发送短信验证码，request[{}]", JSONObject.toJSONString(sendSmsRequest));
            SendSmsResponse sendSmsResponse = getClient().sendSmsWithOptions(sendSmsRequest, runtime);
            log.info("结束发送短信验证码，response[{}]", JSONObject.toJSONString(sendSmsResponse));
        } catch (TeaException error) {
            log.error("发送短信失败，msg[{}]", error.message, error);
            throw error;
        } catch (Exception e) {
            TeaException error = new TeaException(e.getMessage(), e);
            log.error("发送短信失败，msg[{}]", error.message, error);
            throw error;
        }

    }

}
