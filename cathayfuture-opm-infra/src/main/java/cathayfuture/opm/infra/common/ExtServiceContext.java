package cathayfuture.opm.infra.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Map;

/**
 * 描述:
 *
 * <AUTHOR>
 * @since 8/29/22
 */
@Slf4j
@Component
public class ExtServiceContext {

    private static final String BASE_KEY = "x-dtyunxi-context-";
    public static final String ROLE_ID = "yes.req.roleId";
    public static final String USER_CODE = "yes.req.userCode";
    public static final String USER_NAME = "yes.req.userName";
    public static final String POST_CODE = "yes.req.postCode";
    public static final String POST_NAME = "yes.req.postName";

    public static final String USER_ID = "yes.req.userId";

    protected static final String[] CONTEXT_KEYS = new String[]{ROLE_ID, USER_CODE, USER_NAME, POST_CODE, POST_NAME};

    private static String configRoleIds;
    private static String configUserName;
    private static String configUserCode;
    private static String configPostCode;
    private static String configPostName;

    @Value("${cathay.auth.role-ids}")
    public void setConfigRoleIds(String roleIds) {
        configRoleIds = roleIds;
    }

    @Value("${cathay.auth.user-name}")
    public void setConfigUserName(String userName) {
        configUserName = userName;
    }

    @Value("${cathay.auth.username}")
    public void setConfigUserCode(String userCode) {
        configUserCode = userCode;
    }

    @Value("${cathay.auth.post-code}")
    public void setConfigPostCode(String postCode) {
        configPostCode = postCode;
    }

    @Value("${cathay.auth.post-name}")
    public void setConfigPostName(String postName) {
        configPostName = postName;
    }

    public static String getRoleId() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            return configRoleIds;
        }
        return null;
    }

    public static String getUserCode() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            return configUserCode;
        }
        return null;
    }

    public static String getUserName() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            log.info("当前的用户名称是,[{}]", configUserName);
            return configUserName;
        }
        return null;
    }

    public static String getPostCode() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            return configPostCode;
        }
        return null;
    }

    public static String getPostName() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            return configPostName;
        }
        return null;
    }


    public static String getHeaderKey(String key) {
        return BASE_KEY + key.toLowerCase();
    }

    // 保留向后兼容的方法，但标记为deprecated
    @Deprecated
    public static void put(String key, Object value) {
        log.warn("ExtServiceContext.put()已废弃，请使用Spring Security");
    }

    @Deprecated
    public static void init(Map<String, Object> headers) {
        log.warn("ExtServiceContext.init()已废弃，请使用Spring Security");
    }

}