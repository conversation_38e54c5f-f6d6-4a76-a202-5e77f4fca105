package cathayfuture.opm.infra.common;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.aop.framework.AopContext;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AopContextUtils {

    public static <T> T getProxy(T t) {
        try {
            T currentProxy = (T) AopContext.currentProxy();
            if (currentProxy.getClass().getSuperclass().equals(t.getClass())) {
                return currentProxy;
            }
        } catch (IllegalStateException e) {
        }
        return t;
    }
}