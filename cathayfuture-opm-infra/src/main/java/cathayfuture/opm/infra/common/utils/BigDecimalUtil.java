package cathayfuture.opm.infra.common.utils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 描述：BigDecimal 帮助类
 *
 * <AUTHOR>
 * <p>
 * Created on 2022-01-14 14:48
 **/
public class BigDecimalUtil {

    /**
     * 如果参数 param 为null 返回BigDecimal.ZERO
     *
     * @param param BigDecimal类型参数
     * @return 非空BigDecimal
     */
    public static BigDecimal ifNullReturnZero(BigDecimal param) {
        if (Objects.isNull(param)) {
            return BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return param;
    }
    public static BigDecimal roundToTwoDecimalPlaces(BigDecimal param) {
        param = ifNullReturnZero(param);
        return param.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal min(List<BigDecimal> params) {
        return params.stream().min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
    }
    public static Boolean lessThanOrEqualToZero(BigDecimal value){
        if (Objects.isNull(value)) {
            return Boolean.TRUE;
        }
        return value.compareTo(BigDecimal.ZERO) <= 0;
    }
    /**
     * 取最小值
     *
     * @param values 要比较数组
     * @return 最小值
     */
    public static BigDecimal min(BigDecimal... values) {
        if (values.length == 0) {
            throw new RuntimeException("参数不能为空");
        }
        BigDecimal minValue = values[0];
        for (BigDecimal bigDecimal : values) {
            minValue = minValue.min(bigDecimal);
        }
        return minValue;
    }


    public static BigDecimal add(BigDecimal orial,Object param) {
        if (Objects.isNull(param)) {
            return orial;
        }
        BigDecimal second=null;
        if(param instanceof BigDecimal){
            second= (BigDecimal) param;
        }
        else{
            second=new BigDecimal(String.valueOf(param));
        }
        if (Objects.isNull(orial)) {
            return second;
        }
        return orial.add(second);
    }

    public static BigDecimal sub(BigDecimal orial,Object param) {
        if (Objects.isNull(param)) {
            return orial;
        }
        BigDecimal second=null;
        if(param instanceof BigDecimal){
            second= (BigDecimal) param;
        }
        else{
            second=new BigDecimal(String.valueOf(param));
        }
        if (Objects.isNull(orial)) {
            return new BigDecimal(0).subtract(second);
        }
        return orial.subtract(second);
    }

}
