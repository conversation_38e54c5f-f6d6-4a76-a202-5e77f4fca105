package cathayfuture.opm.infra.common.basequery.wrapper;

import cathayfuture.opm.infra.common.basequery.TableInfoMaker;
import cathayfuture.opm.infra.common.basequery.exception.CommonSQLException;
import cathayfuture.opm.infra.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import com.baomidou.mybatisplus.core.conditions.SharedString;
import com.baomidou.mybatisplus.core.conditions.segments.MergeSegments;
import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;


public class ExtUpdateWrapper<T> extends AbstractWrapper<T, String, ExtUpdateWrapper<T>> implements Update<ExtUpdateWrapper<T>, String> {

	private final List<String> sqlSet;

	private ExtUpdateWrapper()
	{
		super.initNeed();
		this.sqlSet = new ArrayList();
	}

	public Class getEntityClass(){
		return super.getEntityClass();
	}

	private ExtUpdateWrapper(T entity, List<String> sqlSet, AtomicInteger paramNameSeq, Map<String, Object> paramNameValuePairs, MergeSegments mergeSegments, SharedString lastSql, SharedString sqlComment, SharedString sqlFirst) {
		super.setEntity(entity);
		this.sqlSet = sqlSet;
		this.paramNameSeq = paramNameSeq;
		this.paramNameValuePairs = paramNameValuePairs;
		this.expression = mergeSegments;
		this.lastSql = lastSql;
		this.sqlComment = sqlComment;
		this.sqlFirst = sqlFirst;
	}

	@Override
	public String getSqlSet() {
		return CollectionUtils.isEmpty(this.sqlSet) ? null : String.join(",", this.sqlSet);
	}

	@Override
	public ExtUpdateWrapper<T> set(boolean condition, String column, Object val) {
		if (condition) {
			this.sqlSet.add(String.format("%s=%s", column, this.formatSql("{0}", new Object[]{val})));
		}

		return this.typedThis;
	}
	@Override
	public ExtUpdateWrapper<T> setSql(boolean condition, String sql) {
		if (condition && StringUtils.isNotBlank(sql)) {
			this.sqlSet.add(sql);
		}

		return this.typedThis;
	}
	@Override
	protected ExtUpdateWrapper<T> instance() {
		return new ExtUpdateWrapper(this.getEntity(), (List)null, this.paramNameSeq, this.paramNameValuePairs, new MergeSegments(), SharedString.emptyString(), SharedString.emptyString(), SharedString.emptyString());
	}
	@Override
	public void clear() {
		super.clear();
		this.sqlSet.clear();
	}


	
	public static <T> ExtUpdateWrapper<T> newInstance(Class<T> entityClass)
	{
		ExtUpdateWrapper<T> queryWrapper=new ExtUpdateWrapper<T>();
		queryWrapper.setEntityClass(entityClass);
		return queryWrapper;
	}
	
	public ExtUpdateWrapper<T> eqNotEmpty(String column, String val)
	{
		if(StrUtil.isBlank(val)) {
			return this;
		}
		super.eq(column, val);
		return this;
	}
	
	public ExtUpdateWrapper<T> eqNotEmpty(String column, Object val)
	{
		if(val instanceof String) {
			return eqNotEmpty(column, (String)val);
		}
		super.eq(column, val);
		return this;
	}
	
	@Override
	public ExtUpdateWrapper<T> eq(String column, Object val)
	{
		if(val==null) {
			return this;
		}
		super.eq(column, val);
		return this;
	}
	@Override
	public ExtUpdateWrapper<T> like(String column, Object val)
	{
		String str=String.valueOf(val);
		if(StrUtil.isBlank(str)||"null".equals(str)) {
			return this;
		}
		super.like(column, val);
		return this;
	}
	@Override
	public ExtUpdateWrapper<T> likeLeft(boolean condition, String column, Object val)
	{
		String str=String.valueOf(val);
		if(StrUtil.isBlank(str)||"null".equals(str)) {
			return this;
		}
		super.likeLeft(column, val);
		return this;
	}
	@Override
	public ExtUpdateWrapper<T> likeRight(boolean condition, String column, Object val)
	{
		String str=String.valueOf(val);
		if(StrUtil.isBlank(str)||"null".equals(str)) {
			return this;
		}
		super.likeRight(column, val);
		return this;
	}
	
	@Override
	public ExtUpdateWrapper<T> ne(String column, Object val)
	{
		super.ne(val!=null, column, val);
		return this;
	}
	
	@Override
	public ExtUpdateWrapper<T> lt(String column, Object val)
	{
		super.lt(val!=null, column, val);
		return this;
	}
	
	@Override
	public ExtUpdateWrapper<T> le(String column, Object val)
	{
		super.le(val!=null, column, val);
		return this;
	}
	
	@Override
	public ExtUpdateWrapper<T> gt(String column, Object val)
	{
		super.gt(val!=null, column, val);
		return this;
	}
	
	@Override
	public ExtUpdateWrapper<T> ge(String column, Object val)
	{
		super.ge(val!=null, column, val);
		return this;
	}
	
	@Override
	public ExtUpdateWrapper<T> in(String column, Collection<?> coll)
	{
		super.in(coll!=null&&coll.size()>0, column, coll);
		//in空则查不到
		super.apply(coll==null||coll.size()==0,"1<>1");
		return this;
	}
	
	@Override
	public ExtUpdateWrapper<T> in(String column, Object... values)
	{
		super.in(values!=null&&values.length>0, column, values);
		//in空则查不到
		super.apply(values==null||values.length==0,"1<>1");
		return this;
	}
	
	@Override
	public ExtUpdateWrapper<T> notIn(String column, Collection<?> coll)
	{
		//not in 空就是查全部
		super.notIn(coll!=null&&coll.size()>0, column, coll);
		return this;
	}
	
	@Override
	public ExtUpdateWrapper<T> notIn(String column, Object... values)
	{
		//not in 空就是查全部
		super.notIn(values!=null&&values.length>0, column, values);
		return this;
	}
	
	@Override
	public ExtUpdateWrapper<T> between(String column, Object val1, Object val2)
	{
		if(StrUtil.isBlankIfStr(val1)&&StrUtil.isBlankIfStr(val2))
		{
			return this;
		}
		super.between(column, val1, val2);
		return this;
	}
	
	@Override
	public ExtUpdateWrapper<T> or()
	{
		return (ExtUpdateWrapper<T>)super.or();
	}
	
	@Override
	public ExtUpdateWrapper<T> or(boolean condition)
	{
		return (ExtUpdateWrapper<T>)super.or(condition);
	}

	/**
     * 
     * @param field 	 (两表关联用)主表的列名
     * @param childfield (两表关联用)子表的列名
     * @param extQueryWrapper 子表其他查询条件
     * @return
     */
    public <R> ExtUpdateWrapper<T> andChildCondition(String field, String childfield, ExtUpdateWrapper<R> extQueryWrapper) {
    	
    	if(extQueryWrapper==null)
    	{
    		throw new CommonSQLException("子查询没有传入正确的example");
    	}
    	
    	String sql=extQueryWrapper.getSqlSegment();
    	TableInfo tableInfoChild= TableInfoMaker.getTableInfoOrTempInfo(extQueryWrapper.getEntityClass());
    	if(tableInfoChild==null)
    	{
    		throw new MybatisPlusException(extQueryWrapper.getEntityClass()+"该实体类没有对应的表");
    	}
    	TableInfo tableInfo= TableInfoMaker.getTableInfoOrTempInfo(getEntityClass());
    	if(tableInfo==null)
    	{
    		throw new MybatisPlusException(getEntityClass()+"该实体类没有对应的表");
    	}
    	String tableInfoChildAlias=tableInfoChild.getTableName()+new Random().nextInt(10000);
    	String condition=Optional.ofNullable(sql).filter(t->StrUtil.isNotBlank(t))
    			.map(t->" and "+t.replace("paramNameValuePairs.MPGENVAL", "paramNameValuePairs.MPGENVAL"+tableInfoChildAlias))
    			.orElse("");
    	this.exists("select 1 from "+tableInfoChild.getTableName()+" "+tableInfoChildAlias+" where "
    				+tableInfo.getTableName()+"."+field+"="+tableInfoChildAlias+"."
    				+childfield+condition);
		for(String key:extQueryWrapper.getParamNameValuePairs().keySet())
		{
			this.paramNameValuePairs.put(key.replace("MPGENVAL", "MPGENVAL"+tableInfoChildAlias),
					extQueryWrapper.getParamNameValuePairs().get(key)
					);
		}
        return this;
    }

}
