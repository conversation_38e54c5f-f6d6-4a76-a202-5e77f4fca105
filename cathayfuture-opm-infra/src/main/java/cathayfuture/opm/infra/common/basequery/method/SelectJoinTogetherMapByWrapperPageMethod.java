package cathayfuture.opm.infra.common.basequery.method;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlScriptUtils;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

import java.util.Map;

public class SelectJoinTogetherMapByWrapperPageMethod extends AbstractMethod
{
	@Override
	public MappedStatement injectMappedStatement(Class<?> mapperClass,Class<?> modelClass, TableInfo tableInfo)
	{
		SqlMethod sqlMethod = SqlMethod.SELECT_MAPS_PAGE;
		String sql = String.format("<script>\n %s SELECT %s FROM %s %s %s\n</script>",
				sqlFirst(), sqlSelectColumns(tableInfo, true),
				SqlScriptUtils.convertChoose(String.format("%s != null and %s != null", Constants.WRAPPER, Constants.WRAPPER+".joinTableName")
							,SqlScriptUtils.unSafeParam(Constants.WRAPPER+".joinTableName"), tableInfo.getTableName())
				,
	            sqlWhereEntityWrapper(true, tableInfo), sqlComment());
	    SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, modelClass);
		return this.addSelectMappedStatementForOther(mapperClass,"selectJoinTogetherMapByWrapperPage", sqlSource, Map.class);
	}
}
