package cathayfuture.opm.infra.common.basequery.wrapper;

import cathayfuture.opm.infra.common.basequery.TableInfoMaker;
import cathayfuture.opm.infra.common.basequery.exception.CommonSQLException;
import cathayfuture.opm.infra.common.beantools.BeanTools;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cathayfuture.opm.infra.common.utils.StackMap;
import cathayfuture.opm.infra.common.utils.StrUtil;
import com.baomidou.mybatisplus.core.conditions.ISqlSegment;
import com.baomidou.mybatisplus.core.conditions.SharedString;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.segments.MergeSegments;
import com.baomidou.mybatisplus.core.enums.SqlKeyword;
import com.baomidou.mybatisplus.core.enums.SqlLike;
import com.baomidou.mybatisplus.core.enums.WrapperKeyword;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.metadata.TableInfo;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;


public class ExtQueryWrapper<T> extends QueryWrapper<T>{

	private static final long serialVersionUID = 1L;

	protected StackMap<String,String> tableAliasMap=new StackMap();

	private String tableAlias;

	private ExtQueryWrapper(T entity, Class<T> entityClass, AtomicInteger paramNameSeq, Map<String, Object> paramNameValuePairs, MergeSegments mergeSegments, SharedString lastSql, SharedString sqlComment, SharedString sqlFirst) {
		super(entity);
		super.setEntityClass(entityClass);
		this.paramNameSeq = paramNameSeq;
		this.paramNameValuePairs = paramNameValuePairs;
		this.expression = mergeSegments;
		this.lastSql = lastSql;
		this.sqlComment = sqlComment;
		this.sqlFirst = sqlFirst;
	}

	public ExtQueryWrapper tableAlias(String tableAlias){
		if(getEntityClass()!=null){
			TableInfo tableInfo= TableInfoMaker.getTableInfoOrTempInfo(getEntityClass());
			if(tableInfo!=null){
				this.tableAlias=tableAlias;
				tableAliasMap.put(tableInfo.getTableName(),tableAlias);
			}
		}

		return this;
	}

	public String getTableAlias() {
		return tableAlias;
	}

	protected ExtQueryWrapper()
	{
	}

	public ExtQueryWrapper cloneNewWrapper(){
		ExtQueryWrapper<T> tExtQueryWrapper = this.instance();
		tExtQueryWrapper.paramNameValuePairs=this.paramNameValuePairs;
		tExtQueryWrapper.lastSql=this.lastSql;
		tExtQueryWrapper.sqlComment=this.sqlComment;
		tExtQueryWrapper.sqlFirst=this.sqlFirst;
		tExtQueryWrapper.expression=new MergeSegments();

		if(!this.expression.getNormal().isEmpty()) {
			tExtQueryWrapper.expression.add(this.expression.getNormal().toArray(new ISqlSegment[this.expression.getNormal().size()]));
		}
		if(!this.expression.getGroupBy().isEmpty()) {
			tExtQueryWrapper.expression.add(this.expression.getGroupBy().toArray(new ISqlSegment[this.expression.getGroupBy().size()]));
		}
		if(!this.expression.getOrderBy().isEmpty()) {
			tExtQueryWrapper.expression.add(this.expression.getOrderBy().toArray(new ISqlSegment[this.expression.getOrderBy().size()]));
		}
		if(!this.expression.getHaving().isEmpty()) {
			tExtQueryWrapper.expression.add(this.expression.getHaving().toArray(new ISqlSegment[this.expression.getHaving().size()]));
		}

		return tExtQueryWrapper;
	}
	
	public static <T> ExtQueryWrapper<T> newInstance(Class<T> entityClass)
	{
		ExtQueryWrapper<T> queryWrapper=new ExtQueryWrapper<T>();
		queryWrapper.setEntityClass(entityClass);
		Field dr = BeanTools.getClassField(entityClass, "dr");
		if(dr!=null){
			queryWrapper.eq(LambdaUtils.column(dr.getName(),entityClass), 0);
		}
		return queryWrapper;
	}
	public static <T> ExtQueryWrapper<T> newInstance(Class<T> entityClass,boolean containDelete){
		if(containDelete) {
			ExtQueryWrapper<T> queryWrapper = new ExtQueryWrapper<T>();
			queryWrapper.setEntityClass(entityClass);
			return queryWrapper;
		}
		else {
			return newInstance(entityClass);
		}
	}

	@Override
	public Class<T>  getEntityClass(){
		return super.getEntityClass();
	}

	@Override
	public ExtQueryWrapper<T> setEntityClass(Class<T> entityClass) {
		super.setEntityClass(entityClass);
		return this;
	}

	public ExtQueryWrapper<T> eqNotEmpty(String column, String val)
	{
		if(StrUtil.isBlank(val)) {
			return this;
		}
		super.eq(column, val);
		return this;
	}
	
	public ExtQueryWrapper<T> eqNotEmpty(String column, Object val)
	{
		if(val instanceof String) {
			return eqNotEmpty(column, (String)val);
		}
		super.eq(column, val);
		return this;
	}

	@Override
	public ExtQueryWrapper<T> isNotNull(String column) {
		super.isNotNull(column);
		return this;
	}

	@Override
	public ExtQueryWrapper<T> isNull(String column) {
		super.isNull(column);
		return this;
	}

	@Override
	public ExtQueryWrapper<T> isNotNull(boolean condition, String column) {
		super.isNotNull(condition, column);
		return this;
	}

	@Override
	public ExtQueryWrapper<T> isNull(boolean condition, String column) {
		super.isNull(condition, column);
		return this;
	}

	@Override
	public ExtQueryWrapper<T> eq(String column, Object val)
	{
		if(val==null) {
			return this;
		}
		super.eq(column, val);
		return this;
	}
	@Override
	public ExtQueryWrapper<T> like(String column, Object val)
	{
		String str=String.valueOf(val);
		if(StrUtil.isBlank(str)||"null".equals(str)) {
			return this;
		}
		this.likeValue(true, SqlKeyword.LIKE, column, val, SqlLike.DEFAULT);
		return this;
	}
	@Override
	public ExtQueryWrapper<T> likeLeft(boolean condition, String column, Object val)
	{
		String str=String.valueOf(val);
		if(StrUtil.isBlank(str)||"null".equals(str)) {
			return this;
		}
		super.likeValue(condition, SqlKeyword.LIKE, column, val, SqlLike.LEFT);
		return this;
	}
	@Override
	public ExtQueryWrapper<T> likeRight(boolean condition, String column,Object val)
	{
		String str=String.valueOf(val);
		if(StrUtil.isBlank(str)||"null".equals(str)) {
			return this;
		}
		super.likeValue(condition, SqlKeyword.LIKE, column, val, SqlLike.RIGHT);
		return this;
	}
	
	@Override
	public ExtQueryWrapper<T> ne(String column, Object val)
	{
		super.ne(val!=null, column, val);
		return this;
	}
	
	@Override
	public ExtQueryWrapper<T> lt(String column, Object val) 
	{
		super.lt(val!=null, column, val);
		return this;
	}
	
	@Override
	public ExtQueryWrapper<T> le(String column, Object val) 
	{
		super.le(val!=null, column, val);
		return this;
	}
	
	@Override
	public ExtQueryWrapper<T> gt(String column, Object val) 
	{
		super.gt(val!=null, column, val);
		return this;
	}
	
	@Override
	public ExtQueryWrapper<T> ge(String column, Object val) 
	{
		super.ge(val!=null, column, val);
		return this;
	}

	public ExtQueryWrapper<T> geDate(String column, String start)
	{
		return ge("left("+column+",10)",start);
	}

	public ExtQueryWrapper<T> leDate(String column, String end)
	{
		return le("left("+column+",10)",end);
	}

	public ExtQueryWrapper<T> geMonth(String column, String start)
	{
		return ge("left("+column+",7)",start);
	}

	public ExtQueryWrapper<T> leMonth(String column, String end)
	{
		return le("left("+column+",7)",end);
	}
	
	@Override
	public ExtQueryWrapper<T> in(String column,Collection<?> coll)
	{
		super.in(coll!=null&&coll.size()>0, column, coll);
		//in空则查不到
		super.apply(coll==null||coll.size()==0,"1<>1");
		return this;
	}
	
	@Override
	public ExtQueryWrapper<T> in(String column,Object... values)
	{
		super.in(values!=null&&values.length>0, column, values);
		//in空则查不到
		super.apply(values==null||values.length==0,"1<>1");
		return this;
	}

	@Override
	public ExtQueryWrapper<T> in(boolean condition,String column,Object... values)
	{
		if(condition){
			return in(column,values);
		}
		return this;
	}

	@Override
	public ExtQueryWrapper<T> in(boolean condition,String column,Collection<?> coll)
	{
		if(condition){
			return in(column,coll);
		}
		return this;
	}

	@Override
	public ExtQueryWrapper<T> notIn(String column,Collection<?> coll)
	{
		//not in 空就是查全部
		super.notIn(coll!=null&&coll.size()>0, column, coll);
		return this;
	}

	
	@Override
	public ExtQueryWrapper<T> notIn(String column,Object... values)
	{
		//not in 空就是查全部
		super.notIn(values!=null&&values.length>0, column, values);
		return this;
	}
	
	@Override
	public ExtQueryWrapper<T> between(String column, Object val1, Object val2)
	{
		if(StrUtil.isBlankIfStr(val1)&&StrUtil.isBlankIfStr(val2))
		{
			return this;
		}
		super.between(column, val1, val2);
		return this;
	}
	
	@Override
	public ExtQueryWrapper<T> or()
	{
		return (ExtQueryWrapper<T>)super.or();
	}
	
	@Override
	public ExtQueryWrapper<T> or(boolean condition)
	{
		return (ExtQueryWrapper<T>)super.or(condition);
	}
	@Override
	public ExtQueryWrapper<T> or(boolean condition, Consumer<QueryWrapper<T>> consumer)
	{
		return (ExtQueryWrapper<T>)super.or(condition, consumer);
	}
	@Override
	public ExtQueryWrapper<T> or(Consumer<QueryWrapper<T>> consumer)
	{
		return (ExtQueryWrapper<T>)super.or(consumer);
	}
	@Override
	protected ExtQueryWrapper<T> and(boolean condition)
	{
		super.and(condition);
		return this;
	}

	public ExtQueryWrapper<T> andExt(boolean condition, Consumer<ExtQueryWrapper<T>> consumer)
	{
		this.and(condition).addExtNestedCondition(condition, consumer);
		return this;
	}

	public ExtQueryWrapper<T> andExt(Consumer<ExtQueryWrapper<T>> consumer)
	{
		return andExt(true,consumer);
	}

	public ExtQueryWrapper addExtNestedCondition(boolean condition, Consumer<ExtQueryWrapper<T>> consumer) {
		if (condition) {
			ExtQueryWrapper instance = this.instance();
			consumer.accept(instance);
			return this.doIt(true, WrapperKeyword.APPLY, instance);
		} else {
			return this;
		}
	}
	@Override
	protected ExtQueryWrapper doIt(boolean condition, ISqlSegment... sqlSegments) {
		if (condition) {
			this.expression.add(sqlSegments);
		}

		return this;
	}

	@Override
	protected ExtQueryWrapper<T> instance() {
		return new ExtQueryWrapper(this.getEntity(), this.getEntityClass(), this.paramNameSeq, this.paramNameValuePairs, new MergeSegments(), SharedString.emptyString(), SharedString.emptyString(), SharedString.emptyString());
	}

	@Override
	public ExtQueryWrapper<T> and(Consumer<QueryWrapper<T>> consumer)
	{
		return (ExtQueryWrapper<T>)super.and(consumer);
	}

	public boolean hasCondition(){
		return expression!=null&&expression.getNormal()!=null&&!expression.getNormal().isEmpty();
	}

	public <R> ExtQueryWrapper<T> andChildCondition(boolean condition,String field,String childfield,ExtQueryWrapper<R> extQueryWrapper) {
		if(condition){
			andChildCondition(field,childfield,extQueryWrapper);
		}
		return this;
	}
	/**
     * 
     * @param field 	 (两表关联用)主表的列名
     * @param childfield (两表关联用)子表的列名
     * @param extQueryWrapper 子表其他查询条件
     * @return
     */
    public <R> ExtQueryWrapper<T> andChildCondition(String field,String childfield,ExtQueryWrapper<R> extQueryWrapper) {
    	
    	if(extQueryWrapper==null)
    	{
    		throw new CommonSQLException("子查询没有传入正确的example");
    	}
    	
    	String sql=extQueryWrapper.getSqlSegment();
    	TableInfo tableInfoChild= TableInfoMaker.getTableInfoOrTempInfo(extQueryWrapper.getEntityClass());
    	if(tableInfoChild==null)
    	{
    		throw new MybatisPlusException(extQueryWrapper.getEntityClass()+"该实体类没有对应的表");
    	}
    	TableInfo tableInfo= TableInfoMaker.getTableInfoOrTempInfo(getEntityClass());
    	if(tableInfo==null)
    	{
    		throw new MybatisPlusException(getEntityClass()+"该实体类没有对应的表");
    	}
		String childAlias=tableInfoChild.getTableName()+new Random().nextInt(10000);
    	if(StrUtil.isNotBlank(extQueryWrapper.getTableAlias())){
			childAlias=extQueryWrapper.getTableAlias();
		}
    	final String tableInfoChildAlias=childAlias;
		tableAliasMap.put(tableInfoChild.getTableName(),tableInfoChildAlias);

    	String condition=Optional.ofNullable(sql).filter(t->StrUtil.isNotBlank(t))
    			.map(t->" and "+t.replace("paramNameValuePairs.MPGENVAL", "paramNameValuePairs.MPGENVAL"+tableInfoChildAlias))
    			.orElse("");

		String parentTableAlias = tableAliasMap.pop(tableInfo.getTableName());
    	this.exists("select 1 from "+tableInfoChild.getTableName()+" "+tableInfoChildAlias+" where "
    				+(parentTableAlias==null?tableInfo.getTableName():parentTableAlias)+"."+field+"="+tableInfoChildAlias+"."
    				+childfield+condition);
		for(String key:extQueryWrapper.getParamNameValuePairs().keySet())
		{
			this.paramNameValuePairs.put(key.replace("MPGENVAL", "MPGENVAL"+tableInfoChildAlias),
					extQueryWrapper.getParamNameValuePairs().get(key)
					);
		}
        return this;
    }

	public ExtQueryWrapper<T> limit(int size)
	{
		super.last(" limit "+size);
		return this;
	}

	public ExtQueryWrapper<T> limit(Integer start,int size)
	{
		super.last(" limit "+(start==null?"":(start+","))+size);
		return this;
	}




	@Override
	public ExtQueryWrapper<T> orderByAsc(String column) {
		this.orderByAsc(true, column);
		return this;
	}

	@Override
	public ExtQueryWrapper<T> orderByAsc(String... columns) {
		 this.orderByAsc(true, columns);
		 return this;
	}
	@Override
	public ExtQueryWrapper<T> orderByAsc(boolean condition, String... columns) {
		super.orderBy(condition, true, columns);
		return this;
	}
	@Override
	public ExtQueryWrapper<T> orderByDesc(String column) {
		this.orderByDesc(true, column);
		return this;
	}
	@Override
	public ExtQueryWrapper<T> orderByDesc(String... columns) {
		this.orderByDesc(true, columns);
		return this;
	}
	@Override
	public ExtQueryWrapper<T> orderByDesc(boolean condition, String... columns) {
		super.orderBy(condition, false, columns);
		return this;
	}

}
