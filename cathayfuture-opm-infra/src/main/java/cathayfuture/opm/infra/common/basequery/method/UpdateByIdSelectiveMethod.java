package cathayfuture.opm.infra.common.basequery.method;

import cathayfuture.opm.infra.common.basequery.method.utils.SelectiveFieldsUtils;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlScriptUtils;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

public class UpdateByIdSelectiveMethod extends AbstractMethod 
{
	@Override
	public MappedStatement injectMappedStatement(Class<?> mapperClass,Class<?> modelClass, TableInfo tableInfo)
	{
		SqlMethod sqlMethod = SqlMethod.UPDATE_BY_ID;
		final String additional = optlockVersion(tableInfo) + tableInfo.getLogicDeleteSql(true, true);
		String sql = String.format(sqlMethod.getSql(), tableInfo.getTableName(),
				sqlSetSelective(tableInfo.isLogicDelete(), false, tableInfo, false, ENTITY, ENTITY_DOT),
	            tableInfo.getKeyColumn(), ENTITY_DOT + tableInfo.getKeyProperty(), additional);
		
	    SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, modelClass);
	    return addUpdateMappedStatement(mapperClass, modelClass, "updateByIdSelective", sqlSource);
	}
	
	protected String sqlSetSelective(boolean logic, boolean ew, TableInfo table,
			boolean judgeAliasNull, String alias, String prefix) 
	{
		String sqlScript = SelectiveFieldsUtils.getAllSqlSet(table,logic, prefix);
        if (judgeAliasNull) {
            sqlScript = SqlScriptUtils.convertIf(sqlScript, String.format("%s != null", alias), true);
        }
        if (ew) {
            sqlScript += NEWLINE;
            sqlScript += SqlScriptUtils.convertIf(SqlScriptUtils.unSafeParam(U_WRAPPER_SQL_SET),
                String.format("%s != null and %s != null", WRAPPER, U_WRAPPER_SQL_SET), false);
        }
        sqlScript = SqlScriptUtils.convertSet(sqlScript);
        return sqlScript;
	}
	
	
}
