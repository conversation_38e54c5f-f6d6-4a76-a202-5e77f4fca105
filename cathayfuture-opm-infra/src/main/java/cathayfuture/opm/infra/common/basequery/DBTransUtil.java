package cathayfuture.opm.infra.common.basequery;

import cathayfuture.opm.infra.common.beantools.BeanTools;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cathayfuture.opm.infra.common.lambda.SerializedFunction;
import cathayfuture.opm.infra.common.utils.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;


import java.lang.reflect.Field;

public class DBTransUtil
{
	/**
	 * 用lambda模式获取数据库字段
	 * @param field
	 * @param <T>
	 * @return
	 */
	public static <T>  String lambdaToColumn(SerializedFunction<T,?> field){
		return propertyToColumn(LambdaUtils.resolveFieldName(field),LambdaUtils.resolve(field).getImplClass());
	}

	public static <T>  String column(SerializedFunction<T,?> field){
		return lambdaToColumn(field);
	}

	public static String propertyToColumn(String property, Class clz) 
	{
		if (clz != null&&property!=null) {
			try {
				property=property.trim();
				Field field = BeanTools.getClassFieldWithOutCglib(clz, property);
				if(field==null)
				{
					throw new RuntimeException("在"+clz.getName()+"中没有"+property);
				}
				return propertyToColumn(field);
			}catch (Exception e) {
				e.printStackTrace();
			}
		}
		return "" +  StrUtil.toUnderlineCase(property) + "";
	}
	
	
	//增删改用,不带as转换
	public static String propertyToColumn(Field field)
	{
		TableField tableField=field.getAnnotation(TableField.class);
		if(tableField!=null){
			return tableField.value();
		}
		else{
			return StrUtil.toUnderlineCase(field.getName());
		}
	}


}
