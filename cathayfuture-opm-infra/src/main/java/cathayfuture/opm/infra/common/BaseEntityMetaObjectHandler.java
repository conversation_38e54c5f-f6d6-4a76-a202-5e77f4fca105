package cathayfuture.opm.infra.common;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 9/9/22
 */
@Slf4j
@Component
public class BaseEntityMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("start insert fill ....");
        LocalServiceContext context = LocalServiceContext.getContext();
        log.info("tenantId is [{}]", context.getRequestTenantIdString());
        this.strictInsertFill(metaObject, "tenantId", String.class, context.getRequestTenantIdString());
        log.info("instanceId is [{}]", context.getRequestInstanceId());
        this.strictInsertFill(metaObject, "instanceId", Long.class, context.getRequestInstanceId());
        log.info("createPerson is [{}]", ExtServiceContext.getUserName());
        this.strictInsertFill(metaObject, "createPerson", String.class, context.getRequestUserCode());
        this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
        log.info("updatePerson is [{}]", ExtServiceContext.getUserName());
        this.strictInsertFill(metaObject, "updatePerson", String.class, context.getRequestUserCode());
        this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("updatePerson is [{}]", ExtServiceContext.getUserName());
        LocalServiceContext context = LocalServiceContext.getContext();
        this.setFieldValByName("updatePerson", context.getRequestUserCode(), metaObject);
        this.setFieldValByName("updateTime", new Date(), metaObject);
    }
}
