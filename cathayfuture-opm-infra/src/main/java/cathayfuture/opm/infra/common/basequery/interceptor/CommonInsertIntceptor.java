package cathayfuture.opm.infra.common.basequery.interceptor;



import cathayfuture.opm.infra.common.basequery.commonsql.CommonSqlParamHandler;
import cathayfuture.opm.infra.common.basequery.interceptor.inner.InnerInterceptor;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;

import java.sql.SQLException;


public class CommonInsertIntceptor implements InnerInterceptor {
	
	@Override
	public void beforeUpdate(Executor executor, MappedStatement ms, Object parameter) throws SQLException
	{
		new CommonSqlParamHandler(ms, parameter, ms.getBoundSql(parameter)).processParameter(parameter);
		InnerInterceptor.super.beforeUpdate(executor, ms, parameter);
	}
	
}
