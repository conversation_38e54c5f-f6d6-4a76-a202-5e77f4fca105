package cathayfuture.opm.infra.common.exception;

/**
 * 业务异常类，替代原来的com.dtyunxi.exceptions.BizException
 * 
 * <AUTHOR>
 * @since 2025-08-11
 */
public class BizException extends RuntimeException {
    
    private String code;
    
    public BizException(String message) {
        super(message);
    }
    
    public BizException(String code, String message) {
        super(message);
        this.code = code;
    }
    
    public BizException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public BizException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
}
