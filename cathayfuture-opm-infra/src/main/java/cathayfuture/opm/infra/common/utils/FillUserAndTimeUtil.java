package cathayfuture.opm.infra.common.utils;



import cathayfuture.opm.infra.common.beantools.BeanTools;

import java.util.Date;

public class FillUserAndTimeUtil {

    public static <T> void fillCreate(T entity){
        BeanTools.setProperty(entity,"dr", 0);
        BeanTools.setProperty(entity,"createTime",new Date());
        try {
            BeanTools.setProperty(entity,"createPerson","");
        }
        catch (Exception e){

        }
    }
    public static <T>  void fillUpdate(T entity){
        BeanTools.setProperty(entity,"updateTime",new Date());
        try {
            BeanTools.setProperty(entity, "updateBy","");
        }
        catch (Exception e){

        }
    }

    public static <T>  void fillCreateAndUpdate(T entity){
        fillCreate(entity);
        fillUpdate(entity);
    }
}
