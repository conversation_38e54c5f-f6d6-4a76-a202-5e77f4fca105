package cathayfuture.opm.infra.common.basequery;

import cathayfuture.opm.infra.common.basequery.method.*;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.extension.injector.methods.InsertBatchSomeColumn;

import java.util.List;


public class CustomizedSqlInjector extends DefaultSqlInjector
{
	@Override
	public List<AbstractMethod> getMethodList(Class<?> mapperClass) 
	{
		List<AbstractMethod> methodList = super.getMethodList(mapperClass);
	    methodList.add(new SelectTogetherMapByWrapperMethod());
		methodList.add(new SelectTogetherMapByWrapperPageMethod());
	    methodList.add(new SelectTogetherListByWrapperMethod());
	    methodList.add(new UpdateByIdSelectiveMethod());
	    methodList.add(new UpdateSelectiveMethod());
	    methodList.add(new BatchInsertMethod());
		methodList.add(new UpdateAllColumnsMethod());
		methodList.add(new UpdateByIdAllColumnsMethod());
		methodList.add(new SelectJoinTogetherMapByWrapperPageMethod());
		methodList.add(new InsertBatchSomeColumn().setPredicate(t -> !t.isLogicDelete() || !"reversion".equals(t.getProperty()))); // 添加InsertBatchSomeColumn方法

		return methodList;
	}
}
