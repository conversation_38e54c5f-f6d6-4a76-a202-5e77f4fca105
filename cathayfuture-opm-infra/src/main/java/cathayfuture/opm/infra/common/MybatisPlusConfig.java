package cathayfuture.opm.infra.common;

import cathayfuture.opm.infra.common.basequery.CommonSqlMapper;
import cathayfuture.opm.infra.common.basequery.CustomizedSqlInjector;
import cathayfuture.opm.infra.common.basequery.interceptor.CommonInsertIntceptor;
import cathayfuture.opm.infra.common.basequery.method.*;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.extension.injector.methods.InsertBatchSomeColumn;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import icu.mhb.mybatisplus.plugln.injector.JoinDefaultSqlInjector;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/31/22
 */
@Configuration
public class MybatisPlusConfig extends JoinDefaultSqlInjector {

/*    @Bean
    public MySqlInjector mySqlInjector () {
        return new MySqlInjector();
    }*/

    @Bean
    public MybatisPlusInterceptor paginationInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();

        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        mybatisPlusInterceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        return mybatisPlusInterceptor;
    }


    /**
     * 通用mapper
     *
     * @return
     */
    @Bean
    public CommonSqlMapper commonSqlMapper() {
        return new CommonSqlMapper();
    }

    /**
     * 注入mybatis-plus扩展方法
     *
     * @return
     */
//    @Bean
//    public CustomizedSqlInjector customizedSqlInjector() {
//        return new CustomizedSqlInjector();
//    }

    /**
     * 注入mybatis-plus自动生成主键拦截器
     *
     * @return
     */
    @Bean
    public CommonInsertIntceptor commonInsertIntceptor() {
        return new CommonInsertIntceptor();
    }

    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass) {
        List<AbstractMethod> methodList = super.getMethodList(mapperClass);

        methodList.add(new SelectTogetherMapByWrapperMethod());
        methodList.add(new SelectTogetherMapByWrapperPageMethod());
        methodList.add(new SelectTogetherListByWrapperMethod());
        methodList.add(new UpdateByIdSelectiveMethod());
        methodList.add(new UpdateSelectiveMethod());
        methodList.add(new BatchInsertMethod());
        methodList.add(new UpdateAllColumnsMethod());
        methodList.add(new UpdateByIdAllColumnsMethod());
        methodList.add(new SelectJoinTogetherMapByWrapperPageMethod());
        methodList.add(new InsertBatchSomeColumn().setPredicate(t -> !t.isLogicDelete() || !"reversion".equals(t.getProperty()))); // 添加InsertBatchSomeColumn方法

        return methodList;
    }

}
