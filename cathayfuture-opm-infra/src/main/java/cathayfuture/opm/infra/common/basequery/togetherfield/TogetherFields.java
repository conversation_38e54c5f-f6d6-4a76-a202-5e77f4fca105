package cathayfuture.opm.infra.common.basequery.togetherfield;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class TogetherFields
{
	public static Map<Integer,String> togetherFunMap=new HashMap<Integer, String>();
	
	static
	{
		togetherFunMap.put(TogetherFun.MAX.ordinal(),"max");
		togetherFunMap.put(TogetherFun.MIN.ordinal(),"min");
		togetherFunMap.put(TogetherFun.SUM.ordinal(),"sum");
		togetherFunMap.put(TogetherFun.COUNT.ordinal(),"count");
		togetherFunMap.put(TogetherFun.GROUP_CONCAT.ordinal(),"group_concat");
	
	}
	
	List<TogetherField> fields=new ArrayList<TogetherField>();

	List<Class> groupByTable=new ArrayList<>();
	List<String> groupByField=new ArrayList<>();
	List<String> groupByAlias=new ArrayList<>();


	public TogetherFields groupBy(Class table,String groupByField,String groupByAlias){
		this.groupByTable.add(table);
		this.groupByField.add(groupByField);
		this.groupByAlias.add(groupByAlias);
		return this;
	}

	public TogetherFields groupBy(String groupByField,String groupByAlias){
		return groupBy(null,groupByField,groupByAlias);
	}

	public TogetherFields groupBy(String groupByField){
		return groupBy(groupByField,groupByField);
	}

	public TogetherFields groupBy(Class table,String groupByField){
		return groupBy(table,groupByField,groupByField);
	}


	public List<String>  getGroupByField() {
		return groupByField;
	}

	public List<String>  getGroupByAlias() {
		return groupByAlias;
	}

	public List<Class> getGroupByTable() {
		return groupByTable;
	}

	public List<TogetherField> getFields() {
		return fields;
	}

	public void setFields(List<TogetherField> fields) {
		this.fields = fields;
	}

	public TogetherFields()
	{
		
	}
	
	public static TogetherFields newInstance()
	{
		return new TogetherFields();
	}
	
	public TogetherFields max(String fieldName)
	{
		fields.add(new TogetherField(fieldName, TogetherFun.MAX));
		return this;
	}
	public TogetherFields min(String fieldName)
	{
		fields.add(new TogetherField(fieldName, TogetherFun.MIN));
		return this;
	}
	public TogetherFields count(String fieldName,String asField)
	{
		fields.add(new TogetherField(fieldName, TogetherFun.COUNT,asField));
		return this;
	}
	public TogetherFields count(String fieldName)
	{
		fields.add(new TogetherField(fieldName, TogetherFun.COUNT));
		return this;
	}
	public TogetherFields sum(String fieldName)
	{
		fields.add(new TogetherField(fieldName,TogetherFun.SUM));
		return this;
	}
	public TogetherFields sum(String fieldName,String cast)
	{
		TogetherField field = new TogetherField(fieldName,TogetherFun.SUM);
		field.setCast(cast);
		fields.add(field);
		return this;
	}
	public TogetherFields nothing(String fieldName)
	{
		fields.add(new TogetherField(fieldName, null));
		return this;
	}
	public TogetherFields nothing(String fieldName,String asField)
	{
		fields.add(new TogetherField(fieldName, null,asField));
		return this;
	}
	public TogetherFields groupconcat(String fieldName) {
		fields.add(new TogetherField(fieldName, TogetherFun.GROUP_CONCAT));
		return this;
	}
	public TogetherFields groupconcat(String fieldName,String asField) {
		fields.add(new TogetherField(fieldName, TogetherFun.GROUP_CONCAT,asField));
		return this;
	}
	public TogetherFields togetherif(TogetherFun togetherFun,String condtionField,String condtionOperator,String condtionValue,String fieldName,Object elseValue)
	{
		fields.add(new TogetherField(condtionField,condtionOperator,condtionValue,fieldName, togetherFun,elseValue));
		return this;
	}
	
	public TogetherFields togetherif(TogetherFun togetherFun,String condtionField,String condtionOperator,String condtionValue,String fieldName,Object elseValue,String asField)
	{
		TogetherField field = new TogetherField(condtionField,condtionOperator,condtionValue,fieldName, togetherFun,elseValue);
		field.setAsField(asField);
		fields.add(field);
		return this;
	}

	public static class TogetherField
	{
		private String fieldName;
		private TogetherFun togetherFun;
		private String asField;
		
		private String cast;
		
		private boolean condition;
		private String condtionField;
		private String condtionOperator;
		private String condtionValue;
		private Object elseValue;
		
		
		public TogetherField(String fieldName, TogetherFun togetherFun,String asField) {
			super();
			initField(fieldName,togetherFun,asField);
		}
		public TogetherField(String fieldName, TogetherFun togetherFun) {
			super();
			initField(fieldName,togetherFun,null);
		}
		public TogetherField(String condtionField, String condtionOperator,String condtionValue,String fieldName, TogetherFun togetherFun,Object elseValue,String asField) 
		{
			super();
			this.condition=true;
			initField(fieldName,togetherFun,asField);
			this.condtionField = condtionField;
			this.condtionOperator = condtionOperator;
			this.condtionValue = condtionValue;
			this.elseValue=elseValue;
		}
		public TogetherField(String condtionField, String condtionOperator,String condtionValue,String fieldName, TogetherFun togetherFun,Object elseValue) {
			super();
			this.condition=true;
			initField(fieldName,togetherFun,null);
			this.condtionField = condtionField;
			this.condtionOperator = condtionOperator;
			this.condtionValue = condtionValue;
			this.elseValue=elseValue;
		}
		
		
		private void initField(String fieldName, TogetherFun togetherFun,String asField)
		{
			this.togetherFun=togetherFun;
			this.fieldName=fieldName;
			this.asField=asField;
		}

		

		public boolean isCondition() {
			return condition;
		}

		public void setCondition(boolean condition) {
			this.condition = condition;
		}

		public String getCondtionField() {
			return condtionField;
		}

		public void setCondtionField(String condtionField) {
			this.condtionField = condtionField;
		}

		public String getCondtionOperator() {
			return condtionOperator;
		}

		public void setCondtionOperator(String condtionOperator) {
			this.condtionOperator = condtionOperator;
		}

		public String getCondtionValue() {
			return condtionValue;
		}

		public void setCondtionValue(String condtionValue) {
			this.condtionValue = condtionValue;
		}

		public String getFieldName() {
			return fieldName;
		}
		public void setFieldName(String fieldName) {
			this.fieldName = fieldName;
		}
		
		public TogetherFun getTogetherFun() {
			return togetherFun;
		}
		
		public void setTogetherFun(TogetherFun togetherFun) {
			this.togetherFun = togetherFun;
		}
		
		public Object getElseValue() {
			return elseValue;
		}

		public void setElseValue(Object elseValue) {
			this.elseValue = elseValue;
		}
		public String getAsField() {
			return asField;
		}
		public void setAsField(String asField) {
			this.asField = asField;
		}
		
		public String getAsFieldOrField()
		{
			return asField!=null&&!"".equals(asField)?asField:fieldName;
		}
		
		public String getCast() {
			return cast;
		}
		public void setCast(String cast) {
			this.cast = cast;
		}
		public String getFunction()
		{
			if(getTogetherFun()!=null)
			{
				return togetherFunMap.get(getTogetherFun().ordinal());
			}
			return "";
		}
	}
}

