package cathayfuture.opm.infra.common.basequery.wrapper;

import cathayfuture.opm.infra.common.basequery.TableInfoMaker;
import cathayfuture.opm.infra.common.basequery.enums.JoinEnum;
import cathayfuture.opm.infra.common.basequery.togetherfield.TogetherFields;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cathayfuture.opm.infra.common.lambda.SerializedFunction;
import com.baomidou.mybatisplus.core.conditions.SharedString;
import com.baomidou.mybatisplus.core.metadata.TableInfo;

import java.util.HashMap;
import java.util.Map;

public class JoinTogetherWrapper<T> extends TogetherWrapper<T>
{

	private final SharedString joinTableName;

	protected Map<Class,String> tableAliasMap=new HashMap();

	private JoinTogetherWrapper(Class<T> clazz,String alias)
	{
		setEntityClass(clazz);
		joinTableName=new SharedString();
		TableInfo tableInfo= TableInfoMaker.getTableInfoOrTempInfo(getEntityClass());
		if(tableInfo!=null){
			joinTableName.setStringValue(tableInfo.getTableName()+" "+alias);
			tableAliasMap.put(getEntityClass(),alias);
			super.tableAliasMap.put(tableInfo.getTableName(),alias);
		}
	}
	
	public static <T> JoinTogetherWrapper<T> newInstance(Class<T> clazz,String alias)
	{
		JoinTogetherWrapper<T> wrapper=new JoinTogetherWrapper<T>(clazz,alias);

		return wrapper;
	}

	public JoinTogetherWrapper<T> addTogetherFields(TogetherFields togetherFields){
		this.makeTogetherWrapper(togetherFields);
		return this;
	}

	public <C> JoinTogetherWrapper<T> addJoinMainClass(Class<C> clazz, String childAlias, JoinEnum joinType, SerializedFunction<T,Object> mainKey
			, SerializedFunction<C,Object> childKey){
		TableInfo tableInfo= TableInfoMaker.getTableInfoOrTempInfo(clazz);
		if(tableInfo!=null){
			tableAliasMap.put(clazz,childAlias);
			super.tableAliasMap.put(tableInfo.getTableName(),childAlias);
			StringBuilder sql=new StringBuilder();
			sql.append(joinTableName.getStringValue()).append(" ")
					.append(joinType.getSql()).append(" ")
					.append(tableInfo.getTableName()).append(" ")
					.append(childAlias).append(" ")
					.append("on").append(" ")
					.append(tableAliasMap.get(getEntityClass())).append(".")
					.append(LambdaUtils.column(mainKey))
					.append("=")
					.append(childAlias).append(".")
					.append(LambdaUtils.column(childKey)).append(" ");
			joinTableName.setStringValue(sql.toString());
		}

		return this;
	}
	public String getJoinTableName() {
		return this.joinTableName.getStringValue();
	}


	@Override
	public String getTableAliasDot(Class clazz) {
		if(clazz==null){
			return "";
		}
		return tableAliasMap.get(clazz)+".";
	}
}
