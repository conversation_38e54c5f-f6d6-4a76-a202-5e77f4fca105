2025-08-05 14:19:56,210 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-08-05 14:19:56,212 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-05 14:19:56,491 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-05 14:19:59,519 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-08-05 14:19:59,522 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-08-05 14:48:49,914 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-08-05 14:48:49,917 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-05 14:48:50,167 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-05 14:48:53,080 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-08-05 14:48:53,083 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
