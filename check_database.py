#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查数据库连接和用户数据
"""

import requests
import json
import bcrypt

def test_bcrypt_passwords():
    """测试BCrypt密码验证"""
    print("=== BCrypt密码验证测试 ===")
    
    # 测试已知的BCrypt哈希
    test_cases = [
        {
            "password": "admin123",
            "hash": "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKTcn5Uy6Q7J8yNWNQynVf2.Ek/2",
            "user": "admin"
        },
        {
            "password": "manager123", 
            "hash": "$2a$10$8.UrQjQKvNa8/Oy.3.5.5eKQjKtKtKtKtKtKtKtKtKtKtKtKtKtKt",
            "user": "manager"
        }
    ]
    
    for case in test_cases:
        try:
            # 验证密码
            is_valid = bcrypt.checkpw(case["password"].encode('utf-8'), case["hash"].encode('utf-8'))
            status = "✅ 正确" if is_valid else "❌ 错误"
            print(f"{case['user']} 用户密码验证: {status}")
            print(f"  密码: {case['password']}")
            print(f"  哈希: {case['hash']}")
        except Exception as e:
            print(f"❌ {case['user']} 用户密码验证异常: {e}")
        print()

def test_api_endpoints():
    """测试API端点"""
    print("=== API端点详细测试 ===")
    
    base_url = "http://localhost:8080"
    
    # 测试登录端点的详细信息
    login_url = f"{base_url}/bms/auth/login"
    
    print(f"登录端点: {login_url}")
    
    # 测试不同的请求方式
    test_data = {"username": "admin", "password": "admin123"}
    
    try:
        # 发送POST请求
        response = requests.post(
            login_url,
            json=test_data,
            headers={
                "Content-Type": "application/json",
                "Accept": "application/json"
            },
            timeout=10
        )
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        # 检查响应内容
        if response.status_code == 404:
            print("❌ 登录端点不存在，可能路径错误")
        elif response.status_code == 405:
            print("❌ 方法不允许，可能需要不同的HTTP方法")
        elif response.status_code == 401:
            print("⚠️ 认证失败，可能是用户名密码错误或认证逻辑问题")
        elif response.status_code == 500:
            print("❌ 服务器内部错误，可能是配置或代码问题")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_alternative_endpoints():
    """测试其他可能的端点"""
    print("=== 测试其他可能的端点 ===")
    
    base_url = "http://localhost:8080"
    
    # 可能的登录端点
    endpoints = [
        "/auth/login",
        "/api/auth/login", 
        "/login",
        "/bms/login",
        "/system/login"
    ]
    
    test_data = {"username": "admin", "password": "admin123"}
    
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        try:
            response = requests.post(
                url,
                json=test_data,
                headers={"Content-Type": "application/json"},
                timeout=5
            )
            print(f"{endpoint}: HTTP {response.status_code}")
            if response.status_code != 404:
                print(f"  响应: {response.text[:100]}...")
        except:
            print(f"{endpoint}: 连接失败")

def check_swagger_docs():
    """检查Swagger文档"""
    print("=== 检查API文档 ===")
    
    base_url = "http://localhost:8080"
    
    # 可能的文档端点
    doc_endpoints = [
        "/swagger-ui.html",
        "/swagger-ui/",
        "/doc.html",
        "/api-docs",
        "/v2/api-docs"
    ]
    
    for endpoint in doc_endpoints:
        url = f"{base_url}{endpoint}"
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ 找到API文档: {url}")
                return url
            else:
                print(f"{endpoint}: HTTP {response.status_code}")
        except:
            print(f"{endpoint}: 无法访问")
    
    print("❌ 未找到API文档")
    return None

def main():
    """主函数"""
    print("数据库用户认证问题诊断")
    print("=" * 50)
    
    # 测试BCrypt密码
    try:
        test_bcrypt_passwords()
    except ImportError:
        print("⚠️ bcrypt模块未安装，跳过密码验证测试")
        print("可以运行: pip install bcrypt")
    except Exception as e:
        print(f"❌ BCrypt测试异常: {e}")
    
    print()
    
    # 测试API端点
    test_api_endpoints()
    print()
    
    # 测试其他端点
    test_alternative_endpoints()
    print()
    
    # 检查文档
    swagger_url = check_swagger_docs()
    
    print()
    print("=" * 50)
    print("诊断建议:")
    print("1. 检查数据库连接配置")
    print("2. 确认用户表中有数据")
    print("3. 验证密码加密方式")
    print("4. 检查Spring Security配置")
    print("5. 查看应用启动日志")
    
    if swagger_url:
        print(f"6. 查看API文档: {swagger_url}")

if __name__ == "__main__":
    main()
