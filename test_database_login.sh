#!/bin/bash

# 数据库用户认证测试脚本
# 测试使用数据库中的用户名和密码进行登录

echo "=== 数据库用户认证测试 ==="

# 设置服务器地址
SERVER_URL="http://localhost:8080"

echo "1. 测试admin用户登录（用户名: admin, 密码: admin123）"
echo "发送登录请求..."

# 测试admin用户登录
ADMIN_RESPONSE=$(curl -s -X POST \
  "${SERVER_URL}/bms/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

echo "Admin登录响应: $ADMIN_RESPONSE"

# 提取token
ADMIN_TOKEN=$(echo $ADMIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ ! -z "$ADMIN_TOKEN" ]; then
    echo "✅ Admin登录成功，Token: ${ADMIN_TOKEN:0:20}..."
    
    echo ""
    echo "2. 使用admin token访问受保护的接口"
    
    # 测试访问用户列表接口
    USER_LIST_RESPONSE=$(curl -s -X GET \
      "${SERVER_URL}/bms/system/user/enabled" \
      -H "Authorization: Bearer $ADMIN_TOKEN" \
      -H "Content-Type: application/json")
    
    echo "用户列表响应: $USER_LIST_RESPONSE"
    
else
    echo "❌ Admin登录失败"
fi

echo ""
echo "3. 测试manager用户登录（用户名: manager, 密码: manager123）"

# 测试manager用户登录
MANAGER_RESPONSE=$(curl -s -X POST \
  "${SERVER_URL}/bms/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "manager",
    "password": "manager123"
  }')

echo "Manager登录响应: $MANAGER_RESPONSE"

# 提取token
MANAGER_TOKEN=$(echo $MANAGER_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ ! -z "$MANAGER_TOKEN" ]; then
    echo "✅ Manager登录成功，Token: ${MANAGER_TOKEN:0:20}..."
else
    echo "❌ Manager登录失败"
fi

echo ""
echo "4. 测试错误密码登录"

# 测试错误密码
ERROR_RESPONSE=$(curl -s -X POST \
  "${SERVER_URL}/bms/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "wrongpassword"
  }')

echo "错误密码登录响应: $ERROR_RESPONSE"

echo ""
echo "5. 测试不存在的用户"

# 测试不存在的用户
NOTFOUND_RESPONSE=$(curl -s -X POST \
  "${SERVER_URL}/bms/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "nonexistuser",
    "password": "password"
  }')

echo "不存在用户登录响应: $NOTFOUND_RESPONSE"

echo ""
echo "=== 测试完成 ==="
