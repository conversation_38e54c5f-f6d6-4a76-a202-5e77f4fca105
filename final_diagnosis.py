#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终诊断 - 深入分析admin/manager登录失败的原因
"""

import requests
import json

def deep_analysis():
    """深入分析用户差异"""
    print("=== 深入分析用户差异 ===")
    
    base_url = "http://localhost:8080"
    
    # 获取所有用户的完整信息
    response = requests.get(f"{base_url}/bms/system/user/list")
    if response.status_code != 200:
        print("❌ 无法获取用户列表")
        return
    
    users = response.json()
    
    # 分析每个用户的详细信息
    working_users = []
    broken_users = []
    
    test_passwords = {
        "admin": "admin123",
        "manager": "manager123", 
        "testuser": "test123",
        "debuguser": "debug123"
    }
    
    for user in users:
        username = user['username']
        password = test_passwords.get(username, "unknown")
        
        # 测试登录
        try:
            login_response = requests.post(
                f"{base_url}/bms/auth/login",
                json={"username": username, "password": password},
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if login_response.status_code == 200:
                working_users.append(user)
            else:
                broken_users.append(user)
        except:
            broken_users.append(user)
    
    print("工作用户的完整信息:")
    for user in working_users:
        print(f"\n{user['username']}:")
        for key, value in user.items():
            if key != 'password':  # 不显示完整密码
                print(f"  {key}: {value}")
            else:
                print(f"  {key}: {value[:20]}...")
    
    print("\n" + "="*50)
    print("不工作用户的完整信息:")
    for user in broken_users:
        print(f"\n{user['username']}:")
        for key, value in user.items():
            if key != 'password':
                print(f"  {key}: {value}")
            else:
                print(f"  {key}: {value[:20]}...")
    
    # 寻找关键差异
    print("\n" + "="*50)
    print("关键差异分析:")
    
    if working_users and broken_users:
        # 比较字段
        working_sample = working_users[0]
        broken_sample = broken_users[0]
        
        for key in working_sample.keys():
            if key == 'password':
                continue
            
            working_values = set(str(u.get(key)) for u in working_users)
            broken_values = set(str(u.get(key)) for u in broken_users)
            
            if working_values != broken_values:
                print(f"字段 '{key}' 存在差异:")
                print(f"  工作用户: {working_values}")
                print(f"  不工作用户: {broken_values}")

def test_role_permissions():
    """测试角色和权限"""
    print("=== 测试角色和权限 ===")
    
    base_url = "http://localhost:8080"
    
    # 尝试获取角色信息
    try:
        roles_response = requests.get(f"{base_url}/bms/system/role/enabled")
        if roles_response.status_code == 200:
            roles = roles_response.json()
            print(f"找到 {len(roles)} 个角色:")
            for role in roles:
                print(f"  {role.get('roleCode', 'N/A')}: {role.get('roleName', 'N/A')}")
        else:
            print(f"无法获取角色信息: {roles_response.status_code}")
    except Exception as e:
        print(f"获取角色信息异常: {e}")
    
    # 尝试获取用户角色关联
    try:
        user_roles_response = requests.get(f"{base_url}/bms/system/user-role/list")
        if user_roles_response.status_code == 200:
            user_roles = user_roles_response.json()
            print(f"\n找到 {len(user_roles)} 个用户角色关联:")
            for ur in user_roles:
                print(f"  用户ID {ur.get('userId')}: 角色ID {ur.get('roleId')}")
        else:
            print(f"无法获取用户角色关联: {user_roles_response.status_code}")
    except Exception as e:
        print(f"获取用户角色关联异常: {e}")

def test_direct_database_simulation():
    """模拟直接数据库操作"""
    print("=== 模拟直接数据库操作 ===")
    
    # 创建一个与admin完全相同的用户（除了用户名）
    base_url = "http://localhost:8080"
    
    # 首先获取admin用户的完整信息
    response = requests.get(f"{base_url}/bms/system/user/list")
    if response.status_code != 200:
        print("❌ 无法获取用户列表")
        return
    
    users = response.json()
    admin_user = next((u for u in users if u['username'] == 'admin'), None)
    
    if not admin_user:
        print("❌ 未找到admin用户")
        return
    
    print("找到admin用户，创建完全相同的测试用户...")
    
    # 创建与admin完全相同的用户
    clone_user = admin_user.copy()
    clone_user['username'] = 'adminclone'
    clone_user['userCode'] = 'ADMINCLONE001'
    clone_user['email'] = '<EMAIL>'
    clone_user['password'] = 'admin123'  # 明文密码
    
    # 移除不需要的字段
    for key in ['id', 'createTime', 'updateTime']:
        if key in clone_user:
            del clone_user[key]
    
    try:
        create_response = requests.post(
            f"{base_url}/bms/system/user",
            json=clone_user,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if create_response.status_code == 200:
            created_clone = create_response.json()
            print(f"✅ 克隆用户创建成功，ID: {created_clone['id']}")
            
            # 立即测试登录
            login_response = requests.post(
                f"{base_url}/bms/auth/login",
                json={"username": "adminclone", "password": "admin123"},
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if login_response.status_code == 200:
                print("✅ 克隆用户登录成功！")
                print("这说明问题不在用户数据结构，而在特定的用户记录")
            else:
                print(f"❌ 克隆用户登录失败: {login_response.status_code}")
                print("这说明问题可能在用户数据结构或权限配置")
        else:
            print(f"❌ 克隆用户创建失败: {create_response.status_code}")
            print(f"响应: {create_response.text}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")

def suggest_solutions():
    """建议解决方案"""
    print("=== 建议解决方案 ===")
    
    print("基于分析结果，可能的解决方案:")
    print()
    print("1. 数据库直接修复:")
    print("   - 直接在数据库中删除admin和manager用户")
    print("   - 重新运行初始化脚本创建用户")
    print()
    print("2. 权限问题:")
    print("   - 检查admin和manager用户是否有正确的角色分配")
    print("   - 确保角色表和用户角色关联表有正确的数据")
    print()
    print("3. 应用重启:")
    print("   - 重启应用以清除可能的缓存问题")
    print()
    print("4. 使用工作的用户格式:")
    print("   - 基于testuser的成功模式重新创建admin用户")

def main():
    """主函数"""
    print("最终诊断分析")
    print("=" * 60)
    
    # 深入分析用户差异
    deep_analysis()
    
    print("\n" + "=" * 60)
    
    # 测试角色权限
    test_role_permissions()
    
    print("\n" + "=" * 60)
    
    # 模拟直接数据库操作
    test_direct_database_simulation()
    
    print("\n" + "=" * 60)
    
    # 建议解决方案
    suggest_solutions()

if __name__ == "__main__":
    main()
