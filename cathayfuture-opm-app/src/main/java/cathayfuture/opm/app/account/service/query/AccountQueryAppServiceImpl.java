package cathayfuture.opm.app.account.service.query;

import cathayfuture.opm.client.utils.WxAccountHolder;
import cathayfuture.opm.client.account.api.query.AccountQueryAppService;
import cathayfuture.opm.client.account.api.query.RAccountStudentQueryAppService;
import cathayfuture.opm.client.account.dto.response.AccountRespDTO;
import cathayfuture.opm.client.account.dto.response.RAccountStudentRespDTO;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import cathayfuture.opm.domain.account.AccountEntity;
import cathayfuture.opm.domain.account.repository.AccountRepository;
import cathayfuture.opm.domain.student.enums.StudentTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AccountQueryAppServiceImpl implements AccountQueryAppService {

    @Resource
    private StudentQueryAppService studentQueryAppService;
    @Resource
    private RAccountStudentQueryAppService rAccountStudentQueryAppService;

    private AccountRepository repository;

    public AccountQueryAppServiceImpl(AccountRepository repository) {
        this.repository = repository;
    }

    @Override
    public List<StudentRespDTO> listBoundStudents() {
        AccountRespDTO accountRespDTO = WxAccountHolder.get();
        List<RAccountStudentRespDTO> rAccountStudentList = rAccountStudentQueryAppService.listByAccountId(accountRespDTO.getId());
        List<Integer> studentIdList = rAccountStudentList.stream().map(RAccountStudentRespDTO::getStudentId).collect(Collectors.toList());
        List<StudentRespDTO> result = studentQueryAppService.listByIds(studentIdList).stream().sorted(Comparator.comparing(StudentRespDTO::getStudentName)).collect(Collectors.toList());
        return result;
    }

    @Override
    public List<AccountRespDTO> listBoundAccounts(Integer studentId, Boolean includeCurrentUser) {
        List<RAccountStudentRespDTO> rAccountStudentList = rAccountStudentQueryAppService.listByStudentId(studentId);
        List<Integer> accountIdList = rAccountStudentList.stream().map(RAccountStudentRespDTO::getAccountId).collect(Collectors.toList());
        if (Objects.nonNull(includeCurrentUser) && !includeCurrentUser) {
            accountIdList.remove(WxAccountHolder.get().getId());
        }
        if (CollectionUtils.isEmpty(accountIdList)) {
            return Collections.emptyList();
        }
        List<AccountEntity> accountEntityList = repository.selectByIds(accountIdList);
        return accountEntityList.stream().map(this::convertRespDTO).collect(Collectors.toList());
    }

    private AccountRespDTO convertRespDTO(Object source) {
        AccountRespDTO respDTO = new AccountRespDTO();
        BeanUtils.copyProperties(source, respDTO);
        return respDTO;
    }

    private AccountEntity convertToEntity(Object source) {
        AccountEntity entity = new AccountEntity();
        BeanUtils.copyProperties(source, entity);
        return entity;
    }
}
