package cathayfuture.opm.app.account.service;

import cathayfuture.opm.client.account.api.RAccountStudentAppService;
import cathayfuture.opm.client.account.api.query.RAccountStudentQueryAppService;
import cathayfuture.opm.client.account.dto.request.RAccountStudentReqDTO;
import cathayfuture.opm.client.account.dto.response.RAccountStudentRespDTO;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import cathayfuture.opm.client.student.exception.StudentNotExistException;
import cathayfuture.opm.client.utils.WxAccountHolder;
import cathayfuture.opm.client.account.api.AccountAppService;
import cathayfuture.opm.client.account.dto.request.AccountReqDTO;
import cathayfuture.opm.client.account.dto.response.AccountRespDTO;
import cathayfuture.opm.domain.account.AccountEntity;
import cathayfuture.opm.domain.account.projection.WxProjection;
import cathayfuture.opm.domain.account.repository.AccountRepository;
import com.google.common.base.Joiner;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AccountAppServiceImpl implements AccountAppService {

    private AccountRepository repository;
    private WxProjection wxProjection;
    @Resource
    private StudentQueryAppService studentQueryAppService;
    @Resource
    private RAccountStudentQueryAppService rAccountStudentQueryAppService;
    @Resource
    private RAccountStudentAppService rAccountStudentAppService;

    public AccountAppServiceImpl(AccountRepository repository, WxProjection wxProjection) {
        this.repository = repository;
        this.wxProjection = wxProjection;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AccountRespDTO wxLogin(String code, String phoneCode, AccountReqDTO accountReqDTO) {
        AccountEntity reqAccountEntity = convertToEntity(accountReqDTO);
        AccountEntity sessionInfo = wxProjection.getSessionInfo(code);
        reqAccountEntity.setOpenId(sessionInfo.getOpenId());
        reqAccountEntity.setUnionId(sessionInfo.getUnionId());
        if (StringUtils.isNotEmpty(phoneCode)) {
            String phoneNumber = wxProjection.getPhoneNumber(code);
            reqAccountEntity.setPhoneNumber(phoneNumber);
        }
        AccountEntity accountEntity = repository.selectByOpenId(sessionInfo.getOpenId());
        if (Objects.nonNull(accountEntity)) {
            reqAccountEntity.setId(accountEntity.getId());
            reqAccountEntity.setCreateTime(new Date());
            repository.updateById(reqAccountEntity);
        } else {
            reqAccountEntity.setUpdateTime(new Date());
            repository.insert(reqAccountEntity);
        }
        return convertRespDTO(reqAccountEntity);
    }

    @Override
    public String bindPhoneNumber(String code) {
        String phoneNumber = wxProjection.getPhoneNumber(code);
        if (StringUtils.isNotEmpty(phoneNumber)) {
            AccountRespDTO currentUser = WxAccountHolder.get();
            AccountEntity updateParam = new AccountEntity();
            updateParam.setId(currentUser.getId());
            updateParam.setPhoneNumber(phoneNumber);
            repository.updateById(updateParam);
        }
        return phoneNumber;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<StudentRespDTO> bindStudents(List<StudentReqDTO> studentReqDTOList) {
        if (CollectionUtils.isEmpty(studentReqDTOList)) {
            return Collections.emptyList();
        }
        AccountRespDTO accountRespDTO = WxAccountHolder.get();
        List<RAccountStudentRespDTO> rAccountStudentList = rAccountStudentQueryAppService.listByAccountId(accountRespDTO.getId());
        Set<Integer> boundStudentIdSet = rAccountStudentList.stream().map(RAccountStudentRespDTO::getStudentId).collect(Collectors.toSet());
        List<Integer> unboundStudentIdList;
        if (!CollectionUtils.isEmpty(rAccountStudentList)) {
            unboundStudentIdList = new ArrayList<>(studentReqDTOList.stream().filter(s -> Objects.nonNull(s.getId()) && !boundStudentIdSet.contains(s.getId())).map(StudentReqDTO::getId).collect(Collectors.toSet()));
        } else {
            unboundStudentIdList = new ArrayList<>(studentReqDTOList.stream().filter(s -> Objects.nonNull(s.getId())).map(StudentReqDTO::getId).collect(Collectors.toSet()));
        }
        if (CollectionUtils.isEmpty(unboundStudentIdList)) {
            return Collections.emptyList();
        }
        List<StudentRespDTO> unboundStudentList = studentQueryAppService.listByIds(unboundStudentIdList);
        if (!Objects.equals(unboundStudentIdList.size(), unboundStudentList.size())) {
            Set<Integer> existStudentIdSet = unboundStudentList.stream().map(StudentRespDTO::getId).collect(Collectors.toSet());
            List<Integer> notExistStudentIdList = unboundStudentIdList.stream().filter(id -> !existStudentIdSet.contains(id)).collect(Collectors.toList());
            throw new StudentNotExistException("学生信息不存在，id[" + Joiner.on(",").join(notExistStudentIdList) + "]");
        }
        rAccountStudentAppService.batchAdd(unboundStudentList.stream().map(s -> {
            RAccountStudentReqDTO rAccountStudentReqDTO = new RAccountStudentReqDTO();
            rAccountStudentReqDTO.setAccountId(accountRespDTO.getId());
            rAccountStudentReqDTO.setStudentId(s.getId());
            return rAccountStudentReqDTO;
        }).collect(Collectors.toList()));
        return unboundStudentList;
    }


    private AccountRespDTO convertRespDTO(Object source) {
        AccountRespDTO respDTO = new AccountRespDTO();
        if (Objects.nonNull(source)) {
            BeanUtils.copyProperties(source, respDTO);
        }
        return respDTO;
    }

    private AccountEntity convertToEntity(Object source) {
        AccountEntity entity = new AccountEntity();
        if (Objects.nonNull(source)) {
            BeanUtils.copyProperties(source, entity);
        }
        return entity;
    }
}
