package cathayfuture.opm.app.account.service;

import cathayfuture.opm.client.account.api.RAccountStudentAppService;
import cathayfuture.opm.client.account.dto.request.RAccountStudentReqDTO;
import cathayfuture.opm.client.utils.WxAccountHolder;
import cathayfuture.opm.domain.account.RAccountStudentEntity;
import cathayfuture.opm.domain.account.repository.RAccountStudentRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RAccountStudentAppServiceImpl implements RAccountStudentAppService {

    private RAccountStudentRepository repository;

    public RAccountStudentAppServiceImpl(RAccountStudentRepository repository) {
        this.repository = repository;
    }

    @Override
    public int batchAdd(List<RAccountStudentReqDTO> rAccountStudentReqDTOList) {
        if (CollectionUtils.isEmpty(rAccountStudentReqDTOList)) {
            return 0;
        }
        List<RAccountStudentEntity> entityList = rAccountStudentReqDTOList.stream().map(e -> {
            RAccountStudentEntity entity = this.convertToEntity(e);
            entity.setCreatePerson(String.valueOf(WxAccountHolder.get().getId()));
            entity.setCreateTime(new Date());
            return entity;
        }).collect(Collectors.toList());
        return repository.insertBatch(entityList);
    }

    private RAccountStudentEntity convertToEntity(Object source) {
        RAccountStudentEntity entity = new RAccountStudentEntity();
        BeanUtils.copyProperties(source, entity);
        return entity;
    }
}
