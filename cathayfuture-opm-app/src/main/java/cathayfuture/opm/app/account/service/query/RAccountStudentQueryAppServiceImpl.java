package cathayfuture.opm.app.account.service.query;

import cathayfuture.opm.client.account.api.query.RAccountStudentQueryAppService;
import cathayfuture.opm.client.account.dto.response.RAccountStudentRespDTO;
import cathayfuture.opm.domain.account.RAccountStudentEntity;
import cathayfuture.opm.domain.account.repository.RAccountStudentRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class RAccountStudentQueryAppServiceImpl implements RAccountStudentQueryAppService {

    private RAccountStudentRepository repository;

    public RAccountStudentQueryAppServiceImpl(RAccountStudentRepository repository) {
        this.repository = repository;
    }

    @Override
    public List<RAccountStudentRespDTO> listByAccountId(Integer accountId) {
        if (Objects.isNull(accountId)) {
            return Collections.emptyList();
        }
        List<RAccountStudentEntity> entityList = repository.listByAccountId(accountId);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        return entityList.stream().map(this::convertRespDTO).collect(Collectors.toList());
    }

    @Override
    public List<RAccountStudentRespDTO> listByStudentId(Integer studentId) {
        if (Objects.isNull(studentId)) {
            return Collections.emptyList();
        }
        List<RAccountStudentEntity> entityList = repository.listByStudentId(studentId);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        return entityList.stream().map(this::convertRespDTO).collect(Collectors.toList());
    }

    private RAccountStudentRespDTO convertRespDTO(Object source) {
        RAccountStudentRespDTO respDTO = new RAccountStudentRespDTO();
        BeanUtils.copyProperties(source, respDTO);
        return respDTO;
    }
}
