package cathayfuture.opm.app.student.service;

import cathayfuture.opm.app.common.RedisLockService;
import cathayfuture.opm.client.action.api.ActionAppService;
import cathayfuture.opm.client.action.dto.request.ActionReqDTO;
import cathayfuture.opm.client.order.api.query.OrderQueryAppService;
import cathayfuture.opm.client.order.dto.request.CreateFrontMoneyOrderReqDTO;
import cathayfuture.opm.client.order.dto.response.OrderRespDTO;
import cathayfuture.opm.client.order.exception.OrderCreateException;
import cathayfuture.opm.client.student.api.StudentAppService;
import cathayfuture.opm.client.student.api.query.ExpenseDefaultQueryAppService;
import cathayfuture.opm.client.student.api.query.ExpenseStandardQueryAppService;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.request.ExpenseStandardReqDTO;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import cathayfuture.opm.client.student.dto.response.ExpenseDefaultQueryRespDto;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import cathayfuture.opm.client.student.exception.StudentAddException;
import cathayfuture.opm.client.student.exception.StudentNotExistException;
import cathayfuture.opm.domain.action.enums.ActionCodeEnum;
import cathayfuture.opm.domain.action.enums.ModuleEnum;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import cathayfuture.opm.domain.order.enums.PaymentStatusEnum;
import cathayfuture.opm.domain.student.StudentEntity;
import cathayfuture.opm.domain.student.enums.ExpenseStandardTypeEnum;
import cathayfuture.opm.domain.student.enums.RegisterStatusEnum;
import cathayfuture.opm.domain.student.enums.StudentTypeEnum;
import cathayfuture.opm.domain.student.repository.StudentRepository;
import cathayfuture.opm.infra.common.AopContextUtils;
import cathayfuture.opm.infra.common.ExtServiceContext;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.dtyunxi.huieryun.lock.api.Mutex;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class StudentAppServiceImpl implements StudentAppService {

    private static final String STUDENT_NO_LOCK_KEY = "STUDENT_NO_LOCK_KEY";

    private static final String STUDENT_ID_NUMBER_LOCK_KEY = "STUDENT_ID_NUMBER_LOCK_KEY";

    @Resource
    private RedisLockService redisLockService;

    private StudentRepository repository;

    @Resource
    private ActionAppService actionAppService;
    @Resource
    private OrderQueryAppService orderQueryAppService;
    @Resource
    private ExpenseStandardQueryAppService expenseStandardQueryAppService;
    @Resource
    private ExpenseDefaultQueryAppService expenseDefaultQueryAppService;
    @Resource
    private StudentQueryAppService studentQueryAppService;

    public StudentAppServiceImpl(StudentRepository repository) {
        this.repository = repository;
    }

    private StudentAppServiceImpl getProxy() {
        return AopContextUtils.getProxy(this);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StudentRespDTO addFrontMoneyStudent(StudentReqDTO studentReqDTO) {
        StudentEntity studentEntity = new StudentEntity();
        BeanUtils.copyProperties(studentReqDTO, studentEntity);
        studentEntity.setBirthday(LocalDate.parse(studentReqDTO.getBirthday(), DateTimeFormatter.ISO_LOCAL_DATE));
        studentEntity.setCreatePerson(ExtServiceContext.getUserCode());
        studentEntity.setCreateTime(new Date());
        StudentEntity addedStudentEntity = repository.add(studentEntity);
        StudentRespDTO respDTO = new StudentRespDTO();
        BeanUtils.copyProperties(addedStudentEntity, respDTO);
        return respDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StudentRespDTO save(StudentReqDTO studentReqDTO) {
        Mutex mutexStudentNo = null;
        Mutex mutexIdNumber = null;
        try {
            log.info("保存学生[{}]", JSONObject.toJSONString(studentReqDTO));
            mutexStudentNo = redisLockService.lock(STUDENT_NO_LOCK_KEY, studentReqDTO.getStudentNo());
            log.info(STUDENT_NO_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutexStudentNo.getLockKey());
            mutexIdNumber = redisLockService.lock(STUDENT_ID_NUMBER_LOCK_KEY, studentReqDTO.getIdNumber());
            log.info(STUDENT_ID_NUMBER_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutexIdNumber.getLockKey());
            int count = repository.countByStudentNo(studentReqDTO.getStudentNo(), studentReqDTO.getId());
            if (count > 0) {
                throw new StudentAddException("该学生学号已存在！");
            }
            count = repository.countByIdNumber(studentReqDTO.getIdNumber(), studentReqDTO.getId());
            if (count > 0) {
                throw new StudentAddException("该学生身份证号已存在！");
            }
            StudentEntity studentEntity = new StudentEntity();
            BeanUtils.copyProperties(studentReqDTO, studentEntity);
            studentEntity.setStudentType(StudentTypeEnum.SIGNED.getKey());
            studentEntity.setBirthday(LocalDate.parse(studentReqDTO.getBirthday(), DateTimeFormatter.ISO_LOCAL_DATE));
            if (Objects.nonNull(studentReqDTO.getAdmissionDate())) {
                studentEntity.setAdmissionDate(LocalDate.parse(studentReqDTO.getAdmissionDate(), DateTimeFormatter.ISO_LOCAL_DATE));
            }
            if (Objects.isNull(studentEntity.getId())) {
                log.info("新增学生[{}]", JSONObject.toJSONString(studentEntity));
                studentEntity.setCreatePerson(ExtServiceContext.getUserCode());
                studentEntity.setCreateTime(new Date());
                //新增学生，入园状态默认为0
                studentEntity.setRegisterStatus(RegisterStatusEnum.APPLICATION.getKey());
                repository.add(studentEntity);
            } else {
                log.info("更新学生[{}]", JSONObject.toJSONString(studentEntity));
                StudentEntity student = repository.getById(studentReqDTO.getId());
                StudentReqDTO studentOld = new StudentReqDTO();
                BeanUtils.copyProperties(student, studentOld);
                studentOld.setBirthday(Optional.ofNullable(student.getBirthday()).map(d -> d.format(DateTimeFormatter.ISO_LOCAL_DATE)).orElse(null));
                studentOld.setAdmissionDate(Optional.ofNullable(student.getAdmissionDate()).map(d -> d.format(DateTimeFormatter.ISO_LOCAL_DATE)).orElse(null));
                StudentReqDTO studentNew = studentReqDTO;
                List<ActionReqDTO> actionReqDTOList = actionAppService.collectDiff(studentNew, studentOld);
               if(actionReqDTOList.size() > 0 ){
                   actionAppService.addBatch(actionReqDTOList);
               }
                studentEntity.setUpdatePerson(ExtServiceContext.getUserCode());
                studentEntity.setUpdateTime(new Date());
                repository.updateById(studentEntity);
            }
            StudentRespDTO respDTO = new StudentRespDTO();
            BeanUtils.copyProperties(studentEntity, respDTO);
            return respDTO;
        } catch (Exception e) {
            log.error("保存学生失败, idNumber[{}]，studentNo[{}]", studentReqDTO.getStudentNo(), studentReqDTO.getIdNumber(), e);
            throw e;
        } finally {
            log.info(STUDENT_NO_LOCK_KEY + ":lockService.unlock_mutex: [{}]", Optional.ofNullable(mutexStudentNo).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutexStudentNo);
            log.info(STUDENT_ID_NUMBER_LOCK_KEY + ":lockService.unlock_mutex: [{}]", Optional.ofNullable(mutexIdNumber).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutexIdNumber);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public StudentRespDTO register(StudentReqDTO studentReqDTO) {
        log.info("register 入园入参：{}", JSONUtil.toJsonStr(studentReqDTO));
        StringBuilder builder = new StringBuilder();
        if(StringUtils.isBlank(studentReqDTO.getStudentNo())){
            builder.append("学号，");
        }
        if(StringUtils.isBlank(studentReqDTO.getStudentClass())){
            builder.append("班级，");
        }
        if(StringUtils.isBlank(studentReqDTO.getEthnicGroup())){
            builder.append("民族，");
        }
        if(StringUtils.isBlank(studentReqDTO.getIdNumber())){
            builder.append("身份证号，");
        }
        if(StringUtils.isBlank(studentReqDTO.getNationality())){
            builder.append("国籍，");
        }
        if(StringUtils.isBlank(studentReqDTO.getAdmissionDate())){
            builder.append("入园时间，");
        }

        if(builder.length()>0){
            String substring = builder.substring(0, builder.lastIndexOf("，"));
            throw new StudentAddException(substring+" 不能为空");
        }

        Mutex mutexStudentNo = null;
        Mutex mutexIdNumber = null;
        try {
            log.info("学生入园[{}]", JSONObject.toJSONString(studentReqDTO));
            mutexStudentNo = redisLockService.lock(STUDENT_NO_LOCK_KEY, studentReqDTO.getStudentNo());
            log.info(STUDENT_NO_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutexStudentNo.getLockKey());
            mutexIdNumber = redisLockService.lock(STUDENT_ID_NUMBER_LOCK_KEY, studentReqDTO.getIdNumber());
            log.info(STUDENT_ID_NUMBER_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutexIdNumber.getLockKey());
            StudentEntity studentEntity = new StudentEntity();
            BeanUtils.copyProperties(studentReqDTO, studentEntity);
            studentEntity.setStudentType(StudentTypeEnum.SIGNED.getKey());
            studentEntity.setBirthday(LocalDate.parse(studentReqDTO.getBirthday(), DateTimeFormatter.ISO_LOCAL_DATE));
            if (Objects.nonNull(studentReqDTO.getAdmissionDate())) {
                studentEntity.setAdmissionDate(LocalDate.parse(studentReqDTO.getAdmissionDate(), DateTimeFormatter.ISO_LOCAL_DATE));
            }

          if(Objects.equals(RegisterStatusEnum.ENTER.getKey(),studentEntity.getRegisterStatus())){
                throw new StudentAddException("已入园的学生无法更新学生信息");
            }
            List<OrderRespDTO> orderRespDTOS = orderQueryAppService.queryOrderListByStudentIdListAndTypeAndPaymentStatus(Lists.newArrayList(studentReqDTO.getId()),
                    OrderTypeEnum.INCIDENTALS.getKey(),Lists.newArrayList(PaymentStatusEnum.PAID.getKey(),PaymentStatusEnum.NEEDLESS.getKey()));

            if(CollectionUtil.isEmpty(orderRespDTOS)){
                throw new StudentAddException("请先完成预报名流程！");
            }

            List<OrderRespDTO> frontMoneyList = orderQueryAppService.queryOrderListByStudentIdListAndTypeAndPaymentStatus(Lists.newArrayList(studentReqDTO.getId()),
                    OrderTypeEnum.FRONT_MONEY.getKey(),Lists.newArrayList(PaymentStatusEnum.PAID.getKey(),PaymentStatusEnum.NEEDLESS.getKey()));
            if(CollectionUtil.isEmpty(frontMoneyList)){
                throw new StudentAddException("请先完成定金的缴纳");
            }
            // 插入一条费用标准数据
            ExpenseStandardReqDTO reqDTO = getProxy().convertExpenseStandardReqDTO(studentReqDTO);
            expenseStandardQueryAppService.add(reqDTO);


            log.info("更新学生[{}]", JSONObject.toJSONString(studentEntity));
            StudentEntity student = repository.getById(studentReqDTO.getId());
            StudentReqDTO studentOld = new StudentReqDTO();
            BeanUtils.copyProperties(student, studentOld);
            studentOld.setBirthday(Optional.ofNullable(student.getBirthday()).map(d -> d.format(DateTimeFormatter.ISO_LOCAL_DATE)).orElse(null));
            studentOld.setAdmissionDate(Optional.ofNullable(student.getAdmissionDate()).map(d -> d.format(DateTimeFormatter.ISO_LOCAL_DATE)).orElse(null));
            StudentReqDTO studentNew = studentReqDTO;
            List<ActionReqDTO> actionReqDTOList = actionAppService.collectDiff(studentNew, studentOld, ActionCodeEnum.REGISTER.getKey(), ModuleEnum.STUDENT.getKey());
            actionAppService.addBatch(actionReqDTOList);

            studentEntity.setRegisterStatus(RegisterStatusEnum.ENTER.getKey());
            studentEntity.setUpdatePerson(ExtServiceContext.getUserCode());
            studentEntity.setUpdateTime(new Date());
            repository.updateById(studentEntity);

            StudentRespDTO respDTO = new StudentRespDTO();
            BeanUtils.copyProperties(studentEntity, respDTO);
            return respDTO;
        } catch (Exception e) {
            log.error("学生入园失败, idNumber[{}]，studentNo[{}]", studentReqDTO.getStudentNo(), studentReqDTO.getIdNumber(), e);
            throw e;
        } finally {
            log.info(STUDENT_NO_LOCK_KEY + ":lockService.unlock_mutex: [{}]", Optional.ofNullable(mutexStudentNo).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutexStudentNo);
            log.info(STUDENT_ID_NUMBER_LOCK_KEY + ":lockService.unlock_mutex: [{}]", Optional.ofNullable(mutexIdNumber).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutexIdNumber);
        }
    }

    public ExpenseStandardReqDTO convertExpenseStandardReqDTO(StudentReqDTO studentReqDTO){
        //获取保育费标准默认值
        ExpenseDefaultQueryRespDto childCare = expenseDefaultQueryAppService.getDtoByType(OrderTypeEnum.CHILD_CARE.getKey());

        ExpenseStandardReqDTO reqDTO = new ExpenseStandardReqDTO();
        reqDTO.setStudentId(studentReqDTO.getId());
        reqDTO.setStudentNo(studentReqDTO.getStudentNo());
        reqDTO.setStudentName(studentReqDTO.getStudentName());
        reqDTO.setExpenseStandardType(ExpenseStandardTypeEnum.NOT_SIGNED.getKey());
        reqDTO.setExpenseStandard(childCare.getValue());
        reqDTO.setExpenseType(OrderTypeEnum.CHILD_CARE.getKey());
        reqDTO.setDefaultId(childCare.getId());
        return reqDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void quit(Integer studentId){
        log.info("register 退园操作入参：{}", studentId);
        StudentEntity entity = repository.getById(studentId);
        if(!Objects.equals(RegisterStatusEnum.ENTER.getKey(),entity.getRegisterStatus())){
            throw new StudentNotExistException("只有入园的学生才能执行 退园 操作");
        }
        StudentEntity update = new StudentEntity();
        BeanUtils.copyProperties(entity,update);
        update.setRegisterStatus(RegisterStatusEnum.QUIT.getKey());
        repository.updateById(update);

        List<ActionReqDTO> actionReqDTOList = actionAppService.collectDiff(update, entity, ActionCodeEnum.QUIT.getKey(), ModuleEnum.STUDENT.getKey());
        actionAppService.addBatch(actionReqDTOList);
    }


    @Override
    public void checkRepeatData(CreateFrontMoneyOrderReqDTO reqDTO){
        List<StudentRespDTO> studentList = studentQueryAppService.queryByCondition(reqDTO.getStudentName(),
                LocalDate.parse(reqDTO.getBirthday(),DateTimeFormatter.ISO_DATE),
                RegisterStatusEnum.getActiveList());
        if(CollectionUtil.isNotEmpty(studentList)){
            throw new OrderCreateException("当前学生已经注册，请勿重复注册");
        }
    }
}
