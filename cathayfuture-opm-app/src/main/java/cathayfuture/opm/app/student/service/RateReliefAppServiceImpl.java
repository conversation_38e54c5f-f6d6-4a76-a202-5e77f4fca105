package cathayfuture.opm.app.student.service;

import cathayfuture.opm.client.action.api.ActionAppService;
import cathayfuture.opm.client.action.dto.request.ActionReqDTO;
import cathayfuture.opm.client.student.api.RateReliefAppService;
import cathayfuture.opm.client.student.api.query.ExpenseStandardQueryAppService;
import cathayfuture.opm.client.student.dto.request.ReteReliefAddReqDto;
import cathayfuture.opm.client.student.dto.response.ExpenseStandardRespDTO;
import cathayfuture.opm.client.student.exception.RateReliefAddExceptipn;
import cathayfuture.opm.domain.action.enums.ActionCodeEnum;
import cathayfuture.opm.domain.action.enums.ModuleEnum;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import cathayfuture.opm.domain.student.RateReliefEntity;
import cathayfuture.opm.domain.student.enums.ExpenseTypeEnum;
import cathayfuture.opm.domain.student.repository.RateReliefRepository;
import cathayfuture.opm.infra.common.AopContextUtils;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 减免
 * @date 2023/3/31 10:09
 */
@Service
@Slf4j
public class RateReliefAppServiceImpl implements RateReliefAppService {

    @Resource
    private ActionAppService actionAppService;
    @Resource
    private ExpenseStandardQueryAppService expenseStandardQueryAppService;

    private RateReliefAppServiceImpl getProxy() {
        return AopContextUtils.getProxy(this);
    }

    private RateReliefRepository repository;

    public RateReliefAppServiceImpl(RateReliefRepository repository) {
        this.repository = repository;
    }
    private final DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ReteReliefAddReqDto reqDto){

        getProxy().checkDate(reqDto.getDerateMonth());

        getProxy().checkAmount(reqDto.getStudentId(),reqDto.getDerateAmount());

        RateReliefEntity entity = new RateReliefEntity();
        entity.setStudentId(reqDto.getStudentId());
        entity.setCostType(OrderTypeEnum.CHILD_CARE.getKey());
        entity.setDerateMonth(reqDto.getDerateMonth());
        entity.setDerateAmount(reqDto.getDerateAmount());
        entity.setRemark(reqDto.getRemark());
        repository.add(entity);
        //写入操作流水表
        List<ActionReqDTO> actionReqDTOS = actionAppService.collectDiff(entity,null, ActionCodeEnum.ADD.getKey(), ModuleEnum.RATE_RELIEF.getKey());
        actionAppService.addBatch(actionReqDTOS);
    }

    @Override
    public void delete(Integer id){
        RateReliefEntity entity = repository.findById(id);

        getProxy().checkDate(entity.getDerateMonth());

        repository.delete(id);
        //写入操作流水表
        List<ActionReqDTO> actionReqDTOS = actionAppService.collectDiff(entity,null, ActionCodeEnum.DELETE.getKey(), ModuleEnum.RATE_RELIEF.getKey());
        actionAppService.addBatch(actionReqDTOS);
    }

    public void checkDate(String month){
        String paramDate = month+"-01";
        LocalDate parse = LocalDate.parse(paramDate, DF);
        LocalDate firstDayOfMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        //减免月份不能是当前月份之前或当前月，只能是之后的月份
        if(firstDayOfMonth.isAfter(parse)){
            throw new RateReliefAddExceptipn("减免月份不能选择已经发生结余的月份");
        }
    }

    /**
     * 减免金额需要小于费用标准
     * @param studentId
     * @param target
     */
    public void checkAmount(Integer studentId,BigDecimal target){
        List<ExpenseStandardRespDTO> ExpenseStandardRespDTOS = expenseStandardQueryAppService.findListByStudentIdsAndExpenseType(Lists.newArrayList(studentId), ExpenseTypeEnum.CHILD_CARE.getKey());
        if(CollectionUtil.isEmpty(ExpenseStandardRespDTOS)){
            return;
        }
        BigDecimal expenseStandard = ExpenseStandardRespDTOS.stream()
                .findFirst()
                .orElse(new ExpenseStandardRespDTO())
                .getExpenseStandard();
        if(expenseStandard.compareTo(target)<0){
            throw new RateReliefAddExceptipn("减免金额需要小于费用标准");
        }
    }
}
