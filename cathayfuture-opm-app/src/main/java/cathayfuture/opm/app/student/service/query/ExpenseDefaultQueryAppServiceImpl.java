package cathayfuture.opm.app.student.service.query;

import cathayfuture.opm.client.student.api.query.ExpenseDefaultQueryAppService;
import cathayfuture.opm.client.student.dto.response.ExpenseDefaultQueryRespDto;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import cathayfuture.opm.domain.student.ExpenseDefaultEntity;
import cathayfuture.opm.domain.student.repository.ExpenseDefaultRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 收费标准默认值
 * @date 2023/4/11 14:18
 */

@Service
@Slf4j
public class ExpenseDefaultQueryAppServiceImpl implements ExpenseDefaultQueryAppService {

    private ExpenseDefaultRepository expenseDefaultRepository;

    public ExpenseDefaultQueryAppServiceImpl(ExpenseDefaultRepository expenseDefaultRepository) {
        this.expenseDefaultRepository = expenseDefaultRepository;
    }

    @Override
    public BigDecimal getById(Integer id){
        ExpenseDefaultEntity byId = expenseDefaultRepository.getById(id);
        return byId.getDefaultValue();
    }

    @Override
    public BigDecimal getByType(Integer type){
        ExpenseDefaultEntity byType = expenseDefaultRepository.getByType(type);
        return byType.getDefaultValue();
    }

    @Override
    public ExpenseDefaultQueryRespDto getDtoByType(Integer type){
        ExpenseDefaultEntity byType = expenseDefaultRepository.getByType(type);

        ExpenseDefaultQueryRespDto respDto = new ExpenseDefaultQueryRespDto();
        respDto.setId(byType.getId());
        respDto.setValue(byType.getDefaultValue());

        return respDto;
    }
}
