package cathayfuture.opm.app.student.service.query;

import cathayfuture.opm.client.student.api.query.StudentClassQueryAppService;
import cathayfuture.opm.domain.student.StudentClassEntity;
import cathayfuture.opm.domain.student.repository.StudentClassRepository;
import cathayfuture.opm.infra.student.repository.mapper.StudentClassMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 班级
 * @date 2023/4/4 14:38
 */
@Service
@Slf4j
public class StudentClassQueryAppServiceImpl implements StudentClassQueryAppService {

    private StudentClassRepository repository;

    private StudentClassMapper mapper;

    public StudentClassQueryAppServiceImpl(StudentClassRepository repository, StudentClassMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    @Override
    public List<String> findAllNames(){
        List<StudentClassEntity> allList = repository.findAllList();
        return allList.stream()
                .map(StudentClassEntity::getName)
                .collect(Collectors.toList());
    }
}
