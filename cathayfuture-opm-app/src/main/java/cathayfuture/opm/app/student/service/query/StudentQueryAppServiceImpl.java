package cathayfuture.opm.app.student.service.query;

import cathayfuture.opm.client.account.api.query.RAccountStudentQueryAppService;
import cathayfuture.opm.client.account.dto.response.AccountRespDTO;
import cathayfuture.opm.client.account.dto.response.RAccountStudentRespDTO;
import cathayfuture.opm.client.common.SmsService;
import cathayfuture.opm.client.common.dto.request.CheckSmsVerificationCodeReqDTO;
import cathayfuture.opm.client.common.exception.SmsCheckException;
import cathayfuture.opm.client.order.api.query.OrderQueryAppService;
import cathayfuture.opm.client.order.dto.response.OrderRespDTO;
import cathayfuture.opm.client.order.dto.response.PageRespDTO;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.request.QueryByContactPhoneNumberReqDTO;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import cathayfuture.opm.client.utils.WxAccountHolder;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import cathayfuture.opm.domain.order.enums.PaymentStatusEnum;
import cathayfuture.opm.domain.student.StudentEntity;
import cathayfuture.opm.domain.student.enums.GenderEnum;
import cathayfuture.opm.domain.student.enums.RegisterStatusEnum;
import cathayfuture.opm.domain.student.repository.StudentRepository;
import cathayfuture.opm.infra.common.beantools.BeanTools;
import cathayfuture.opm.infra.student.repository.mapper.StudentMapper;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdcardUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import icu.mhb.mybatisplus.plugln.base.service.impl.JoinServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class StudentQueryAppServiceImpl extends JoinServiceImpl<StudentMapper, StudentEntity> implements StudentQueryAppService {

    @Resource
    private SmsService smsService;
    @Resource
    private RAccountStudentQueryAppService rAccountStudentQueryAppService;
    @Resource
    private OrderQueryAppService orderQueryAppService;

    private StudentRepository repository;

    private StudentMapper studentMapper;

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public StudentQueryAppServiceImpl(StudentRepository repository, StudentMapper studentMapper) {
        this.repository = repository;
        this.studentMapper = studentMapper;
    }

    @Override
    public StudentRespDTO getById(Integer studentId) {
        return convertRespDTO(repository.getById(studentId));
    }

    @Override
    public List<StudentRespDTO> listByIds(List<Integer> studentIdList) {
        if (CollectionUtils.isEmpty(studentIdList)) {
            return Collections.emptyList();
        }
        List<StudentEntity> entityList = repository.listByIds(studentIdList);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        return entityList.stream().map(this::convertRespDTO).collect(Collectors.toList());
    }

    @Override
    public List<StudentRespDTO> listByContactPhoneNumber(QueryByContactPhoneNumberReqDTO queryByContactPhoneNumberReqDTO) {
        String contactPhoneNumber = queryByContactPhoneNumberReqDTO.getContactPhoneNumber();
        String smsVerificationCode = queryByContactPhoneNumberReqDTO.getSmsVerificationCode();
        if (!smsService.checkSmsVerificationCode(new CheckSmsVerificationCodeReqDTO().setPhoneNumber(contactPhoneNumber).setSmsVerificationCode(smsVerificationCode))) {
            throw new SmsCheckException("短信验证码错误");
        }
        return listByContactPhoneNumber(contactPhoneNumber);
    }

    @Override
    public List<StudentRespDTO> listByContactPhoneNumber(String phoneNumber) {
        List<StudentEntity> entityList = repository.listByContactPhoneNumber(phoneNumber);
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        List<StudentRespDTO> result = entityList.stream().map(this::convertRespDTO).collect(Collectors.toList());
        AccountRespDTO currentUser = WxAccountHolder.get();
        if (Objects.nonNull(currentUser)) {
            Set<Integer> boundStudentIdSet = rAccountStudentQueryAppService.listByAccountId(currentUser.getId()).stream().map(RAccountStudentRespDTO::getStudentId).collect(Collectors.toSet());
            result.forEach(r -> r.setBindFlag(boundStudentIdSet.contains(r.getId())));
        }
        return result;
    }

    @Override
    public List<StudentRespDTO> listByType(Integer type) {
        List<StudentEntity> studentEntities = repository.listByType(type);
        return studentEntities.stream().map(this::convertRespDTO).collect(Collectors.toList());
    }

    @Override
    public List<StudentRespDTO> list(StudentReqDTO studentReqDTO) {
        LocalDateTime startAdmissionDate = null;
        LocalDateTime endAdmissionDate = null;
        if (StringUtils.isNotBlank(studentReqDTO.getStartAdmissionDate())) {
            startAdmissionDate = LocalDate.parse(studentReqDTO.getStartAdmissionDate(), DateTimeFormatter.ofPattern(YYYY_MM_DD)).atStartOfDay();
        }
        if (StringUtils.isNotBlank(studentReqDTO.getEndAdmissionDate())) {
            endAdmissionDate = LocalDate.parse(studentReqDTO.getEndAdmissionDate(), DateTimeFormatter.ofPattern(YYYY_MM_DD)).plusDays(1L).atStartOfDay();
        }
        return studentMapper.selectList(Wrappers.lambdaQuery(StudentEntity.class)
                        .orderByDesc(StudentEntity::getCreateTime, StudentEntity::getId)
                .like(StringUtils.isNotBlank(studentReqDTO.getContactPhoneNumber()), StudentEntity::getContactPhoneNumber, studentReqDTO.getContactPhoneNumber())
                .like(StringUtils.isNotBlank(studentReqDTO.getContactName()), StudentEntity::getContactName, studentReqDTO.getContactName())
                .like(StringUtils.isNotBlank(studentReqDTO.getStudentClass()), StudentEntity::getStudentClass, studentReqDTO.getStudentClass())
                .like(StringUtils.isNotBlank(studentReqDTO.getStudentName()), StudentEntity::getStudentName, studentReqDTO.getStudentName())
                .like(StringUtils.isNotBlank(studentReqDTO.getStudentNo()), StudentEntity::getStudentNo, studentReqDTO.getStudentNo())
                .ge(Objects.nonNull(startAdmissionDate), StudentEntity::getAdmissionDate, startAdmissionDate)
                .le(Objects.nonNull(endAdmissionDate), StudentEntity::getAdmissionDate, endAdmissionDate)
                .eq(Objects.nonNull(studentReqDTO.getStudentType()), StudentEntity::getStudentType, studentReqDTO.getStudentType())
                .and(StringUtils.isNotBlank(studentReqDTO.getParentName()), o -> o.like(StudentEntity::getFatherName, studentReqDTO.getParentName()).or().like(StudentEntity::getMotherName, studentReqDTO.getParentName())))
                .stream()
                .map(this::convertRespDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<StudentRespDTO> listByStudentNo(String studentNo) {
        return studentMapper.selectList(Wrappers.lambdaQuery(StudentEntity.class)
                        .orderByDesc(StudentEntity::getId)
                        .eq(StudentEntity::getStudentNo, studentNo))
                .stream()
                .map(this::convertRespDTO)
                .collect(Collectors.toList());
    }

    @Override
    public PageRespDTO<StudentRespDTO> page(StudentReqDTO studentReqDTO, int pageNum, int pageSize) {
        LocalDateTime startAdmissionDate = null;
        LocalDateTime endAdmissionDate = null;
        if (StringUtils.isNotBlank(studentReqDTO.getStartAdmissionDate())) {
            startAdmissionDate = LocalDate.parse(studentReqDTO.getStartAdmissionDate(), DateTimeFormatter.ofPattern(YYYY_MM_DD)).atStartOfDay();
        }
        if (StringUtils.isNotBlank(studentReqDTO.getEndAdmissionDate())) {
            endAdmissionDate = LocalDate.parse(studentReqDTO.getEndAdmissionDate(), DateTimeFormatter.ofPattern(YYYY_MM_DD)).plusDays(1L).atStartOfDay();
        }

        Page<StudentEntity> studentEntityPage = studentMapper
                .selectPage(new Page<>(pageNum, pageSize),
                        Wrappers.lambdaQuery(StudentEntity.class)
                                .orderByDesc(StudentEntity::getCreateTime, StudentEntity::getId)
                                .like(StringUtils.isNotBlank(studentReqDTO.getContactPhoneNumber()), StudentEntity::getContactPhoneNumber, studentReqDTO.getContactPhoneNumber())
                                .like(StringUtils.isNotBlank(studentReqDTO.getContactName()), StudentEntity::getContactName, studentReqDTO.getContactName())
                                .like(StringUtils.isNotBlank(studentReqDTO.getStudentClass()), StudentEntity::getStudentClass, studentReqDTO.getStudentClass())
                                .like(StringUtils.isNotBlank(studentReqDTO.getStudentName()), StudentEntity::getStudentName, studentReqDTO.getStudentName())
                                .like(StringUtils.isNotBlank(studentReqDTO.getStudentNo()), StudentEntity::getStudentNo, studentReqDTO.getStudentNo())
                                .ge(Objects.nonNull(startAdmissionDate), StudentEntity::getAdmissionDate, startAdmissionDate)
                                .le(Objects.nonNull(endAdmissionDate), StudentEntity::getAdmissionDate, endAdmissionDate)
                                .eq(Objects.nonNull(studentReqDTO.getStudentType()), StudentEntity::getStudentType, studentReqDTO.getStudentType())
                                .and(StringUtils.isNotBlank(studentReqDTO.getParentName()), o -> o.like(StudentEntity::getFatherName, studentReqDTO.getParentName()).or().like(StudentEntity::getMotherName, studentReqDTO.getParentName()))
                                .eq(Objects.nonNull(studentReqDTO.getRegisterStatus()), StudentEntity::getRegisterStatus, studentReqDTO.getRegisterStatus())
                );

        PageRespDTO<StudentRespDTO> result = new PageRespDTO<>();

        List<StudentRespDTO> collect = studentEntityPage.getRecords().stream().map(this::convertRespDTO).collect(Collectors.toList());

        translateCompletePayment(collect);

        translateRegisterStatusStr(collect);

        result.setPageNum(studentEntityPage.getCurrent())
                .setPageSize(studentEntityPage.getSize())
                .setTotal(studentEntityPage.getTotal())
                .setRecords(collect);
        return result;
    }

    private StudentRespDTO convertRespDTO(Object source) {
        StudentRespDTO respDTO = new StudentRespDTO();
        BeanUtils.copyProperties(source, respDTO);
        return respDTO;
    }

    @Override
    public Integer countByStudentNo(String studentNo) {
        return repository.countByStudentNo(studentNo, null);
    }

    @Override
    public StudentRespDTO idCardResolver(String idNumber) {
        StudentRespDTO result = new StudentRespDTO();
        if (StringUtils.isBlank(idNumber) || !IdcardUtil.isValidCard(idNumber)) {
            return result;
        }
        result.setGender(GenderEnum.getByOrdinal(IdcardUtil.getGenderByIdCard(idNumber)).getKey());
        result.setBirthday(IdcardUtil.getBirthDate(idNumber).toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
        return result;
    }


    public void translateCompletePayment(List<StudentRespDTO> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        List<Integer> stuIds = list.stream()
                .map(StudentRespDTO::getId)
                .collect(Collectors.toList());
        List<OrderRespDTO> orderRespDTOS = orderQueryAppService.queryOrderListByStudentIdListAndTypeAndPaymentStatus(stuIds, OrderTypeEnum.INCIDENTALS.getKey(),
                Lists.newArrayList(PaymentStatusEnum.PAID.getKey(),PaymentStatusEnum.NEEDLESS.getKey()));
        if(CollectionUtil.isEmpty(orderRespDTOS)){
            for(StudentRespDTO dto :list){
                dto.setCompletePayment(Boolean.FALSE);
            }
            return;
        }

        Map<Integer, List<OrderRespDTO>> collect = orderRespDTOS.stream()
                .collect(Collectors.groupingBy(OrderRespDTO::getStudentId));

        for(StudentRespDTO dto :list){
            boolean completePay = Boolean.FALSE;
            //对应杂费的支付状态是已支付和无需支付才是true
            List<OrderRespDTO> orderList = collect.get(dto.getId());
            if(CollectionUtil.isNotEmpty(orderList)){
                completePay = Boolean.TRUE;
            }
            dto.setCompletePayment(completePay);

        }
    }

    public void translateRegisterStatusStr(List<StudentRespDTO> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        for(StudentRespDTO data :list){
            String desc = RegisterStatusEnum.getEnumDescription(data.getRegisterStatus());
            data.setRegisterStatusStr(desc);
        }
    }


    @Override
    public List<StudentRespDTO> queryListByStudentName(String studentName){
        List<StudentEntity> entityList = repository.queryByStudentName(studentName);
        BeanTools.createListFrom(entityList, StudentRespDTO.class);
        List<StudentRespDTO>  listFrom = entityList.stream().map(this::convertRespDTO).collect(Collectors.toList());
        return listFrom;
    }

    @Override
    public List<StudentRespDTO> queryListByAdmissionDateAndStudentClass(String studentClass){
        List<StudentEntity> entityList = repository.queryListByAdmissionDateAndStudentClass(studentClass);
        List<StudentRespDTO>  listFrom = entityList.stream()
                .map(this::convertRespDTO)
                .collect(Collectors.toList());
        return listFrom;
    }

    @Override
    public Map<String,List<StudentRespDTO>> queryStudentGroupByClass(){
        Map<String, List<StudentEntity>> map = repository.groupByClass();

        Map<String,List<StudentRespDTO>> result = new HashMap<>(map.size());
        map.forEach((k,v)->{
            List<StudentRespDTO> listFrom = BeanTools.createListFrom(v, StudentRespDTO.class);
            result.put(k,listFrom);
        });
        return  result;
    }

    @Override
    public List<StudentRespDTO> queryByCondition(String studentName,LocalDate birthday,List<Integer> registerStatusList){
        List<StudentEntity> entityList = repository.queryByCondition(studentName, birthday, registerStatusList);
        return entityList.stream()
                .map(this::convertRespDTO)
                .collect(Collectors.toList());
    }


}
