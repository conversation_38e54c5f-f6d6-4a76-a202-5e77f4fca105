package cathayfuture.opm.app.student.service.query;

import cathayfuture.opm.client.student.api.query.ExpenseStandardQueryAppService;
import cathayfuture.opm.client.student.api.query.RateReliefQueryAppService;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.request.RateReliefPageReqDTO;
import cathayfuture.opm.client.student.dto.response.ExpenseStandardRespDTO;
import cathayfuture.opm.client.student.dto.response.RateReliefPageRespDTO;
import cathayfuture.opm.client.student.dto.response.RateReliefQueryStudentInfoByNameRespDto;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import cathayfuture.opm.domain.student.RateReliefEntity;
import cathayfuture.opm.domain.student.StudentEntity;
import cathayfuture.opm.domain.student.enums.ExpenseTypeEnum;
import cathayfuture.opm.domain.student.repository.RateReliefRepository;
import cathayfuture.opm.infra.common.basequery.wrapper.ExtQueryWrapper;
import cathayfuture.opm.infra.common.beantools.BeanTools;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cathayfuture.opm.infra.common.utils.BigDecimalUtil;
import cathayfuture.opm.infra.student.repository.mapper.RateReliefMapper;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dtyunxi.exceptions.BizException;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:  减免
 * @date 2023/3/31 10:05
 */
@Service
@Slf4j
public class RateReliefQueryAppServiceImpl implements RateReliefQueryAppService {

    private RateReliefRepository repository;

    private RateReliefMapper mapper;

    @Resource
    private StudentQueryAppService studentQueryAppService;

    @Resource
    private ExpenseStandardQueryAppService expenseStandardQueryAppService;

    public RateReliefQueryAppServiceImpl(RateReliefRepository repository, RateReliefMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    @Override
    public IPage<RateReliefPageRespDTO> page(RateReliefPageReqDTO reqDTO, IPage page){

        IPage<RateReliefEntity> pageInfo = mapper.selectPage(page, ExtQueryWrapper.newInstance(RateReliefEntity.class)
                .orderByDesc(LambdaUtils.column(RateReliefEntity::getCreateTime))
                .andChildCondition(
                        LambdaUtils.column(RateReliefEntity::getStudentId),
                        LambdaUtils.column(StudentEntity::getId),
                        ExtQueryWrapper.newInstance(StudentEntity.class)
                                .like(LambdaUtils.column(StudentEntity::getStudentName), reqDTO.getStudentName())
                                .like(LambdaUtils.column(StudentEntity::getStudentNo), reqDTO.getStudentNo())
                )
        );


        IPage<RateReliefPageRespDTO> result = BeanTools.createPageFrom(pageInfo, RateReliefPageRespDTO.class);

        translateStudentInfo(result.getRecords());

        translateEnum(result.getRecords());

        return result;
    }

    /**
     * 翻译学生信息
     * @param list
     */
    private void translateStudentInfo(List<RateReliefPageRespDTO> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        Set<Integer> setIds = list.stream()
                .map(RateReliefPageRespDTO::getStudentId)
                .collect(Collectors.toSet());
        List<StudentRespDTO> studentRespDTOS = studentQueryAppService.listByIds(new ArrayList<>(setIds));
        Map<Integer, StudentRespDTO> map = studentRespDTOS.stream()
                .collect(Collectors.toMap(StudentRespDTO::getId, Function.identity(), (o, n) -> n));

        for(RateReliefPageRespDTO dto :list){
            StudentRespDTO student = map.get(dto.getStudentId());

            if(Objects.nonNull(student)){
                dto.setStudentName(student.getStudentName());
                dto.setStudentNo(student.getStudentNo());
            }
        }
    }

    /**
     * 翻译枚举
     * @param list
     */
    private void translateEnum(List<RateReliefPageRespDTO> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        for (RateReliefPageRespDTO dto : list) {
            String desc = Optional.ofNullable(dto.getCostType())
                    .map(OrderTypeEnum::getEnumDescription)
                    .orElse(StringUtils.EMPTY);
            dto.setCostTypeDesc(desc);
        }
    }


    @Override
    public List<RateReliefQueryStudentInfoByNameRespDto> queryListByStudentName(String studentName){
        if(StringUtils.isBlank(studentName)){
            throw new BizException("请输入学生姓名，输入为空");
        }
        List<StudentRespDTO> studentRespDTOS = studentQueryAppService.queryListByStudentName(studentName);
        if(CollectionUtil.isEmpty(studentRespDTOS)){
            return new ArrayList<>();
        }

        List<Integer> studentIds = studentRespDTOS.stream()
                .map(StudentRespDTO::getId)
                .collect(Collectors.toList());
        List<ExpenseStandardRespDTO> ExpenseStandardRespDTOS = expenseStandardQueryAppService.findListByStudentIdsAndExpenseType(studentIds, ExpenseTypeEnum.CHILD_CARE.getKey());
        Map<Integer, BigDecimal> map = ExpenseStandardRespDTOS.stream()
                .collect(Collectors.toMap(ExpenseStandardRespDTO::getStudentId, ExpenseStandardRespDTO::getExpenseStandard));

        List<RateReliefQueryStudentInfoByNameRespDto> result = new ArrayList<>();

        for (StudentRespDTO data : studentRespDTOS) {
            BigDecimal expenseStandard = Optional.ofNullable(map.get(data.getId()))
                    .orElse(BigDecimal.ZERO);
            result.add(RateReliefQueryStudentInfoByNameRespDto
                    .build(data,expenseStandard)
            );
        }
        return result;
    }


    public Table<Integer,String,BigDecimal> queryTableByStudentIds(List<Integer> studentIds){
        List<RateReliefEntity> entities = repository.findListByIds(studentIds);
        Table<Integer,String,BigDecimal> table = HashBasedTable.create();
        if(CollectionUtil.isEmpty(entities)){
            return table;
        }

        for(RateReliefEntity data : entities){
            //如果又多条减免，合并
            if(Objects.nonNull(table.get(data.getStudentId(), data.getDerateMonth()))){
                BigDecimal decimal = table.get(data.getStudentId(), data.getDerateMonth());
                table.put(data.getStudentId(), data.getDerateMonth(), BigDecimalUtil.add(decimal,data.getDerateAmount()));
                continue;
            }
            table.put(data.getStudentId(), data.getDerateMonth(), data.getDerateAmount());
        }
        return table;
    }


    @Override
    public Map<Integer,BigDecimal> queryMapByStudentIdsAndMonth(List<Integer> studentIds,String month){
        Table<Integer, String, BigDecimal> table = queryTableByStudentIds(studentIds);
        Map<Integer,BigDecimal> map = new HashMap<>();
        for (Integer studentId : studentIds) {
            BigDecimal bigDecimal = Optional.ofNullable(table.get(studentId, month)).orElse(BigDecimal.ZERO);
            map.put(studentId,bigDecimal);
        }
        return map;
    }
}
