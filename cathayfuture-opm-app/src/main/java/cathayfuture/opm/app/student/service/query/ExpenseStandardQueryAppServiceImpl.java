package cathayfuture.opm.app.student.service.query;

import cathayfuture.opm.client.order.dto.response.PageRespDTO;
import cathayfuture.opm.client.student.api.query.ExpenseStandardQueryAppService;
import cathayfuture.opm.client.student.dto.request.ExpenseStandardReqDTO;
import cathayfuture.opm.client.student.dto.response.ExpenseStandardRespDTO;
import cathayfuture.opm.domain.attendance.ennums.CommonBooleanEnum;
import cathayfuture.opm.domain.student.ExpenseStandardEntity;
import cathayfuture.opm.domain.student.StudentEntity;
import cathayfuture.opm.domain.student.enums.ExpenseStandardTypeEnum;
import cathayfuture.opm.domain.student.enums.ExpenseTypeEnum;
import cathayfuture.opm.domain.student.repository.ExpenseStandardRepository;
import cathayfuture.opm.infra.common.ExtServiceContext;
import cathayfuture.opm.infra.student.repository.mapper.ExpenseStandardMapper;
import cathayfuture.opm.infra.student.repository.mapper.StudentMapper;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import icu.mhb.mybatisplus.plugln.base.service.impl.JoinServiceImpl;
import icu.mhb.mybatisplus.plugln.core.JoinLambdaWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ExpenseStandardQueryAppServiceImpl extends JoinServiceImpl<ExpenseStandardMapper, ExpenseStandardEntity> implements ExpenseStandardQueryAppService {

    @Resource
    private ExpenseStandardRepository expenseStandardRepository;

    private ExpenseStandardMapper expenseStandardMapper;
    private StudentMapper studentMapper;

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public ExpenseStandardQueryAppServiceImpl(ExpenseStandardMapper expenseStandardMapper, StudentMapper studentMapper) {
        this.expenseStandardMapper = expenseStandardMapper;
        this.studentMapper = studentMapper;
    }

    @Override
    public PageRespDTO<ExpenseStandardRespDTO> page(ExpenseStandardReqDTO expenseStandardReqDTO, int pageNum, int pageSize) {

        // 第一步：创建 wrapper（如果当前类已经继承了 JoinServiceImpl，可以直接调用 joinLambdaQueryWrapper 获取）
//        JoinLambdaWrapper<StudentEntity> wrapper = joinLambdaQueryWrapper(StudentEntity.class);
        JoinLambdaWrapper<StudentEntity> wrapper = new JoinLambdaWrapper<>(StudentEntity.class);

        // 第二步：设置主表：取哪些字段，查询条件有哪些
        wrapper.selectAs((cb)->{
            cb.add(StudentEntity::getId,"studentId")
                .add(StudentEntity::getStudentName)
                .add(StudentEntity::getStudentNo)
                .add(StudentEntity::getIdNumber)
                .add(StudentEntity::getGender)
                .add(StudentEntity::getAdmissionDate)
                .add(StudentEntity::getBusinessUnit);
        })
        .like(StringUtils.isNotBlank(expenseStandardReqDTO.getStudentName()), StudentEntity::getStudentName, expenseStandardReqDTO.getStudentName())
        .like(StringUtils.isNotBlank(expenseStandardReqDTO.getStudentNo()), StudentEntity::getStudentNo, expenseStandardReqDTO.getStudentNo());

        // 第三步：设置关联表：取哪些字段，查询条件有哪些

        // 本期仅做保育费标准类型查询
        wrapper.leftJoin(ExpenseStandardEntity.class, ExpenseStandardEntity::getStudentId, StudentEntity::getId)
                .eq(ExpenseStandardEntity::getExpenseType,ExpenseTypeEnum.CHILD_CARE.getKey())
                .eq(Objects.nonNull(expenseStandardReqDTO.getExpenseStandardType()),ExpenseStandardEntity::getExpenseStandardType,expenseStandardReqDTO.getExpenseStandardType())
                .select(ExpenseStandardEntity::getExpenseStandardType,ExpenseStandardEntity::getExpenseType,ExpenseStandardEntity::getExpenseStandard,ExpenseStandardEntity::getId,ExpenseStandardEntity::getRemark)
                .end();

        // 第四步：创建查询（注意：使用哪个 service，主表就是这个 service 所配置的表）
                Page<ExpenseStandardRespDTO> expenseStandardRespPage = studentMapper
                        .joinSelectPage(new Page<>(pageNum, pageSize),wrapper, ExpenseStandardRespDTO.class);

        PageRespDTO<ExpenseStandardRespDTO> result = new PageRespDTO<>();

        List<ExpenseStandardRespDTO> collect = expenseStandardRespPage.getRecords();
        translateExpenseStandardTypeStr(collect);
        result.setPageNum(expenseStandardRespPage.getCurrent())
                .setPageSize(expenseStandardRespPage.getSize())
                .setTotal(expenseStandardRespPage.getTotal())
                .setRecords(collect);
        return result;
    }

    public void translateExpenseStandardItem(ExpenseStandardRespDTO item){
        String expenseStandardTypedesc = ExpenseStandardTypeEnum.getEnumDescription(item.getExpenseStandardType());
        item.setExpenseStandardTypeStr(expenseStandardTypedesc);
        String expenseTypedesc = ExpenseTypeEnum.getEnumDescription(item.getExpenseType());
        item.setExpenseTypeStr(expenseTypedesc);
    }

    public void translateExpenseStandardTypeStr(List<ExpenseStandardRespDTO> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        for(ExpenseStandardRespDTO data :list){
            translateExpenseStandardItem(data);
        }
    }

    @Override
    public ExpenseStandardRespDTO getById(Integer expenseStandardId) {
        return joinQueryStuExpenseStandard(expenseStandardId);

    }
    private ExpenseStandardRespDTO convertRespDTO(Object source) {
        ExpenseStandardRespDTO respDTO = new ExpenseStandardRespDTO();
        BeanUtils.copyProperties(source, respDTO);
        return respDTO;
    }


    @Override
    public List<ExpenseStandardRespDTO> findListByStudentIdsAndExpenseType(List<Integer> studentId, Integer expenseType){
        List<ExpenseStandardEntity> entityList = expenseStandardRepository.findListByStudentIds(studentId);

        return entityList.stream()
                .filter(x -> Objects.equals(x.getExpenseType(), expenseType))
                .map(this::convertRespDTO)
                .collect(Collectors.toList());
    }

    @Override
    public ExpenseStandardRespDTO edit(ExpenseStandardReqDTO expenseStandardReqDTO){
        // 编辑信息
        ExpenseStandardEntity expenseStandardEntity = expenseStandardRepository.getById(expenseStandardReqDTO.getId());
        expenseStandardEntity.setExpenseStandardType(expenseStandardReqDTO.getExpenseStandardType());
        expenseStandardEntity.setExpenseStandard(expenseStandardReqDTO.getExpenseStandard());
        expenseStandardEntity.setUpdateTime(new Date());
        expenseStandardEntity.setUpdatePerson(ExtServiceContext.getUserCode());
        expenseStandardEntity.setRemark(expenseStandardReqDTO.getRemark());
        expenseStandardRepository.edit(expenseStandardEntity);
        return joinQueryStuExpenseStandard(expenseStandardReqDTO.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(ExpenseStandardReqDTO expenseStandardReqDTO){
        ExpenseStandardEntity expenseStandardEntity = new ExpenseStandardEntity();
        BeanUtils.copyProperties(expenseStandardReqDTO,expenseStandardEntity);
        expenseStandardEntity.setUpdateTime(new Date());
        expenseStandardEntity.setUpdatePerson(ExtServiceContext.getUserCode());
        expenseStandardEntity.setCreateTime(new Date());
        expenseStandardEntity.setCreatePerson(ExtServiceContext.getUserCode());
        expenseStandardEntity.setDr(CommonBooleanEnum.NO.getKey());
        expenseStandardRepository.add(expenseStandardEntity);
    }

    /**
     *
     * @param expenseStandardId 费用标准id
     * @return
     */
    public  ExpenseStandardRespDTO joinQueryStuExpenseStandard(Integer expenseStandardId){
        JoinLambdaWrapper<StudentEntity> wrapper = new JoinLambdaWrapper<>(StudentEntity.class);
        wrapper.selectAs((cb)->{
            cb.add(StudentEntity::getId,"studentId")
                    .add(StudentEntity::getStudentName)
                    .add(StudentEntity::getStudentNo)
                    .add(StudentEntity::getIdNumber)
                    .add(StudentEntity::getGender)
                    .add(StudentEntity::getAdmissionDate)
                    .add(StudentEntity::getBusinessUnit);
        });

        wrapper.leftJoin(ExpenseStandardEntity.class, ExpenseStandardEntity::getStudentId, StudentEntity::getId)
                .eq(Objects.nonNull(expenseStandardId),ExpenseStandardEntity::getId,expenseStandardId)
                .select(ExpenseStandardEntity::getExpenseStandardType,ExpenseStandardEntity::getExpenseType,ExpenseStandardEntity::getExpenseStandard,ExpenseStandardEntity::getId)
                .end();

        ExpenseStandardRespDTO expenseStandardResp = studentMapper
                .joinSelectOne(wrapper, ExpenseStandardRespDTO.class);
        translateExpenseStandardItem(expenseStandardResp);
        return expenseStandardResp;
    }

}
