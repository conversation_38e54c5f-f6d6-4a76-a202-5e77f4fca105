package cathayfuture.opm.app.student.service;

import cathayfuture.opm.client.student.api.ExpenseDefaultAppService;
import cathayfuture.opm.domain.student.ExpenseDefaultEntity;
import cathayfuture.opm.domain.student.repository.ExpenseDefaultRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 收费标默认值
 * @date 2023/4/11 14:18
 */

@Service
@Slf4j
public class ExpenseDefaultAppServiceImpl implements ExpenseDefaultAppService {

    private ExpenseDefaultRepository expenseDefaultRepository;

    public ExpenseDefaultAppServiceImpl(ExpenseDefaultRepository expenseDefaultRepository) {
        this.expenseDefaultRepository = expenseDefaultRepository;
    }

    @Override
    public void change(String value, Integer type,Integer id){
        ExpenseDefaultEntity entity = new ExpenseDefaultEntity();
//        entity.setType(OrderTypeEnum.CHILD_CARE.getKey());
        entity.setType(type);
        entity.setDefaultValue(new BigDecimal(value));
        entity.setId(id);
        expenseDefaultRepository.change(entity);
    }
}
