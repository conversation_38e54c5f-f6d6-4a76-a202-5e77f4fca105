package cathayfuture.opm.app.common;

import cathayfuture.opm.client.AppException;
import com.dtyunxi.huieryun.lock.api.ILockService;
import com.dtyunxi.huieryun.lock.api.Mutex;
import com.dtyunxi.lang.BusinessRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * redis lock
 *
 * <AUTHOR>
 * @since 2022-02-24
 */
@Slf4j
@Service
public class RedisLockService {
    @Resource
    private ILockService lockService;

    public Mutex lock(String tableName, String primaryKey) {
        Mutex mutex;
        try {
            mutex = lockService.lock(tableName, primaryKey, -1, 10, TimeUnit.SECONDS);
        } catch (BusinessRuntimeException e) {
            log.warn("RedisLockUtils lock error", e);
            throw new AppException();
        }
        return mutex;
    }
    public boolean unlock(Mutex mutex) {
        return lockService.unlock(mutex);
    }
}