package cathayfuture.opm.app.common;

import cathayfuture.opm.client.order.dto.request.ExcelUploadReqDTO;
import cathayfuture.opm.client.order.exception.ExcelUploadException;
import cathayfuture.opm.domain.order.enums.ExcelUploadTypeEnum;
import com.alibaba.fastjson.JSON;
import com.dtyunxi.huieryun.oss.api.IObjectStorageService;
import com.dtyunxi.huieryun.starter.objectstorage.OssProperties;
import com.taslyware.framework.exceltools.exception.ExcelImportException;
import com.taslyware.framework.exceltools.importer.Importer;
import com.taslyware.framework.exceltools.importer.vo.ImportResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.text.MessageFormat;
import java.util.Objects;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/30/22
 */
@Slf4j
@Service
public class ExcelUploadService {

    private static final String URI_SEPARATOR = "/";

    @Resource
    private OssProperties ossProperties;

    @Resource
    private IObjectStorageService objectStorageService;

    @Resource
    private Importer importer;

    public ImportResponse uploadByCode(HttpServletResponse response, String code, ExcelUploadReqDTO excelUploadReqDto) {
        log.info("导入文件请求开始："+excelUploadReqDto.getUri());
        ExcelUploadTypeEnum excelUploadTypeEnum = ExcelUploadTypeEnum.getByCode(code);
        if (Objects.isNull(excelUploadTypeEnum)) {
            throw new ExcelUploadException("400001");
        }
        if (StringUtils.lastIndexOf(excelUploadReqDto.getUri(), URI_SEPARATOR) >= 0) {
            excelUploadReqDto.setUri(StringUtils.substringAfterLast(excelUploadReqDto.getUri(), URI_SEPARATOR));
        }
        if (excelUploadTypeEnum.getAsync()) {
            // 异步处理
            throw new ExcelUploadException("400002");
        } else {
            // 同步处理
            String ossUrlPrefix = MessageFormat.format("/{0}/{1}", ossProperties.getBucketName(), ossProperties.getDir());
            String fileURI = ossUrlPrefix + excelUploadReqDto.getUri();
            OutputStream excelOutputStream = objectStorageService.get(fileURI);
            if (excelOutputStream==null) {
                log.info("导入文件获取oss文件为null："+fileURI);
                throw new ExcelUploadException("导入文件获取oss文件为null："+fileURI);
            }
            ByteArrayOutputStream byteArrayOutputStream = (ByteArrayOutputStream) excelOutputStream;
            ByteArrayInputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            try {
                ImportResponse importResponse = importer.importExcel(response, inputStream,
                        new ByteArrayInputStream(byteArrayOutputStream.toByteArray()), code,
                        JSON.toJSONString(excelUploadReqDto));
                log.info("导入文件请求结束："+excelUploadReqDto.getUri());
                return importResponse;
            }catch(ExcelImportException ei){
                log.error("导入文件异常", ei);
                throw new ExcelUploadException(ei.getMessage());
            }
            catch (Exception e) {
                log.error("导入文件异常", e);
                throw new ExcelUploadException(e.getMessage());
            }
        }
    }
}