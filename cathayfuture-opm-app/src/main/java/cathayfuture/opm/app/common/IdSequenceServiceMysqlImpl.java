package cathayfuture.opm.app.common;

import cathayfuture.opm.client.common.IdSequenceService;
import cathayfuture.opm.domain.order.gateway.repository.IdSequenceRepository;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2022/8/27
 */
@Service
@Primary
public class IdSequenceServiceMysqlImpl implements IdSequenceService {

    private IdSequenceRepository idSequenceRepository;

    public IdSequenceServiceMysqlImpl(IdSequenceRepository idSequenceRepository) {
        this.idSequenceRepository = idSequenceRepository;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer incrAndGetSequence(String prefix) {
        return this.incrAndGetSequence(prefix, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer incrAndGetSequence(String prefix, Integer increment) {
        if (increment == null) {
            increment = 1;
        }
        this.idSequenceRepository.incrSequence(prefix, increment);
        return this.idSequenceRepository.getSequence(prefix);
    }
}
