package cathayfuture.opm.app.attendance.service;

import cathayfuture.opm.client.action.api.ActionAppService;
import cathayfuture.opm.client.action.dto.request.ActionReqDTO;
import cathayfuture.opm.client.attendance.api.AttendanceRecordAppService;
import cathayfuture.opm.client.attendance.api.query.AskedForLeaveQueryAppService;
import cathayfuture.opm.client.attendance.api.query.AttendanceRecordQueryAppService;
import cathayfuture.opm.client.attendance.api.query.CalendarQueryAppService;
import cathayfuture.opm.client.attendance.dto.request.AttendanceRecordSaveReqDto;
import cathayfuture.opm.client.attendance.dto.response.AttendanceRecordDataIntegrityByClassRespDto;
import cathayfuture.opm.client.attendance.enums.SpecialDateTypeEnum;
import cathayfuture.opm.client.attendance.exception.AttendanceRecordSaveException;
import cathayfuture.opm.client.student.api.query.StudentClassQueryAppService;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import cathayfuture.opm.domain.action.enums.ActionCodeEnum;
import cathayfuture.opm.domain.action.enums.ModuleEnum;
import cathayfuture.opm.domain.attendance.AttendanceRecordEntity;
import cathayfuture.opm.domain.attendance.ennums.AttendanceRecordStatusEnum;
import cathayfuture.opm.domain.attendance.ennums.CommonBooleanEnum;
import cathayfuture.opm.domain.attendance.repository.AttendanceRecordRepository;
import cathayfuture.opm.infra.attendance.repository.mapper.AttendanceRecordMapper;
import cathayfuture.opm.infra.common.AopContextUtils;
import cathayfuture.opm.infra.common.beantools.BeanTools;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 考勤记录
 * @date 2023/3/31 17:15
 */
@Service
@Slf4j
public class AttendanceRecordAppServiceImpl implements AttendanceRecordAppService {

    @Resource
    private AskedForLeaveQueryAppService askedForLeaveQueryAppService;
    @Resource
    private ActionAppService actionAppService;
    @Resource
    private CalendarQueryAppService calendarQueryAppService;

    private AttendanceRecordAppServiceImpl getProxy() {
        return AopContextUtils.getProxy(this);
    }

    private AttendanceRecordRepository repository;

    private AttendanceRecordMapper mapper;

    public AttendanceRecordAppServiceImpl(AttendanceRecordRepository repository, AttendanceRecordMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save( List<AttendanceRecordSaveReqDto> reqDtos){
        log.info("AttendanceRecordAppService.save_入参：{}", JSONUtil.toJsonStr(reqDtos));
        for (AttendanceRecordSaveReqDto data : reqDtos) {
            if(Objects.isNull(data.getStudentId())){
                throw new AttendanceRecordSaveException("参数 studentId 不能为null");
            }
            if(StringUtils.isBlank(data.getCurrentDate())){
                throw new AttendanceRecordSaveException("参数 currentDate 不能为空或null");
            }
        }
        

        LocalDate parse = LocalDate.parse(reqDtos.stream().findFirst().orElse(new AttendanceRecordSaveReqDto()).getCurrentDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        //只能新增/修改本月和今后的日期，不能修改上个月的日期
        getProxy().check(parse);

        Set<Integer> collect = reqDtos.stream()
                .map(AttendanceRecordSaveReqDto::getStudentId)
                .collect(Collectors.toSet());
        List<AttendanceRecordEntity> exist = repository.exist(new ArrayList<>(collect), parse);

        Table<Integer, LocalDate, AttendanceRecordEntity> table = getProxy().convertTable(exist);

        List<AttendanceRecordEntity> listFrom = BeanTools.createListFrom(reqDtos, AttendanceRecordEntity.class);


        for(AttendanceRecordEntity entity : listFrom){

            entity.setAttendanceDate(parse);


            AttendanceRecordEntity oldEntity = table.get(entity.getStudentId(), parse);

            if(Objects.isNull(oldEntity)){
                //新增

                getProxy().fillField(entity,null);

                repository.add(entity);
                //记录操作流水
                List<ActionReqDTO> actionReqDTOS = actionAppService.collectDiff(entity,null, ActionCodeEnum.ADD.getKey(), ModuleEnum.ATTENDANCE_RECORD.getKey());
                actionAppService.addBatch(actionReqDTOS);
            }else {
                //修改
                entity.setId(oldEntity.getId());
                //寒暑假出勤标记，长期病假出勤标记，正常出勤标记
                getProxy().fillField(entity,oldEntity);

                repository.update(entity);
                //记录操作流水
                List<ActionReqDTO> actionReqDTOS = actionAppService.collectDiff(entity,oldEntity, ActionCodeEnum.EDIT.getKey(), ModuleEnum.ATTENDANCE_RECORD.getKey());
                actionAppService.addBatch(actionReqDTOS);
            }

        }
    }

    public void check(LocalDate date){
        LocalDate LateLastMonth = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        if(!date.isAfter(LateLastMonth)){
            throw new AttendanceRecordSaveException("不能修改考勤！只能新增/修改本月和今后的日期，不能修改上个月的日期");
        }
    }


    public AttendanceRecordEntity fillField(AttendanceRecordEntity entity,AttendanceRecordEntity old){

        //填充null值字段，前端传的值是null，使用old填充，不是null，采用前端的值
        if(Objects.isNull(old)){
            Integer attendanceStatus = Arrays.asList(entity.getAmAttendance(), entity.getNoonAttendance(), entity.getPmAttendance()).stream()
                    .filter(s -> Objects.nonNull(s)).findFirst().orElse(null);
            entity.setAmAttendance(attendanceStatus);
            entity.setNoonAttendance(attendanceStatus);
            entity.setPmAttendance(attendanceStatus);
        }else {
            entity.setAmAttendance(Objects.isNull(entity.getAmAttendance())?old.getAmAttendance():entity.getAmAttendance());
            entity.setNoonAttendance(Objects.isNull(entity.getNoonAttendance())?old.getNoonAttendance():entity.getNoonAttendance());
            entity.setPmAttendance(Objects.isNull(entity.getPmAttendance())?old.getPmAttendance():entity.getPmAttendance());
        }



        //获取长期病假标记
        Boolean islongterm = askedForLeaveQueryAppService.judgementLongTerm(entity.getStudentId(),null);
        //获取日历寒暑假
        Integer isHoliday = calendarQueryAppService.judgeSpecialDate(entity.getAttendanceDate(), SpecialDateTypeEnum.HOLIDAY);

       //判断当前天是否出勤: 有一个出勤当天即为出勤，三个都缺勤当前才为缺勤
        AttendanceRecordStatusEnum statusEnum = AttendanceRecordStatusEnum.computeAttendanceRecordStatus(entity.getAmAttendance(), entity.getNoonAttendance(), entity.getPmAttendance());
        //正常出勤
        entity.setNormalFlg(Objects.nonNull(statusEnum) ? statusEnum.getKey() : null);
        //长期病假
        entity.setLongTermFlg(islongterm ? CommonBooleanEnum.YES.getKey() : CommonBooleanEnum.NO.getKey());
        //寒暑假
        entity.setHolidayFlg(isHoliday);

        return entity;
    }

    public Table<Integer,LocalDate,AttendanceRecordEntity> convertTable(List<AttendanceRecordEntity> entityList){
        Table<Integer,LocalDate,AttendanceRecordEntity> table = HashBasedTable.create();
        for (AttendanceRecordEntity data : entityList) {
            table.put(data.getStudentId(),data.getAttendanceDate(),data);
        }
        return table;

    }

    @Override
    public Integer initData(String idsStr, String dateListStr){
        List<Integer> list = new ArrayList<>();
        List<LocalDate> dateList = new ArrayList<>();
        if(StringUtils.isNotBlank(idsStr)){
            list = Arrays.stream(idsStr.split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        if(StringUtils.isNotBlank(dateListStr)){
            dateList =Arrays.stream(dateListStr.split(","))
                    .map(String::trim)
                    .map(this::parseBYStr)
                    .collect(Collectors.toList());
        }

        List<AttendanceRecordEntity> entities = new ArrayList<>();
        for(LocalDate data :dateList){
            for(Integer studentId :list){
                AttendanceRecordEntity entity = new AttendanceRecordEntity();
                entity.setAmAttendance(CommonBooleanEnum.YES.getKey());
                entity.setNoonAttendance(CommonBooleanEnum.YES.getKey());
                entity.setPmAttendance(CommonBooleanEnum.YES.getKey());

                entity.setHolidayFlg(CommonBooleanEnum.NO.getKey());
                entity.setLongTermFlg(CommonBooleanEnum.NO.getKey());
                entity.setNormalFlg(CommonBooleanEnum.YES.getKey());

                entity.setStudentId(studentId);
                entity.setAttendanceDate(data);
                entity.setDr(CommonBooleanEnum.NO.getKey());
                entities.add(entity);
            }
        }
        return repository.batchInsert(entities);
    }

    private LocalDate parseBYStr(String str){
        return LocalDate.parse(str,DateTimeFormatter.ISO_DATE);
    }




}
