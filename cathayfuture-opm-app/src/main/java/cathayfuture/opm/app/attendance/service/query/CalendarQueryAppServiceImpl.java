package cathayfuture.opm.app.attendance.service.query;

import cathayfuture.opm.client.attendance.api.query.CalendarQueryAppService;
import cathayfuture.opm.client.attendance.enums.SpecialDateTypeEnum;
import cathayfuture.opm.domain.attendance.CalendarEntity;
import cathayfuture.opm.domain.attendance.ennums.CommonBooleanEnum;
import cathayfuture.opm.domain.attendance.repository.CalendarRepository;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 日历
 * @date 2023/4/7 15:27
 */
@Service
@Slf4j
public class CalendarQueryAppServiceImpl implements CalendarQueryAppService {

    private CalendarRepository calendarRepository;

    public CalendarQueryAppServiceImpl(CalendarRepository calendarRepository) {
        this.calendarRepository = calendarRepository;
    }

    private static final Map<SpecialDateTypeEnum, String> map = new HashMap<>();

    static {
        map.put(SpecialDateTypeEnum.CLOSE, LambdaUtils.property(CalendarEntity::getClose));
        map.put(SpecialDateTypeEnum.HOLIDAY, LambdaUtils.property(CalendarEntity::getHoliday));
        map.put(SpecialDateTypeEnum.WEEKEND, LambdaUtils.property(CalendarEntity::getWeekend));
    }

    @Override
    public Integer judgeSpecialDate(LocalDate date, SpecialDateTypeEnum typeEnum){
        CalendarEntity oneByDate = calendarRepository.findOneByDate(date);
        Integer value = (Integer) ReflectUtil.getFieldValue(oneByDate, map.get(typeEnum));
        return value;
    }

    /**
     * 去掉法定假日的寒暑假
     * @return
     */
    @Override
    public long countWinterVacationAndSummerVacationDaysWithoutWeekend(LocalDate date){
        List<CalendarEntity> days = calendarRepository.getTargetMonthDays(date);
        long count = days.stream()
                .filter(x -> Objects.equals(x.getHoliday(), CommonBooleanEnum.YES.getKey()))
                .filter(y -> Objects.equals(y.getWeekend(), CommonBooleanEnum.NO.getKey()))
                .count();
        return count;
    }

    /**
     * 统计工作日
     * @param date
     * @return
     */
    @Override
    public long countWeekDays(LocalDate date){
        List<CalendarEntity> days = calendarRepository.getTargetMonthDays(date);
        long count = days.stream()
                .filter(x -> Objects.equals(x.getHoliday(), CommonBooleanEnum.NO.getKey()))
                .filter(y -> Objects.equals(y.getWeekend(), CommonBooleanEnum.NO.getKey()))
                .filter(z -> Objects.equals(z.getWeekday(), CommonBooleanEnum.YES.getKey()))
                .count();
        return count;
    }

    /**
     * 统计工作日去除闭园
     * @param date
     * @return
     */
    @Override
    public long countWorkDaysWithoutColse(LocalDate date){
        List<CalendarEntity> days = calendarRepository.getTargetMonthDays(date);
        long count = days.stream()
                .filter(x -> Objects.equals(x.getHoliday(), CommonBooleanEnum.NO.getKey()))
                .filter(y -> Objects.equals(y.getWeekend(), CommonBooleanEnum.NO.getKey()))
                .filter(z -> Objects.equals(z.getClose(), CommonBooleanEnum.NO.getKey()))
                .filter(k -> Objects.equals(k.getWeekday(), CommonBooleanEnum.YES.getKey()))
                .count();
        return count;
    }

    /**
     * 统计月天数，去除周末和法定节假日
     * @param date
     * @return
     */
    @Override
    public long countWithoutWeekend(LocalDate date){
        List<CalendarEntity> days = calendarRepository.getTargetMonthDays(date);
        long count = days.stream()
                .filter(y -> Objects.equals(y.getWeekend(), CommonBooleanEnum.NO.getKey()))
                .count();
        return count;
    }

    /**
     * 统计月天数，包括寒暑假+开园日，过滤掉周末和闭园日
     * @param date
     * @return
     */
    @Override
    public List<LocalDate> countWithoutWeekendAndWithoutClose(LocalDate date){

        LocalDate first = date.with(TemporalAdjusters.firstDayOfMonth());
        List<CalendarEntity> days = calendarRepository.getTargetMonthDays(first,date);
        List<LocalDate> collect = days.stream()
                .filter(y -> Objects.equals(y.getWeekend(), CommonBooleanEnum.NO.getKey()))
                .filter(x -> Objects.equals(x.getClose(), CommonBooleanEnum.NO.getKey()))
                .map(CalendarEntity::getDate)
                .collect(Collectors.toList());
        return collect;
    }

    @Override
    public List<LocalDate> countWeekDay(LocalDate date){
        LocalDate first = date.with(TemporalAdjusters.firstDayOfMonth());
        List<CalendarEntity> days = calendarRepository.getTargetMonthDays(first,date);
        List<LocalDate> collect = days.stream()
                .filter(y -> Objects.equals(y.getWeekday(), CommonBooleanEnum.YES.getKey()))
                .map(CalendarEntity::getDate)
                .collect(Collectors.toList());
        return collect;
    }



    @Override
    public List<Integer> getDayOfMonthList(LocalDate date){
        List<CalendarEntity> days = calendarRepository.getTargetMonthDays(date);
        List<Integer> dayOfMonthList = days.stream()
                .filter(x -> Objects.equals(x.getHoliday(), CommonBooleanEnum.NO.getKey()))
                .filter(y -> Objects.equals(y.getWeekend(), CommonBooleanEnum.NO.getKey()))
                .filter(z -> Objects.equals(z.getClose(), CommonBooleanEnum.NO.getKey()))
                .filter(k -> Objects.equals(k.getWeekday(), CommonBooleanEnum.YES.getKey()))
                .map(item -> {
                    return item.getDate().getDayOfMonth();
                })
                .sorted()
                .collect(Collectors.toList());
        return dayOfMonthList;
    }

    @Override
    public List<Integer> getDayOfMonthHolidayList(LocalDate date){
        List<CalendarEntity> days = calendarRepository.getTargetMonthDays(date);
        List<Integer> dayOfMonthList = days.stream()
                .filter(x -> Objects.equals(x.getHoliday(), CommonBooleanEnum.YES.getKey()))
                .filter(y -> Objects.equals(y.getWeekend(), CommonBooleanEnum.NO.getKey()))
                .filter(z -> Objects.equals(z.getClose(), CommonBooleanEnum.NO.getKey()))
                .filter(k -> Objects.equals(k.getWeekday(), CommonBooleanEnum.NO.getKey()))
                .map(item -> {
                    return item.getDate().getDayOfMonth();
                })
                .sorted()
                .collect(Collectors.toList());
        return dayOfMonthList;
    }


    @Override
    public String queryCurrentVersion(LocalDate targetDate){
        return calendarRepository.queryVersion(targetDate);
    }

}
