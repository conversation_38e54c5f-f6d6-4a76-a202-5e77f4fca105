package cathayfuture.opm.app.attendance.service.query;

import cathayfuture.opm.client.attendance.api.query.AskedForLeaveQueryAppService;
import cathayfuture.opm.client.attendance.dto.request.AskedForLeaveReqDto;
import cathayfuture.opm.client.attendance.dto.response.AskedForLeaveRespDto;
import cathayfuture.opm.client.attendance.dto.response.StudentInfoAndLongTermRespDTO;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import cathayfuture.opm.domain.attendance.AskedForLeaveEntity;
import cathayfuture.opm.domain.attendance.ennums.AskedForLeaveStatusEnum;
import cathayfuture.opm.domain.attendance.ennums.AskedForLeaveTypeEnum;
import cathayfuture.opm.domain.attendance.repository.AskedForLeaveRepository;
import cathayfuture.opm.domain.student.StudentEntity;
import cathayfuture.opm.domain.student.enums.GenderEnum;
import cathayfuture.opm.infra.attendance.repository.mapper.AskedForLeaveMapper;
import cathayfuture.opm.infra.common.basequery.wrapper.ExtQueryWrapper;
import cathayfuture.opm.infra.common.beantools.BeanTools;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dtyunxi.exceptions.BizException;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.function.IntPredicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 请假查询
 * @date 2023/3/27 15:05
 */
@Service
@Slf4j
public class AskedForLeaveQueryAppServiceImpl implements AskedForLeaveQueryAppService {

    @Resource
    private StudentQueryAppService studentQueryAppService;


    private AskedForLeaveRepository askedForLeaveRepository;

    private AskedForLeaveMapper askedForLeaveMapper;

    private final String FORMATE_DAY = "yyyy-MM-dd";
    private final String FORMATE_MONTH = "yyyy-MM";


    public AskedForLeaveQueryAppServiceImpl(AskedForLeaveRepository askedForLeaveRepository, AskedForLeaveMapper askedForLeaveMapper) {
        this.askedForLeaveRepository = askedForLeaveRepository;
        this.askedForLeaveMapper = askedForLeaveMapper;
    }

    private static final Map<AskedForLeaveStatusEnum,ExtQueryWrapper<AskedForLeaveEntity>> sqlMap = new HashMap<>();

    static {
        sqlMap.put(AskedForLeaveStatusEnum.NOT_STARTED,
                ExtQueryWrapper.newInstance(AskedForLeaveEntity.class)
                        .gt(LambdaUtils.column(AskedForLeaveEntity::getRealStartDate), LocalDate.now())
                        .isNull(LambdaUtils.column(AskedForLeaveEntity::getReturnStartDate))
                        .isNull(LambdaUtils.column(AskedForLeaveEntity::getReturnEndDate))
        );

        sqlMap.put(AskedForLeaveStatusEnum.ON_LEAVE,
                ExtQueryWrapper.newInstance(AskedForLeaveEntity.class)
                        .le(LambdaUtils.column(AskedForLeaveEntity::getRealStartDate), LocalDate.now())
                        .ge(LambdaUtils.column(AskedForLeaveEntity::getRealEndDate), LocalDate.now())
                        .isNull(LambdaUtils.column(AskedForLeaveEntity::getReturnStartDate))
                        .isNull(LambdaUtils.column(AskedForLeaveEntity::getReturnEndDate))
        );

        sqlMap.put(AskedForLeaveStatusEnum.FINISHED,
                ExtQueryWrapper.newInstance(AskedForLeaveEntity.class)
                        .lt(LambdaUtils.column(AskedForLeaveEntity::getRealEndDate), LocalDate.now())
                        .isNull(LambdaUtils.column(AskedForLeaveEntity::getReturnStartDate))
                        .isNull(LambdaUtils.column(AskedForLeaveEntity::getReturnEndDate))
        );

        sqlMap.put(AskedForLeaveStatusEnum.RETURNED,
                ExtQueryWrapper.newInstance(AskedForLeaveEntity.class)
                        .isNotNull(LambdaUtils.column(AskedForLeaveEntity::getReturnStartDate))
                        .isNotNull(LambdaUtils.column(AskedForLeaveEntity::getReturnEndDate))
        );

        sqlMap.put(AskedForLeaveStatusEnum.ALL,
                ExtQueryWrapper.newInstance(AskedForLeaveEntity.class));
    }


    @Override
    public IPage<AskedForLeaveRespDto> page(AskedForLeaveReqDto reqDto, IPage page) {

        LocalDate begin = null;
        LocalDate end = null;
        if(StringUtils.isNotBlank(reqDto.getDateStr())){
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(FORMATE_DAY);
            LocalDate localDate = LocalDate.parse(reqDto.getDateStr()+"-01",dateTimeFormatter);
            begin = localDate.with(TemporalAdjusters.firstDayOfMonth());
            end = localDate.with(TemporalAdjusters.lastDayOfMonth());
        }

        LocalDate finalBegin = begin;
        LocalDate finalEnd = end;
        AskedForLeaveStatusEnum enumByKey = AskedForLeaveStatusEnum.getEnumByKey(reqDto.getStatus());
        ExtQueryWrapper<AskedForLeaveEntity> query = sqlMap.get(enumByKey).cloneNewWrapper();

        IPage<AskedForLeaveEntity> pageInfo = askedForLeaveMapper.selectPage(page,query
                        .andExt(StringUtils.isNotBlank(reqDto.getDateStr()),
                                t->t
                        .between(LambdaUtils.column(AskedForLeaveEntity::getStartDate), finalBegin, finalEnd)
                                .or()
                        .between(LambdaUtils.column(AskedForLeaveEntity::getEndDate), finalBegin, finalEnd)
                                .or()
                        .le(LambdaUtils.column(AskedForLeaveEntity::getStartDate), finalBegin).ge(LambdaUtils.column(AskedForLeaveEntity::getEndDate), finalEnd)
                        )
                        .orderByDesc(LambdaUtils.column(AskedForLeaveEntity::getCreateTime))
                .andChildCondition(
                        LambdaUtils.column(AskedForLeaveEntity::getStuId),
                        LambdaUtils.column(StudentEntity::getId),
                        ExtQueryWrapper.newInstance(StudentEntity.class)
                                .like(LambdaUtils.column(StudentEntity::getStudentName), reqDto.getStudentName())
                                .like(LambdaUtils.column(StudentEntity::getStudentNo), reqDto.getStudentNo())
                )
        );

        IPage<AskedForLeaveRespDto> result = BeanTools.createPageFrom(pageInfo, AskedForLeaveRespDto.class);

        translateStudentInfo(result.getRecords());

        translateEnum(result.getRecords());

        translateDate(result.getRecords());

        return result;
    }

    /**
     * 翻译学生信息
     * @param list
     */
    private void translateStudentInfo(List<AskedForLeaveRespDto> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        Set<Integer> setIds = list.stream()
                .map(AskedForLeaveRespDto::getStuId)
                .collect(Collectors.toSet());
        List<StudentRespDTO> studentRespDTOS = studentQueryAppService.listByIds(new ArrayList<>(setIds));
        Map<Integer, StudentRespDTO> map = studentRespDTOS.stream()
                .collect(Collectors.toMap(StudentRespDTO::getId, Function.identity(), (o, n) -> n));

        for(AskedForLeaveRespDto dto :list){
            StudentRespDTO student = map.get(dto.getStuId());

            if(Objects.nonNull(student)){
                dto.setStudentName(student.getStudentName());
                dto.setStudentNo(student.getStudentNo());
            }
        }
    }

    /**
     * 翻译枚举
     * @param list
     */
    private void translateEnum(List<AskedForLeaveRespDto> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }

        for (AskedForLeaveRespDto dto : list) {
            AskedForLeaveStatusEnum statusEnum = AskedForLeaveStatusEnum.getEnumByCurrentTime(dto.getRealStartDate(), dto.getRealEndDate(),dto.getReturnStartDate());
            dto.setStatus(statusEnum.getKey());
            dto.setStatusDesc(statusEnum.getDescription());

            dto.setTypeDesc(AskedForLeaveTypeEnum.getEnumDescription(dto.getType()));
        }
    }

    /**
     * 翻译日期相关
     * @param list
     */
    private void translateDate(List<AskedForLeaveRespDto> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        for (AskedForLeaveRespDto dto : list) {
            dto.setStartDateStr(LocalDateToStr(dto.getStartDate(),FORMATE_MONTH));
            dto.setEndDateStr(LocalDateToStr(dto.getEndDate(),FORMATE_MONTH));
            dto.setRealStartDateStr(LocalDateToStr(dto.getRealStartDate(),FORMATE_DAY));
            dto.setRealEndDateStr(LocalDateToStr(dto.getRealEndDate(),FORMATE_DAY));
        }
    }

    private String LocalDateToStr(LocalDate date,String formate){
        if(Objects.isNull(date)){
            return StringUtils.EMPTY;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(formate);
        return date.format(df);
    }

    @Override
    public Boolean judgementLongTerm(Integer stuId,LocalDate localDate){
        if(Objects.isNull(stuId)){
            log.error("AskedForLeaveQueryAppService.judgementLongTerm 入参异常stuId:{}",stuId);
            throw new BizException("AskedForLeaveQueryAppService.judgementLongTerm 入参异常stuId 为null");
        }
        List<AskedForLeaveEntity> entityList = askedForLeaveRepository.findListByStuId(stuId);

        Boolean result = Boolean.FALSE;

        if(CollectionUtil.isEmpty(entityList)){
            return result;
        }

        LocalDate targetDate = LocalDate.now();
        if (Objects.nonNull(localDate)) {
            targetDate = localDate;
        }


        for(AskedForLeaveEntity data : entityList){
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String startDateStr = data.getRealStartDate().format(df);
            String endDateStr = data.getRealEndDate().format(df);

            String date = targetDate.getYear()+"-"+String.format("%02d",LocalDate.now().getMonthValue());

            //1，当前时间处于开始和结束之间，则为长期
            boolean after = targetDate.isAfter(data.getRealEndDate());
            boolean before = targetDate.isBefore(data.getRealStartDate());
            if((!after) && (!before)){
                result = Boolean.TRUE;
                break;
            }

            //2，当前时间月份有任意一天处于开始和结束之间，也为长期
            List<String> betweenMonth = getBetweenMonth(startDateStr, endDateStr);
            if(betweenMonth.contains(date)){
                result = Boolean.TRUE;
                break;
            }
        }
        return result;
    }

    @Override
    public Map<Integer,Boolean> judgementLongTermByStudentIds(final List<Integer> stuIds, LocalDate localDate){
        if(CollectionUtil.isEmpty(stuIds)){
            log.error("AskedForLeaveQueryAppService.judgementLongTermByStudentIds 入参异常stuId:{}",stuIds);
            throw new BizException("AskedForLeaveQueryAppService.judgementLongTermByStudentIds 入参异常 stuIds 为空");
        }
        List<AskedForLeaveEntity> entityList = askedForLeaveRepository.findListByStuIds(stuIds);
        List<Integer> studentIds = entityList.stream()
                .map(AskedForLeaveEntity::getStuId)
                .collect(Collectors.toList());

        Map<Integer,Boolean> result = new HashMap<>();

        Multimap<Integer, Boolean> multimap = ArrayListMultimap.create();


        if(CollectionUtil.isEmpty(entityList)){
            return new HashMap<>(0);
        }

        LocalDate targetDate = LocalDate.now();
        if (Objects.nonNull(localDate)) {
            targetDate = localDate;
        }

//        MultiValueMap<String,String> map=new LinkedMultiValueMap();


        for(AskedForLeaveEntity data : entityList){

            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String startDateStr = data.getRealStartDate().format(df);
            String endDateStr = data.getRealEndDate().format(df);

            String date = targetDate.getYear()+"-"+String.format("%02d",localDate.getMonthValue());

            //1，当前时间处于开始和结束之间，则为长期
            boolean after = targetDate.isAfter(data.getRealEndDate());
            boolean before = targetDate.isBefore(data.getRealStartDate());
            if((!after) && (!before)){
                multimap.put(data.getStuId(),Boolean.TRUE);
                continue;
            }
            //2，当前时间月份有任意一天处于开始和结束之间，也为长期
            List<String> betweenMonth = getBetweenMonth(startDateStr, endDateStr);
            if(betweenMonth.contains(date)){
                multimap.put(data.getStuId(),Boolean.TRUE);
                continue;
            }
            multimap.put(data.getStuId(),Boolean.FALSE);
        }

        for (Integer key : multimap.keySet()) {
            result.put(key,multimap.get(key).stream().anyMatch(x->Objects.equals(x,Boolean.TRUE)));
        }

        //        Map<Integer, Collection<Boolean>> map = multimap.asMap();
//        for (Map.Entry<Integer, Collection<Boolean>> entry : map.entrySet()) {
//            result.put(entry.getKey(), entry.getValue().stream().anyMatch(x->Objects.equals(x,Boolean.TRUE)));
//        }
        return result;
    }

    /**
     * 获取两个时间之间的月份
     * @param minDate
     * @param maxDate
     * @return
     */
    private List<String> getBetweenMonth(String minDate,String maxDate){
        List<String> list = new ArrayList<>();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();

        try {
            min.setTime(sdf.parse(minDate));
            min.set(min.get(Calendar.YEAR),min.get(Calendar.MONTH),1);

            max.setTime(sdf.parse(maxDate));
            max.set(max.get(Calendar.YEAR),max.get(Calendar.MONTH),2);

            Calendar current = min;
            while (current.before(max)){
                list.add(sdf.format(current.getTime()));
                current.add(Calendar.MONTH,1);
            }


        } catch (ParseException e) {
            e.printStackTrace();
        }
        return list;
    }
}
