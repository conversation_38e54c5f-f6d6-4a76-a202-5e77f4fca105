package cathayfuture.opm.app.attendance.service;

import cathayfuture.opm.client.action.api.ActionAppService;
import cathayfuture.opm.client.action.dto.request.ActionReqDTO;
import cathayfuture.opm.client.attendance.api.AskedForLeaveAppService;
import cathayfuture.opm.client.attendance.dto.request.AskedForLeaveSaveReqDto;
import cathayfuture.opm.client.attendance.dto.request.ReturnOperateReqDto;
import cathayfuture.opm.client.attendance.exception.AskedForLeaveCheckException;
import cathayfuture.opm.client.attendance.exception.AskedForLeaveDeleteException;
import cathayfuture.opm.client.attendance.exception.AskedForLeaveNotExistException;
import cathayfuture.opm.domain.action.enums.ActionCodeEnum;
import cathayfuture.opm.domain.action.enums.ModuleEnum;
import cathayfuture.opm.domain.attendance.AskedForLeaveEntity;
import cathayfuture.opm.domain.attendance.ennums.AskedForLeaveStatusEnum;
import cathayfuture.opm.domain.attendance.ennums.AskedForLeaveTypeEnum;
import cathayfuture.opm.domain.attendance.repository.AskedForLeaveRepository;
import cathayfuture.opm.infra.attendance.repository.mapper.AskedForLeaveMapper;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 请假service
 * @date 2023/3/27 15:05
 */
@Service
@Slf4j
public class AskedForLeaveAppServiceImpl implements AskedForLeaveAppService {

    private AskedForLeaveRepository askedForLeaveRepository;

    private AskedForLeaveMapper askedForLeaveMapper;

    @Resource
    private ActionAppService actionAppService;

    private final DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public AskedForLeaveAppServiceImpl(AskedForLeaveRepository askedForLeaveRepository, AskedForLeaveMapper askedForLeaveMapper) {
        this.askedForLeaveRepository = askedForLeaveRepository;
        this.askedForLeaveMapper = askedForLeaveMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AskedForLeaveSaveReqDto reqDto){
        log.info("新增请假操作入参：AskedForLeaveSaveReqDto：{}",JSONUtil.toJsonStr(reqDto));
        //结束时间要大于开始时间
        LocalDate startMonth = LocalDate.parse(reqDto.getStartDateStr()+"-01",DF);
        LocalDate endMonth = LocalDate.parse(reqDto.getEndDateStr()+"-01",DF);

        LocalDate startDate  = startMonth.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate endDate = endMonth.with(TemporalAdjusters.lastDayOfMonth());

        if(startDate.isAfter(endDate)){
            log.error("结束时间要大于开始时间: reqDto:{}",JSONUtil.toJsonStr(reqDto));
            throw new AskedForLeaveCheckException("结束时间要大于开始时间");
        }
        //当前学生入参时间段已经有请假信息了，不允许再请假
        List<AskedForLeaveEntity> listByStuId = askedForLeaveRepository.findListByStuId(reqDto.getStudentId());
        for(AskedForLeaveEntity data :listByStuId){
            boolean boo = startDate.isAfter(data.getRealStartDate())  && endDate.isAfter(data.getRealEndDate());
            if(!boo){
                throw new AskedForLeaveCheckException("当前学生所填的请假时间段已经存在请假信息，请重新填写");
            }
        }

        AskedForLeaveEntity entity = new AskedForLeaveEntity();
        entity.setStuId(reqDto.getStudentId());
        entity.setStartDate(startDate);
        entity.setEndDate(endDate);
        //默认都是长期
        entity.setType(AskedForLeaveTypeEnum.long_term.getKey());
        entity.setRealStartDate(startDate);
        entity.setRealEndDate(endDate);
        Boolean add = askedForLeaveRepository.add(entity);
        //写入操作流水表
        List<ActionReqDTO> actionReqDTOS = actionAppService.collectDiff(entity,null, ActionCodeEnum.ADD.getKey(), ModuleEnum.ASKED_FOR_LEAVE.getKey());
        actionAppService.addBatch(actionReqDTOS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Integer id){
        log.info("请假删除操作入参：id{}",id);
        //判断请假状态，只有未开始才能删除
        AskedForLeaveEntity entity = askedForLeaveRepository.getByid(id);
        AskedForLeaveEntity oldData= ObjectUtil.cloneByStream(entity);

        if(Objects.isNull(entity)){
            log.error("当前id：{}对应 数据不存在",id);
            throw new AskedForLeaveNotExistException("当前id：{"+id+"}对应 数据不存在");
        }
        AskedForLeaveStatusEnum statusEnum = AskedForLeaveStatusEnum.getEnumByCurrentTime(entity.getRealStartDate(), entity.getRealEndDate(), entity.getReturnStartDate());
        if(!Objects.equals(AskedForLeaveStatusEnum.NOT_STARTED,statusEnum)){
            log.error("只有请假状态为 未开始 状态才可以删除: AskedForLeaveEntity:{},当前时间：{}",JSONUtil.toJsonStr(entity),JSONUtil.toJsonStr(LocalDate.now()));
            throw new AskedForLeaveDeleteException("只有请假状态为 未开始 状态才可以删除");
        }
        Boolean delete = askedForLeaveRepository.delete(id);
        //写入操作流水表
        List<ActionReqDTO> actionReqDTOS = actionAppService.collectDiff(entity,null, ActionCodeEnum.DELETE.getKey(), ModuleEnum.ASKED_FOR_LEAVE.getKey());
        actionAppService.addBatch(actionReqDTOS);
        return delete;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean returnOperate(ReturnOperateReqDto reqDto){
        log.info("销假操作入参：reqDto:{}", JSONUtil.toJsonStr(reqDto));

        AskedForLeaveEntity entity = askedForLeaveRepository.getByid(reqDto.getId());
        AskedForLeaveEntity oldData= ObjectUtil.cloneByStream(entity);

        if(Objects.isNull(entity)){
            log.error("当前id：{}对应 数据不存在",reqDto.getId());
            throw new AskedForLeaveNotExistException("当前id：{"+reqDto.getId()+"}对应 数据不存在");
        }
        //结束时间要大于开始时间
        LocalDate returnStartDate = entity.getStartDate();
        LocalDate returnEndDate = LocalDate.parse(reqDto.getReturnEndDateStr(), DF);
        if(returnStartDate.isAfter(returnEndDate)){
            log.error("结束时间要大于开始时间: reqDto:{}",JSONUtil.toJsonStr(reqDto));
            throw new AskedForLeaveCheckException("结束时间要大于开始时间");
        }
        //销假的范围要在请假范围内
        boolean before = returnStartDate.isBefore(entity.getStartDate());
        boolean after = returnEndDate.isAfter(entity.getEndDate());
        if(before || after){
            log.error("销假的范围要在请假范围内 ReturnOperateReqDto:{},entity:{}",JSONUtil.toJsonStr(reqDto),JSONUtil.toJsonStr(entity));
            throw new AskedForLeaveCheckException("销假的范围要在请假范围内");
        }
        //销假的结束时间要大于当前时间
        if(returnEndDate.isBefore(LocalDate.now())){
            log.error("销假的结束时间要大于当前时间 ReturnOperateReqDto:{},entity:{}",JSONUtil.toJsonStr(reqDto),JSONUtil.toJsonStr(entity));
            throw new AskedForLeaveCheckException("销假的结束时间要大于当前时间");
        }

        //只有请假状态为 请假中 状态才可以销假
        AskedForLeaveStatusEnum statusEnum = AskedForLeaveStatusEnum.getEnumByCurrentTime(entity.getStartDate(), entity.getEndDate(), entity.getReturnStartDate());
        if(!Objects.equals(AskedForLeaveStatusEnum.ON_LEAVE,statusEnum)){
            log.error("只有请假状态为 请假中 状态才可以销假: AskedForLeaveEntity:{},当前时间：{}",JSONUtil.toJsonStr(entity),JSONUtil.toJsonStr(LocalDate.now()));
            throw new AskedForLeaveDeleteException("只有请假状态为 请假中 状态才可以销假");
        }

        AskedForLeaveEntity update = new AskedForLeaveEntity();
        update.setId(reqDto.getId());
        update.setReturnStartDate(returnStartDate);
        update.setReturnEndDate(returnEndDate);
        //销假结束时间作为实际请假时间的start
        update.setRealEndDate(returnEndDate);
        Boolean boo = askedForLeaveRepository.updateById(update);
        //写入操作流水表
        List<ActionReqDTO> actionReqDTOS = actionAppService.collectDiff(entity,oldData, ActionCodeEnum.RETURN_OPERATE.getKey(), ModuleEnum.ASKED_FOR_LEAVE.getKey());
        actionAppService.addBatch(actionReqDTOS);
        return boo;
    }

}
