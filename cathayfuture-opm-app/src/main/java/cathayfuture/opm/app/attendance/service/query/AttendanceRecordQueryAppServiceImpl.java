package cathayfuture.opm.app.attendance.service.query;

import cathayfuture.opm.app.attendance.service.AttendanceRecordAppServiceImpl;
import cathayfuture.opm.client.attendance.api.query.AskedForLeaveQueryAppService;
import cathayfuture.opm.client.attendance.api.query.AttendanceRecordQueryAppService;
import cathayfuture.opm.client.attendance.api.query.CalendarQueryAppService;
import cathayfuture.opm.client.attendance.dto.request.AttendanceRecordPageReqDto;
import cathayfuture.opm.client.attendance.dto.response.AttendanceRecordDataIntegrityByClassRespDto;
import cathayfuture.opm.client.attendance.dto.response.AttendanceRecordPageRespDTO;
import cathayfuture.opm.client.attendance.dto.response.StudentInfoAndLongTermRespDTO;
import cathayfuture.opm.client.attendance.enums.SpecialDateTypeEnum;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import cathayfuture.opm.domain.attendance.AttendanceRecordEntity;
import cathayfuture.opm.domain.attendance.CalendarEntity;
import cathayfuture.opm.domain.attendance.ennums.AttendanceRecordFlgEnum;
import cathayfuture.opm.domain.attendance.ennums.AttendanceRecordStatusEnum;
import cathayfuture.opm.domain.attendance.ennums.CommonBooleanEnum;
import cathayfuture.opm.domain.attendance.repository.AttendanceRecordRepository;
import cathayfuture.opm.domain.student.StudentEntity;
import cathayfuture.opm.domain.student.enums.GenderEnum;
import cathayfuture.opm.infra.attendance.repository.mapper.AttendanceRecordMapper;
import cathayfuture.opm.infra.common.AopContextUtils;
import cathayfuture.opm.infra.common.basequery.wrapper.ExtQueryWrapper;
import cathayfuture.opm.infra.common.beantools.BeanTools;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.jni.Local;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 考勤记录查询相关
 * @date 2023/3/31 17:13
 */
@Service
@Slf4j
public class AttendanceRecordQueryAppServiceImpl implements AttendanceRecordQueryAppService {

    @Resource
    private StudentQueryAppService studentQueryAppService;
    @Resource
    private AskedForLeaveQueryAppService askedForLeaveQueryAppService;
    @Resource
    private CalendarQueryAppService calendarQueryAppService;

    private AttendanceRecordRepository repository;

    private AttendanceRecordMapper mapper;

    private final String FORMATE_DAY = "yyyy-MM-dd";

    public AttendanceRecordQueryAppServiceImpl(AttendanceRecordRepository repository, AttendanceRecordMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }


    @Override
    public IPage<AttendanceRecordPageRespDTO> page(AttendanceRecordPageReqDto reqDto, IPage page){
        ExtQueryWrapper<AttendanceRecordEntity> query = ExtQueryWrapper.newInstance(AttendanceRecordEntity.class);
        if(StringUtils.isNotBlank(reqDto.getStartDateStr()) && StringUtils.isNotBlank(reqDto.getEndDateStr())){
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(FORMATE_DAY);
            LocalDate begin = LocalDate.parse(reqDto.getStartDateStr(),dateTimeFormatter);
            LocalDate end = LocalDate.parse(reqDto.getEndDateStr(),dateTimeFormatter);
            query.ge(LambdaUtils.column(AttendanceRecordEntity::getAttendanceDate),begin)
                    .le(LambdaUtils.column(AttendanceRecordEntity::getAttendanceDate),end);
        }

        if(Objects.equals(AttendanceRecordStatusEnum.ATTENDANCE.getKey(),reqDto.getStatus())){
            query.and(t->t
                    .eq(LambdaUtils.column(AttendanceRecordEntity::getAmAttendance),AttendanceRecordStatusEnum.ATTENDANCE.getKey())
                    .isNotNull(LambdaUtils.column(AttendanceRecordEntity::getNoonAttendance))
                    .isNotNull(LambdaUtils.column(AttendanceRecordEntity::getPmAttendance))
                    .or()
                    .eq(LambdaUtils.column(AttendanceRecordEntity::getNoonAttendance),AttendanceRecordStatusEnum.ATTENDANCE.getKey())
                    .isNotNull(LambdaUtils.column(AttendanceRecordEntity::getAmAttendance))
                    .isNotNull(LambdaUtils.column(AttendanceRecordEntity::getPmAttendance))
                    .or()
                    .eq(LambdaUtils.column(AttendanceRecordEntity::getPmAttendance),AttendanceRecordStatusEnum.ATTENDANCE.getKey())
                    .isNotNull(LambdaUtils.column(AttendanceRecordEntity::getAmAttendance))
                    .isNotNull(LambdaUtils.column(AttendanceRecordEntity::getNoonAttendance))
            );
        }
        if(Objects.equals(AttendanceRecordStatusEnum.ABSENCE_FROM_DUTY.getKey(),reqDto.getStatus())){
            //缺勤必须三个都是缺勤
            query.eq(LambdaUtils.column(AttendanceRecordEntity::getAmAttendance),AttendanceRecordStatusEnum.ABSENCE_FROM_DUTY.getKey())
                    .eq(LambdaUtils.column(AttendanceRecordEntity::getNoonAttendance),AttendanceRecordStatusEnum.ABSENCE_FROM_DUTY.getKey())
                    .eq(LambdaUtils.column(AttendanceRecordEntity::getPmAttendance),AttendanceRecordStatusEnum.ABSENCE_FROM_DUTY.getKey());
        }

        IPage<AttendanceRecordEntity> pageInfo = mapper.selectPage(page, query
                .orderByDesc(LambdaUtils.column(AttendanceRecordEntity::getCreateTime))
                .andChildCondition(
                        LambdaUtils.column(AttendanceRecordEntity::getStudentId),
                        LambdaUtils.column(StudentEntity::getId),
                        ExtQueryWrapper.newInstance(StudentEntity.class)
                                .like(LambdaUtils.column(StudentEntity::getStudentName), reqDto.getStudentName())
                                .like(LambdaUtils.column(StudentEntity::getStudentNo), reqDto.getStudentNo())
                                .eq(LambdaUtils.column(StudentEntity::getStudentClass), reqDto.getStudentClass())
                )
        );
        IPage<AttendanceRecordPageRespDTO> result = BeanTools.createPageFrom(pageInfo, AttendanceRecordPageRespDTO.class);

        translateStudentInfo(result.getRecords());

        translateEnum(result.getRecords());

        translateDate(result.getRecords());

        return result;
    }

    /**
     * 翻译学生信息
     * @param list
     */
    private void translateStudentInfo(List<AttendanceRecordPageRespDTO> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        Set<Integer> setIds = list.stream()
                .map(AttendanceRecordPageRespDTO::getStudentId)
                .collect(Collectors.toSet());
        List<StudentRespDTO> studentRespDTOS = studentQueryAppService.listByIds(new ArrayList<>(setIds));
        Map<Integer, StudentRespDTO> map = studentRespDTOS.stream()
                .collect(Collectors.toMap(StudentRespDTO::getId, Function.identity(), (o, n) -> n));

        for(AttendanceRecordPageRespDTO dto :list){
            StudentRespDTO student = map.get(dto.getStudentId());

            if(Objects.nonNull(student)){
                dto.setStudentName(student.getStudentName());
                dto.setStudentNo(student.getStudentNo());
                dto.setStudentClass(student.getStudentClass());
                dto.setGender(student.getGender());
            }
        }
    }

    /**
     * 翻译枚举
     * @param list
     */
    private void translateEnum(List<AttendanceRecordPageRespDTO> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }

        for (AttendanceRecordPageRespDTO dto : list) {
            dto.setGenderdesc(GenderEnum.getEnumDescription(dto.getGender()));
            dto.setAmAttendanceDesc(AttendanceRecordStatusEnum.getEnumDescription(dto.getAmAttendance()));
            dto.setNoonAttendanceDesc(AttendanceRecordStatusEnum.getEnumDescription(dto.getNoonAttendance()));
            dto.setPmAttendanceDesc(AttendanceRecordStatusEnum.getEnumDescription(dto.getPmAttendance()));
            AttendanceRecordStatusEnum statusEnum = AttendanceRecordStatusEnum.computeAttendanceRecordStatus(dto.getAmAttendance(), dto.getNoonAttendance(), dto.getPmAttendance());
            String statusDesc = null;
            if(Objects.nonNull(statusEnum)){
                statusDesc = statusEnum.getDescription();
            }
            dto.setStatusDesc(statusDesc);
        }
    }


    /**
     * 翻译日期相关
     * @param list
     */
    private void translateDate(List<AttendanceRecordPageRespDTO> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(FORMATE_DAY);
        for (AttendanceRecordPageRespDTO dto : list) {
            dto.setAttendanceDateStr(dto.getAttendanceDate().format(df));
        }
    }

    @Override
    public List<StudentInfoAndLongTermRespDTO> queryStudentInfoList(String studentClass,String date){

        List<StudentInfoAndLongTermRespDTO> result = new ArrayList<>();
        LocalDate currentDate = LocalDate.now();
        if(StringUtils.isNotBlank(date)){
            currentDate = LocalDate.parse(date,DateTimeFormatter.ISO_LOCAL_DATE);
        }
        //根据入园日期和班级查询学生信息
        List<StudentRespDTO> studentRespDTOS = studentQueryAppService.queryListByAdmissionDateAndStudentClass(studentClass);
        if(CollectionUtil.isEmpty(studentRespDTOS)){
            return result;
        }

        List<Integer> studentIds = studentRespDTOS.stream()
                .map(StudentRespDTO::getId)
                .collect(Collectors.toList());

        String message = attendanceValidity(currentDate, studentIds.stream().findFirst().orElse(null));

        List<AttendanceRecordEntity> entityList = repository.exist(studentIds, currentDate);
        Table<Integer,LocalDate,AttendanceRecordEntity> table= HashBasedTable.create();
        if(CollectionUtil.isNotEmpty(entityList)){
            for (AttendanceRecordEntity entity : entityList) {
                table.put(entity.getStudentId(),entity.getAttendanceDate(),entity);
            }
        }

        for (StudentRespDTO data : studentRespDTOS) {
            //学生信息相关
            Boolean islongterm = askedForLeaveQueryAppService.judgementLongTerm(data.getId(),null);
            StudentInfoAndLongTermRespDTO respDTO = new StudentInfoAndLongTermRespDTO();
            BeanUtil.copyProperties(data,respDTO);
            respDTO.setStudentId(data.getId());
            respDTO.setLongTerm(islongterm);
            respDTO.setGenderDesc(GenderEnum.getEnumDescription(respDTO.getGender()));
            respDTO.setCurrentDate(currentDate);
            respDTO.setCurrentMonthStr(LocalDate.now().getYear()+"-"+LocalDate.now().getMonthValue());
            //考勤相关
            AttendanceRecordEntity entity = table.get(respDTO.getStudentId(), currentDate);
            //默认值为出勤,长期病假默认缺勤
            Integer defaultKey = AttendanceRecordStatusEnum.getDefaultKey(islongterm);

            AttendanceRecordEntity value = Optional.ofNullable(entity)
                    .orElse(buildAttendance(defaultKey, defaultKey, defaultKey));
            respDTO.setAmAttendance(Objects.isNull(value.getAmAttendance())?defaultKey:value.getAmAttendance());
            respDTO.setNoonAttendance(Objects.isNull(value.getNoonAttendance())?defaultKey:value.getNoonAttendance());
            respDTO.setPmAttendance(Objects.isNull(value.getPmAttendance())?defaultKey:value.getPmAttendance());
            respDTO.setRemark(Objects.isNull(value.getRemark())?null:value.getRemark());
            respDTO.setUnusualAttendanceMessage(message);

            result.add(respDTO);
        }
        return result;
    }

    private AttendanceRecordEntity buildAttendance(Integer am,Integer noon,Integer pm){
        AttendanceRecordEntity entity = new AttendanceRecordEntity();
        entity.setAmAttendance(am);
        entity.setNoonAttendance(noon);
        entity.setPmAttendance(pm);
        return entity;
    }

    private static final Map<Integer, String> map = new HashMap<>();

    static {
        map.put(AttendanceRecordFlgEnum.HOLIDAY.getKey(), LambdaUtils.property(AttendanceRecordEntity::getHolidayFlg));
        map.put(AttendanceRecordFlgEnum.LONG_TERM.getKey(), LambdaUtils.property(AttendanceRecordEntity::getLongTermFlg));
        map.put(AttendanceRecordFlgEnum.NORMAL.getKey(), LambdaUtils.property(AttendanceRecordEntity::getNormalFlg));
    }

    public List<AttendanceRecordEntity> queryAttendanceMonth(Integer type,LocalDate date){
        //获取当前月份考勤
        List<AttendanceRecordEntity> list = repository.getListByTargetMonth(date);
        return list.stream()
                .filter(x -> Objects.equals(CommonBooleanEnum.YES.getKey(),(Integer) ReflectUtil.getFieldValue(x, map.get(type))))
                .filter(y -> Objects.equals(CommonBooleanEnum.YES.getKey(),y.getNormalFlg()))
                .collect(Collectors.toList());
    }

    @Override
    public Map<Integer, Long> countAttendanceMonthByStudentIds(Integer type, List<Integer> studentIds,LocalDate date){
        List<AttendanceRecordEntity> list = queryAttendanceMonth(type,date);
        return list.stream()
                .filter(x-> studentIds.contains(x.getStudentId()))
                .collect(Collectors.groupingBy(AttendanceRecordEntity::getStudentId,Collectors.counting()));
    }

    /**
     * 校验考勤合法性,任意一个当前班级的学生就行，因为同一班考勤要么都有，要么都没有
     * @return
     */
    private String attendanceValidity(LocalDate date,Integer studentId){
        //获取dangyue日历表的开园日+寒暑假列表
        List<LocalDate> localDates = calendarQueryAppService.countWeekDay(date);
        //排除当天
        localDates.removeIf(x->x.isEqual(LocalDate.now()));
        //判断这几天这一个班的学生是否每天都有考勤数据，没有的话，返回缺少的日期
        List<LocalDate> entities = repository.queryListByLocalDateList(localDates, studentId);
        //取两个集合的差集，就是目标
        String message =  localDates.stream()
                .filter(x -> !entities.contains(x))
                .map(this::LocalDateFormateToStr)
                .collect(Collectors.joining(","));
        if(StringUtils.isNotBlank(message)){
            message = "日期："+ message +" 的考勤出现异常，请重新填写";
        }
        return message;
    }

    private String LocalDateFormateToStr(LocalDate date){
        DateTimeFormatter dtf= DateTimeFormatter.ofPattern(FORMATE_DAY);
        String dateStr = date.format(dtf);
        return dateStr;
    }

    @Override
    public Map<Integer, LocalDate> queryStudentFirstDayMap(){
        return repository.queryStudentFirstDayMap();
    }

    @Override
    public Boolean dataIntegrityByStudentIds(List<Integer> studentIds){
        List<AttendanceRecordEntity> entities = repository.exist(studentIds, LocalDate.now());
        if(CollectionUtil.isNotEmpty(entities)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }




    @Override
    public List<AttendanceRecordDataIntegrityByClassRespDto> queryAttendanceRecordDataIntegrityByClassRespDto(){
        //查询班级学生
        Map<String, List<StudentRespDTO>> studentMap = studentQueryAppService.queryStudentGroupByClass();
        //班级学生考勤是否填报
        List<AttendanceRecordDataIntegrityByClassRespDto> list = new ArrayList<>();
        studentMap.forEach((k,v)->{
            List<Integer> studentIds = v.stream().map(StudentRespDTO::getId).collect(Collectors.toList());
            Boolean done = this.dataIntegrityByStudentIds(studentIds);
            AttendanceRecordDataIntegrityByClassRespDto dto = new AttendanceRecordDataIntegrityByClassRespDto();
            dto.setClassName(k);
            dto.setStatus(done?CommonBooleanEnum.YES.getKey():CommonBooleanEnum.NO.getKey());
            list.add(dto);
        });
        return list;
    }

}
