package cathayfuture.opm.app.order.service;

import cathayfuture.opm.app.common.RedisLockService;
import cathayfuture.opm.client.account.api.AccountAppService;
import cathayfuture.opm.client.account.dto.response.AccountRespDTO;
import cathayfuture.opm.client.common.IdSequenceService;
import cathayfuture.opm.client.order.api.ComputationalCostsAppService;
import cathayfuture.opm.client.order.api.OrderAppService;
import cathayfuture.opm.client.order.api.OrderMonthlyStatementAppService;
import cathayfuture.opm.client.order.api.PaymentDetailAppService;
import cathayfuture.opm.client.order.dto.request.*;
import cathayfuture.opm.client.order.dto.response.EnrollRespDTO;
import cathayfuture.opm.client.order.dto.response.PreOrderRespDTO;
import cathayfuture.opm.client.order.exception.OrderCreateException;
import cathayfuture.opm.client.order.exception.OrderOfflinePayException;
import cathayfuture.opm.client.order.exception.OrderPayNotifyException;
import cathayfuture.opm.client.order.exception.OrderPrePayException;
import cathayfuture.opm.client.student.api.StudentAppService;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import cathayfuture.opm.client.utils.WxAccountHolder;
import cathayfuture.opm.domain.order.OrderEntity;
import cathayfuture.opm.domain.order.PaymentDetailEntity;
import cathayfuture.opm.domain.order.enums.*;
import cathayfuture.opm.domain.order.gateway.projection.PaymentProjection;
import cathayfuture.opm.domain.order.gateway.repository.OrderRepository;
import cathayfuture.opm.domain.order.gateway.repository.PaymentDetailRepository;
import cathayfuture.opm.domain.student.enums.RegisterStatusEnum;
import cathayfuture.opm.domain.student.enums.StudentTypeEnum;
import cathayfuture.opm.infra.common.ExtServiceContext;
import cathayfuture.opm.infra.common.LocalServiceContext;
import cathayfuture.opm.infra.common.utils.BigDecimalUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.dtyunxi.huieryun.lock.api.Mutex;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
// 移除Spring Cloud依赖，删除RefreshScope注解
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderAppServiceImpl implements OrderAppService {

    public static final String LOCK_SERVICE_UNLOCK_MUTEX_PARAM_KEY = ":lockService.unlock_mutex: [{}]";
    @Value("${order.code.prefix}")
    private String orderCodePrefix;
    @Value("${order.frontMoney.amount: 3000}")
    private BigDecimal frontMoneyAmount;

    private static final String ORDER_PRE_PAY_LOCK_KEY = "ORDER_PRE_PAY_LOCK_KEY";

    private static final String ORDER_IMPORT_LOCK_KEY = "ORDER_IMPORT_LOCK_KEY";

    private static final String ORDER_CREATE_LOCK_KEY = "ORDER_CREATE_LOCK_KEY";

    private static final String ORDER_OFFLINE_PAY_LOCK_KEY = "ORDER_OFFLINE_PAY_LOCK_KEY";

    private static final String PAYMENT_CENTER_CALLBACK_SUCCESS_FLAG = "SUCCESS";

    @Resource
    private PaymentProjection paymentProjection;
    @Resource
    private PaymentDetailRepository paymentDetailRepository;
    @Resource
    private RedisLockService redisLockService;
    @Resource
    private StudentAppService studentAppService;
    @Resource
    private StudentQueryAppService studentQueryAppService;
    @Resource
    private AccountAppService accountAppService;
    @Resource
    private ComputationalCostsAppService computationalCostsAppService;
    @Resource
    private OrderMonthlyStatementAppService orderMonthlyStatementAppService;


    private OrderRepository orderRepository;

    private IdSequenceService idSequenceService;

    private PaymentDetailAppService paymentDetailAppService;

    public OrderAppServiceImpl(OrderRepository orderRepository, IdSequenceService idSequenceService, PaymentDetailAppService paymentDetailAppService) {
        this.orderRepository = orderRepository;
        this.idSequenceService = idSequenceService;
        this.paymentDetailAppService = paymentDetailAppService;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EnrollRespDTO enroll(EnrollReqDTO enrollReqDTO) {
        OrderEntity order = convertToEnrollOrder(enrollReqDTO);
        order = this.orderRepository.add(order);
        return convertEnrollRespDTO(order);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String prePay(Integer orderId) {
        Mutex mutex = null;
        try {
            log.info("预支付开始，orderId[{}]", orderId);
            mutex = redisLockService.lock(ORDER_PRE_PAY_LOCK_KEY, String.valueOf(orderId));
            log.info(ORDER_PRE_PAY_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutex.getLockKey());
            OrderEntity orderEntity = orderRepository.getById(orderId);
            if (Objects.isNull(orderEntity)) {
                throw new OrderPrePayException("订单不存在！");
            }
            if (!Objects.equals(orderEntity.getPaymentStatus(), PaymentStatusEnum.NO_PAY.getKey())) {
                throw new OrderPrePayException("该订单已支付！");
            }
            //todo: 2023.05.23 订单金额是0或者负数，支付状态应该是无需支付
            if(Objects.equals(orderEntity.getPaymentStatus(), PaymentStatusEnum.NEEDLESS.getKey())){
                throw new OrderPrePayException("该订单无需支付！");
            }
            if (Objects.equals(orderEntity.getStatus(), OrderStatusEnum.DELETED.getKey())) {
                throw new OrderPrePayException("该订单已作废！");
            }
            AccountRespDTO currentUser = WxAccountHolder.get();
            if (Objects.nonNull(orderEntity.getPayer()) && !Objects.equals(orderEntity.getPayer(), currentUser.getId())) {
                throw new OrderPrePayException("已有用户正在付款！");
            }
            PaymentDetailEntity lastNotPayDetail = paymentDetailRepository.getLastNotPayByOrderId(orderId);
            if (Objects.isNull(orderEntity.getPayer())
                    || Objects.isNull(lastNotPayDetail)
                    || !Objects.equals(lastNotPayDetail.getPayerId(), orderEntity.getPayer())
                    || LocalDateTime.now().minusMinutes(110L).isAfter(lastNotPayDetail.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
            ) {
                log.info("未发起过预支付或变更人员后未发起过预支付");
                // 插入支付详情
                PaymentDetailEntity paymentDetailEntity = new PaymentDetailEntity()
                        .setOrderId(orderEntity.getId())
                        .setPaymentType(PaymentChannelEnum.ONLINE.getKey())
                        .setPaymentMode(PaymentModeEnum.WECHAT_ONLINE.getKey())
                        .setPaymentStatus(PaymentStatusEnum.NO_PAY.getKey())
                        .setPayAmount(Optional.ofNullable(orderEntity.getChargeAmount()).orElse(BigDecimal.ZERO))
                        .setPayerId(currentUser.getId());
                paymentDetailEntity.setCreateTime(new Date());
                paymentDetailEntity.setCreatePerson(String.valueOf(currentUser.getId()));
                paymentDetailRepository.add(paymentDetailEntity);
                // 更新订单信息
                String tradeNo = generateTradeNo(StringUtils.isBlank(orderEntity.getTradeNo()) ? orderEntity.getCode() : orderEntity.getTradeNo());
                orderEntity.setTradeNo(tradeNo);
                OrderEntity orderUpdateParam = new OrderEntity();
                orderUpdateParam.setId(orderId);
                orderUpdateParam.setPayer(currentUser.getId());
                orderUpdateParam.setTradeNo(tradeNo);
                orderUpdateParam.setUpdateTime(new Date());
                orderUpdateParam.setUpdatePerson(String.valueOf(currentUser.getId()));
                orderUpdateParam.setRevision(orderEntity.getRevision());
                if (orderRepository.updateById(orderUpdateParam) == 0) {
                    throw new OrderPrePayException("支付失败，订单状态已改变");
                }
            }
            return paymentProjection.prePay(orderEntity);
        } catch (Exception e) {
            log.error("订单预支付失败, orderId[{}]", orderId, e);
            throw e;
        } finally {
            log.info(ORDER_PRE_PAY_LOCK_KEY + LOCK_SERVICE_UNLOCK_MUTEX_PARAM_KEY, Optional.ofNullable(mutex).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutex);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteOrder(Integer orderId, String deleteReason) {
        return orderRepository.delete(orderId, deleteReason);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void payComplete(String tradeNo, String payStatus, BigDecimal payAmount, String tripartiteOrderNo) {
        OrderEntity orderEntity = orderRepository.getByTradeNo(tradeNo);
        if (Objects.equals(orderEntity.getPaymentStatus(), PaymentStatusEnum.PAID.getKey())) {
            log.info("订单状态是已支付，直接返回成功");
            return;
        }
        PaymentDetailEntity paymentDetailEntity = paymentDetailRepository.getLastNotPayByOrderId(orderEntity.getId());
        if (Objects.isNull(paymentDetailEntity)) {
            throw new OrderPayNotifyException("支付信息不存在");
        }
        if (!Objects.equals(orderEntity.getPayer(), paymentDetailEntity.getPayerId())) {
            throw new OrderPayNotifyException("支付信息异常");
        }
        if (StringUtils.equalsIgnoreCase(payStatus, PAYMENT_CENTER_CALLBACK_SUCCESS_FLAG)) {
            if (paymentDetailEntity.getPayAmount().compareTo(payAmount) != 0) {
                throw new OrderPayNotifyException("订单[" + orderEntity.getCode() + "]订单金额与已支付金额不一致");
            }
            OrderEntity updateParam = new OrderEntity();
            updateParam.setId(orderEntity.getId());
            updateParam.setPaymentStatus(PaymentStatusEnum.PAID.getKey());
            updateParam.setPayAmount(payAmount);
            updateParam.setPaymentTime(new Date());
            updateParam.setPaymentChannel(PaymentChannelEnum.ONLINE.getKey());
            updateParam.setPaymentMode(String.valueOf(PaymentModeEnum.WECHAT_ONLINE.getKey()));
            updateParam.setStatus(OrderStatusEnum.NORMAL.getKey());
            updateParam.setUpdateTime(new Date());
            // 使用乐观锁
            updateParam.setRevision(orderEntity.getRevision());
            if (orderRepository.updateById(updateParam) == 0) {
                throw new OrderPayNotifyException("订单[" + orderEntity.getCode() + "]状态已改变");
            }
            PaymentDetailEntity detailUpdateParam = new PaymentDetailEntity()
                    .setPaymentStatus(PaymentStatusEnum.PAID.getKey())
                    .setRevision(paymentDetailEntity.getRevision());
            detailUpdateParam.setId(paymentDetailEntity.getId());
            detailUpdateParam.setTransactionId(tripartiteOrderNo);
            detailUpdateParam.setUpdateTime(new Date());
            if (paymentDetailRepository.updateById(detailUpdateParam) == 0) {
                throw new OrderPayNotifyException("订单[" + orderEntity.getCode() + "]状态已改变");
            }
        }
    }

    private EnrollRespDTO convertEnrollRespDTO(OrderEntity order) {
        EnrollRespDTO enrollRespDTO = new EnrollRespDTO();
        BeanUtils.copyProperties(order, enrollRespDTO);
        return enrollRespDTO;
    }

    private OrderEntity convertToEnrollOrder(EnrollReqDTO enrollReqDTO) {
        OrderEntity order = new OrderEntity();
        BeanUtils.copyProperties(enrollReqDTO, order);
        return order;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateCode(int type) {
        String codePrefix = orderCodePrefix;
        String dateStr = DateUtil.format(new Date(), "yyyyMMdd");
        String dateTimeStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        String datePrefix = codePrefix + dateStr;
        String dateTimePrefix = codePrefix + dateTimeStr;
        Integer sequence = idSequenceService.incrAndGetSequence(datePrefix);
        return dateTimePrefix + StringUtils.leftPad(sequence.toString(), 4, '0');
    }

    @Override
    public List<String> generateCodes(int type, int increment) {
        String codePrefix = orderCodePrefix;
        String dateStr = DateUtil.format(new Date(), "yyyyMMdd");
        String dateTimeStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        String datePrefix = codePrefix + dateStr;
        String dateTimePrefix = codePrefix + dateTimeStr;
        Integer sequence = idSequenceService.incrAndGetSequence(datePrefix, increment);
        List<String> codes = Lists.newArrayList();
        for (int i = 0; i < increment; i++) {
            String code = dateTimePrefix + StringUtils.leftPad(Integer.toString((sequence - i)), 4, '0');
            codes.add(code);
        }
        Collections.reverse(codes);
        return codes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changePayer(OrderChangePayerReqDTO changePayerReqDTO) {
        Mutex mutex = null;
        Integer orderId = changePayerReqDTO.getOrderId();
        try {
            mutex = redisLockService.lock(ORDER_PRE_PAY_LOCK_KEY, String.valueOf(orderId));
            log.info(ORDER_PRE_PAY_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutex.getLockKey());
            OrderEntity orderEntity = orderRepository.getById(orderId);
            if (Objects.isNull(orderEntity)) {
                throw new OrderPrePayException("该订单不存在！");
            }
            if (!Objects.equals(orderEntity.getPaymentStatus(), PaymentStatusEnum.NO_PAY.getKey())) {
                throw new OrderPrePayException("该订单已支付！");
            }
            //todo: 2023.05.23 订单金额是0或者负数，支付状态应该是无需支付

            if (!Objects.equals(orderEntity.getPaymentStatus(), PaymentStatusEnum.NEEDLESS.getKey())) {
                throw new OrderPrePayException("该订单无需支付！");
            }
            if (Objects.equals(orderEntity.getStatus(), OrderStatusEnum.DELETED.getKey())) {
                throw new OrderPrePayException("该订单已作废！");
            }
            if (Objects.isNull(orderEntity.getPayer())) {
                throw new OrderPrePayException("该订单无支付人！");
            }
            if (Objects.equals(orderEntity.getPayer(), changePayerReqDTO.getPayerId())) {
                throw new OrderPrePayException("不能变更成相同的支付人");
            }
            OrderEntity updateParam = new OrderEntity();
            updateParam.setId(orderId);
            updateParam.setUpdatePerson(String.valueOf(WxAccountHolder.get().getId()));
            updateParam.setUpdateTime(new Date());
            updateParam.setRevision(orderEntity.getRevision());
            updateParam.setPayer(changePayerReqDTO.getPayerId());
            if (orderRepository.updateById(updateParam) == 0) {
                throw new OrderPrePayException("变更支付人失败！");
            }
            return true;
        } catch (Exception e) {
            log.error("订单变更支付人失败, orderId[{}]", orderId, e);
            throw e;
        } finally {
            log.info(ORDER_PRE_PAY_LOCK_KEY + LOCK_SERVICE_UNLOCK_MUTEX_PARAM_KEY, Optional.ofNullable(mutex).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutex);

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean payOffline(OrderReqDTO orderReqDTO) {
        Integer orderId = orderReqDTO.getId();
        Mutex mutex = null;
        try {
            mutex = redisLockService.lock(ORDER_OFFLINE_PAY_LOCK_KEY, String.valueOf(orderId));
            log.info(ORDER_OFFLINE_PAY_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutex.getLockKey());
            OrderEntity orderEntity = orderRepository.getById(orderId);
            BigDecimal chargeAmount = orderEntity.getChargeAmount();
            BigDecimal detailAmountSum = orderReqDTO.getPaymentDetails().stream().map(PaymentDetailReqDTO::getPayAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            // 是否是全额活动减免
            boolean isActivity = orderReqDTO.getPaymentDetails().stream().anyMatch(detail -> Objects.equals(detail.getPaymentMode(), PaymentModeEnum.ACTIVITY_REDUCTION.getKey()));
            if (orderEntity.getStatus() != OrderStatusEnum.NORMAL.getKey()) {
                log.info("订单状态不正确, 订单状态为[{}]", orderEntity.getStatus());
                throw new OrderOfflinePayException("该订单已作废！");
            }
            //todo: 2023.05.23 订单金额是0或者负数，支付状态应该是无需支付
            if(chargeAmount.compareTo(BigDecimal.ZERO)<=0){
                throw new OrderOfflinePayException("该订单无需支付！");
            }
            if (orderEntity.getPaymentStatus() != PaymentStatusEnum.NO_PAY.getKey()) {
                log.info("订单支付状态不正确，订单支付状态为[{}]", orderEntity.getPaymentStatus());
                if (orderEntity.getPaymentStatus() == PaymentStatusEnum.NEEDLESS.getKey()) {
                    throw new OrderOfflinePayException("该订单无需支付！");
                }
                throw new OrderOfflinePayException("该订单已支付！");
            }
            if (!isActivity && chargeAmount.compareTo(detailAmountSum) != 0) {
                log.info("订单金额与支付明细金额不相等, 订单金额为[{}], 支付明细金额为[{}]", chargeAmount, detailAmountSum);
                throw new OrderOfflinePayException("订单金额与支付明细金额不相等!");
            }
            List<PaymentDetailReqDTO> posCheckPaymentDetails = orderReqDTO.getPaymentDetails().stream().filter(OrderAppServiceImpl::filterPos).collect(Collectors.toList());
            boolean posFlag = posCheckPaymentDetails.stream().allMatch(detail -> StringUtils.isNotBlank(detail.getPos()));
            if (!posFlag) {
                log.info("银行卡支付必须要有pos号");
                throw new OrderOfflinePayException("银行卡支付必须要有pos号!");
            }

            OrderEntity paidOrder = new OrderEntity();
            paidOrder.setId(orderId);
            paidOrder.setUpdatePerson(ExtServiceContext.getUserName());
            paidOrder.setCollectionPerson(LocalServiceContext.getContext().getRequestUserCode());
            paidOrder.setPayAmount(chargeAmount);
            paidOrder.setPaymentStatus(PaymentStatusEnum.PAID.getKey());
            paidOrder.setPaymentTime(new Date());
            paidOrder.setUpdateTime(new Date());
            paidOrder.setPaymentChannel(PaymentChannelEnum.OFFLINE.getKey());
            paidOrder.setRemark(orderReqDTO.getRemark());
            paidOrder.setPaymentMode(orderReqDTO.getPaymentDetails()
                    .stream()
                    .map(PaymentDetailReqDTO::getPaymentMode)
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
            orderRepository.updateById(paidOrder);
            orderReqDTO.getPaymentDetails().forEach(detailDto -> {
                detailDto.setOrderId(orderId);
                detailDto.setPaymentModeName(PaymentModeEnum.getEnumDescription(detailDto.getPaymentMode()));
                detailDto.setPaymentType(PaymentChannelEnum.OFFLINE.getKey());
                detailDto.setPaymentStatus(PaymentStatusEnum.PAID.getKey());
                detailDto.setCreateTime(new Date());
                detailDto.setUpdateTime(new Date());
                // 判断是否是活动减免，若是活动减免，则支付金额为收费金额减去detailAmountSum缴费金额总和
                if(Objects.equals(detailDto.getPaymentMode(), PaymentModeEnum.ACTIVITY_REDUCTION.getKey())){
                    // detailDto.setPayAmount(chargeAmount.subtract(detailAmountSum));
                    // 当前只有全额活动减免，所以支付金额为收费金额
                    detailDto.setPayAmount(chargeAmount);
                }
            });
            paymentDetailAppService.batchAdd(orderReqDTO.getPaymentDetails());

        } catch (Exception e) {
            log.error("订单线下支付失败, orderId[{}]", orderId, e);
            throw e;
        } finally {
            log.info(ORDER_OFFLINE_PAY_LOCK_KEY + LOCK_SERVICE_UNLOCK_MUTEX_PARAM_KEY, Optional.ofNullable(mutex).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutex);
        }
        return Boolean.TRUE;
    }

    private static boolean filterPos(PaymentDetailReqDTO detail) {
        int[] checkPosPaymentModes = {PaymentModeEnum.BANK_CARD.getKey(), PaymentModeEnum.WECHAT_OFFLINE.getKey(), PaymentModeEnum.ALIPAY.getKey()};
        return Arrays.stream(checkPosPaymentModes).anyMatch(detail.getPaymentMode()::equals);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean importOrders(List<OrderReqDTO> orderReqDTOList) {
        Mutex mutex = null;
        try {
            mutex = redisLockService.lock(ORDER_IMPORT_LOCK_KEY, "order");
            log.info(ORDER_IMPORT_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutex.getLockKey());
            List<String> codes = generateCodes(OrderTypeEnum.CHILD_CARE.getKey(), orderReqDTOList.size());

            List<Integer> studentIds = orderReqDTOList.stream()
                    .map(OrderReqDTO::getStudentId)
                    .collect(Collectors.toList());
            Integer type = orderReqDTOList.stream()
                    .findFirst()
                    .orElse(new OrderReqDTO())
                    .getType();
            //todo: 2023.05.26导入只能本月，所以获取月份当前月份
            Map<Integer, BigDecimal> previousActualSurplusMap = orderMonthlyStatementAppService.queryPreviousActualSurplusMap(studentIds, LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()), type);

            List<OrderEntity> orderEntityList = orderReqDTOList.stream().map(req -> {
                OrderEntity order = new OrderEntity();
                BeanUtils.copyProperties(req, order);
                //todo: 2023.05.26 导入订单金额如果是0或者负数，支付状态应该是无需支付
                Integer paymentStatus = PaymentStatusEnum.NO_PAY.getKey();
                if(req.getChargeAmount().compareTo(BigDecimal.ZERO)<=0){
                    paymentStatus = PaymentStatusEnum.NEEDLESS.getKey();
                }
                order.setStatus(OrderStatusEnum.NORMAL.getKey());
                order.setPaymentStatus(paymentStatus);
                order.setBusinessForms(BusinessUnitEnum.KINDERGARTEN.getKey());
                //todo: 2023.05.26上期余额字段现在需要从月结表获取
/*                order.setPreviousActualSurplus(Optional.ofNullable(order.getPreviousRemainingSum()).orElse(BigDecimal.ZERO)
                        .subtract(Optional.ofNullable(order.getPreviousPayableCharge()).orElse(BigDecimal.ZERO)));*/
                BigDecimal previousActualSurplus = previousActualSurplusMap.getOrDefault(req.getStudentId(), BigDecimal.ZERO);
                BigDecimal nextPayableCharge = BigDecimalUtil.add(order.getChargeAmount(),previousActualSurplus);

                order.setPreviousActualSurplus(previousActualSurplus);
                order.setNextPayableCharge(nextPayableCharge);

                order.setCreatePerson(ExtServiceContext.getUserCode());
                order.setCreateTime(new Date());
                order.setUpdatePerson(ExtServiceContext.getUserCode());
                order.setUpdateTime(new Date());
                order.setDr(0);
                order.setDataSource(OrderSourceEnum.MANUAL_OPERATION.getKey());
                return order;
            }).collect(Collectors.toList());
            for (int i = 0; i < orderEntityList.size(); i++) {
                orderEntityList.get(i).setCode(codes.get(i));
            }
            List<List<OrderEntity>> partitionOrderEntityList = Lists.partition(orderEntityList, 500);
            partitionOrderEntityList.forEach(partition -> orderRepository.addBatch(partition));
        } catch (Exception e) {
            log.error("订单导入失败", e);
            throw e;
        } finally {
            log.info(ORDER_IMPORT_LOCK_KEY + LOCK_SERVICE_UNLOCK_MUTEX_PARAM_KEY, Optional.ofNullable(mutex).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutex);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createFrontMoneyOrder(CreateFrontMoneyOrderReqDTO createFrontMoneyOrderReqDTO) {
        AccountRespDTO currentUser = WxAccountHolder.get();
        if (Objects.isNull(currentUser)) {
            throw new OrderCreateException("请先登陆再操作");
        }
        Mutex mutex = null;
        try {
            mutex = redisLockService.lock(ORDER_CREATE_LOCK_KEY, createFrontMoneyOrderReqDTO.getContactPhoneNumber());
            log.info(ORDER_CREATE_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutex.getLockKey());
            log.info("nacos配置定金订单的金额为[{}]", frontMoneyAmount);
    /*        List<StudentRespDTO> studentList = studentQueryAppService.listByContactPhoneNumber(createFrontMoneyOrderReqDTO.getContactPhoneNumber());
            Optional<StudentRespDTO> studentOptional = studentList.stream().filter(s -> Objects.equals(createFrontMoneyOrderReqDTO.getBirthday(), s.getBirthday()) && Objects.equals(createFrontMoneyOrderReqDTO.getStudentName(), s.getStudentName()))
                    .findFirst();*/
            //******** 判断是否新学生，产品与业务沟通去除了联系人手机号这一条件，只有：学生姓名，学生生日，入学状态为预入园或者在园
            List<StudentRespDTO> studentList = studentQueryAppService.queryByCondition(createFrontMoneyOrderReqDTO.getStudentName(),
                    LocalDate.parse(createFrontMoneyOrderReqDTO.getBirthday(), DateTimeFormatter.ISO_DATE),
                    RegisterStatusEnum.getActiveList());
            StudentRespDTO student;
/*            if (studentOptional.isPresent()) {
                student = studentOptional.get();*/
            if(CollectionUtil.isNotEmpty(studentList)){
                throw new OrderCreateException("当前学生已经注册，请勿重复注册");
            } else {
                StudentReqDTO studentReqDTO = new StudentReqDTO();
                BeanUtils.copyProperties(createFrontMoneyOrderReqDTO, studentReqDTO);
                studentReqDTO.setStudentType(StudentTypeEnum.NOT_SIGNED.getKey());
                studentReqDTO.setBusinessUnit(BusinessUnitEnum.KINDERGARTEN.getKey());
                student = studentAppService.addFrontMoneyStudent(studentReqDTO);
            }
            accountAppService.bindStudents(Lists.newArrayList(new StudentReqDTO().setId(student.getId())));
            OrderEntity order = new OrderEntity();
            order.setCode(generateCode(OrderTypeEnum.FRONT_MONEY.getKey()));
            order.setType(OrderTypeEnum.FRONT_MONEY.getKey());
            order.setStatus(OrderStatusEnum.NORMAL.getKey());
            order.setPaymentStatus(PaymentStatusEnum.NO_PAY.getKey());
            order.setBusinessForms(BusinessUnitEnum.KINDERGARTEN.getKey());
            order.setStudentId(student.getId());
            order.setStudentCode(student.getStudentNo());
            order.setStudentName(student.getStudentName());
            order.setGrade(student.getStudentClass());
            order.setContactInfo(student.getContactPhoneNumber());
            order.setChargingStandard(frontMoneyAmount);
            order.setChargeItem(OrderTypeEnum.FRONT_MONEY.getDescription());
            order.setChargeAmount(frontMoneyAmount);
            order.setCreatePerson(String.valueOf(currentUser.getId()));
            order.setCreateTime(new Date());
            order.setUpdatePerson(String.valueOf(currentUser.getId()));
            order.setUpdateTime(new Date());
            OrderEntity addedOrder = orderRepository.add(order);

            //20230418 新增需求：创建报名订单成功后需要 创建杂费订单
            computationalCostsAppService.computeSundryCharges(student.getId());
            return addedOrder.getId();
        } catch (Exception e) {
            log.error("订单创建失败", e);
            throw e;
        } finally {
            log.info(ORDER_CREATE_LOCK_KEY + LOCK_SERVICE_UNLOCK_MUTEX_PARAM_KEY, Optional.ofNullable(mutex).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutex);
        }
    }

    @Override
    public PreOrderRespDTO preOrder(PreOrderReqDTO preOrderReqDTO) {
        PreOrderRespDTO result = new PreOrderRespDTO();
        result.setActualPayAmount(frontMoneyAmount);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refund(Integer orderId, String refundTime, String refundReason) {
        Boolean orderResult = orderRepository.refund(orderId, refundTime, refundReason);
        Boolean paymentResult = Boolean.FALSE;
        if (Boolean.TRUE.equals(orderResult)) {
            paymentResult = paymentDetailAppService.refund(orderId);
        }
        return orderResult && paymentResult;
    }

    private String generateTradeNo(String oldTradeNo) {
        if (!StringUtils.contains(oldTradeNo, "-")) {
            return oldTradeNo + "-" + "001";
        }
        String[] split = StringUtils.split(oldTradeNo, "-");
        if (ArrayUtils.getLength(split) != 2) {
            throw new OrderPrePayException("生成新的商户订单号失败，订单号：" + oldTradeNo);
        }
        if (!NumberUtil.isInteger(split[1])) {
            throw new OrderPrePayException("生成新的商户订单号失败，订单号：" + oldTradeNo);
        }
        return split[0] + "-" + String.format("%03d", Integer.valueOf(split[1]) + 1);
    }



}
