package cathayfuture.opm.app.order;

import cathayfuture.opm.client.order.api.ComputationalCostsAppService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单定时任务
 * @date 2023/6/9 10:27
 */
@Component
public class OrderTask {

    @Resource
    private ComputationalCostsAppService service;

    /**
     * 月结
     */
    public void monthlyStatementTask(){
        service.monthlySettlementOperate(null,null);
        service.monthlySettlementForDinnerOperate(null,null);
    }

    /**
     * 订单生成
     */
    public void systemCreateOrder(){
        service.childCareOperate(null,null);
        service.dinnerOperate(null,null);
    }

}
