package cathayfuture.opm.app.order.service;

import cathayfuture.opm.app.common.RedisLockService;
import cathayfuture.opm.app.order.service.computationalCosts.HandlerAutoCalFactory;
import cathayfuture.opm.client.attendance.api.query.AskedForLeaveQueryAppService;
import cathayfuture.opm.client.attendance.api.query.AttendanceRecordQueryAppService;
import cathayfuture.opm.client.attendance.api.query.CalendarQueryAppService;
import cathayfuture.opm.client.common.IdSequenceService;
import cathayfuture.opm.client.order.api.AutoCalHandler;
import cathayfuture.opm.client.order.api.ComputationalCostsAppService;
import cathayfuture.opm.client.order.api.OrderMonthlyStatementAppService;
import cathayfuture.opm.client.order.api.OrderOperationRecordService;
import cathayfuture.opm.client.order.dto.bo.*;
import cathayfuture.opm.client.order.dto.response.ComputationalOrderAddRespDTO;
import cathayfuture.opm.client.student.api.query.ExpenseDefaultQueryAppService;
import cathayfuture.opm.client.student.api.query.ExpenseStandardQueryAppService;
import cathayfuture.opm.client.student.api.query.RateReliefQueryAppService;
import cathayfuture.opm.client.student.dto.response.ExpenseStandardRespDTO;
import cathayfuture.opm.domain.attendance.ennums.AttendanceRecordFlgEnum;
import cathayfuture.opm.domain.attendance.ennums.CommonBooleanEnum;
import cathayfuture.opm.domain.order.OrderChargeAmountRecordEntity;
import cathayfuture.opm.domain.order.OrderEntity;
import cathayfuture.opm.domain.order.OrderMonthlyStatementEntity;
import cathayfuture.opm.domain.order.OrderRecoreEntity;
import cathayfuture.opm.domain.order.enums.*;
import cathayfuture.opm.domain.order.gateway.repository.OrderChargeAmountRecordRepository;
import cathayfuture.opm.domain.order.gateway.repository.OrderMonthlyStatementRepository;
import cathayfuture.opm.domain.order.gateway.repository.OrderRecoreRepository;
import cathayfuture.opm.domain.order.gateway.repository.OrderRepository;
import cathayfuture.opm.domain.student.StudentEntity;
import cathayfuture.opm.domain.student.enums.ExpenseTypeEnum;
import cathayfuture.opm.domain.student.repository.StudentRepository;
import cathayfuture.opm.infra.common.ExtServiceContext;
import cathayfuture.opm.infra.common.beantools.BeanTools;
import cathayfuture.opm.infra.common.utils.BigDecimalUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.dtyunxi.exceptions.BizException;
import com.dtyunxi.huieryun.lock.api.Mutex;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 费用计算
 * @date 2023/4/11 16:30
 */
@Service
@Slf4j
public class ComputationalCostsAppServiceImpl implements ComputationalCostsAppService {

    @Resource
    private AttendanceRecordQueryAppService attendanceRecordQueryAppService;
    @Resource
    private CalendarQueryAppService calendarQueryAppService;
    @Resource
    private ExpenseStandardQueryAppService expenseStandardQueryAppService;
    @Resource
    private StudentRepository studentRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private AskedForLeaveQueryAppService askedForLeaveQueryAppService;
    @Resource
    private RateReliefQueryAppService rateReliefQueryAppService;
    @Resource
    private ExpenseDefaultQueryAppService expenseDefaultQueryAppService;
    @Resource
    private ComputationalCostsAppService service;
    @Resource
    private IdSequenceService idSequenceService;
    @Resource
    private RedisLockService redisLockService;
    @Resource
    private OrderChargeAmountRecordRepository orderChargeAmountRecordRepository;
    @Resource
    private OrderMonthlyStatementAppService orderMonthlyStatementAppService;
    @Resource
    private OrderMonthlyStatementRepository orderMonthlyStatementRepository;
    @Resource
    private OrderOperationRecordService orderOperationRecordService;

    public static final String LOCK_SERVICE_UNLOCK_MUTEX_PARAM_KEY = ":lockService.unlock_mutex: [{}]";
    public static final String LOCK_SERVICE_LOCK_MUTEX_PARAM_KEY = ":lockService.lock_mutex: [{}]";
    public static final String AUTO_CREATE_ORDER_LOCK_KEY = "AUTO_CREATE_ORDER_LOCK_KEY";
    private static final String ORDER_IMPORT_LOCK_KEY = "ORDER_IMPORT_LOCK_KEY";
    @Value("${order.code.prefix}")
    private String orderCodePrefix;

    private final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final DateTimeFormatter LAST_FORMATE = DateTimeFormatter.ofPattern("yyyy-MM");

    private final String SYSTEM_AUTO_PERSON = "systemGeneration";



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddOrderEntityList(List<ComputationalOrderAddRespDTO> respDTOList){
        if(CollectionUtil.isEmpty(respDTOList)){
            return;
        }
        List<OrderEntity> list = BeanTools.createListFrom(respDTOList, OrderEntity.class);
        LocalDate targetDate = respDTOList.stream().findFirst().orElse(new ComputationalOrderAddRespDTO()).getTargetDate();

        List<OrderChargeAmountRecordBo> recordList = respDTOList.stream()
                .map(ComputationalOrderAddRespDTO::getOrderChargeAmountRecordBo)
                .collect(Collectors.toList());

        List<OrderChargeAmountRecordEntity> listFrom = BeanTools.createListFrom(recordList, OrderChargeAmountRecordEntity.class);

        Integer type = null;

        List<OrderOperationRecordEntityBo> OrderOperationRecordEntityBoList = new ArrayList<>();
        String userCode = ExtServiceContext.getUserCode();
        for(OrderEntity data :list){
            data.setDr(CommonBooleanEnum.NO.getKey());
            type = data.getType();
            data.setCreateTime(new Date());
            data.setUpdateTime(new Date());
            data.setDataSource(OrderSourceEnum.SYSTEM_OPERATION.getKey());
            data.setCreatePerson(SYSTEM_AUTO_PERSON);
            data.setUpdatePerson(SYSTEM_AUTO_PERSON);


            OrderOperationRecordEntityBo entityBo = new OrderOperationRecordEntityBo();
            entityBo.setOperator(userCode);
            entityBo.setBatchCode(data.getBatchCode());
            entityBo.setType(data.getType());
            entityBo.setDr(CommonBooleanEnum.NO.getKey());
            entityBo.setOperationType(OrderCreateOperationTypeEnum.ORDER.getKey());
            OrderOperationRecordEntityBoList.add(entityBo);
        }
        Map<String, List<OrderOperationRecordEntityBo>> groupByBatchCode = OrderOperationRecordEntityBoList.stream()
                .collect(Collectors.groupingBy(OrderOperationRecordEntityBo::getBatchCode));


        List<OrderOperationRecordEntityBo> recordEntityBoList = new ArrayList<>();
        groupByBatchCode.forEach((x,y)->{
            OrderOperationRecordEntityBo recordEntityBo = y.stream().findAny().orElse(new OrderOperationRecordEntityBo());
            recordEntityBoList.add(recordEntityBo);
        });


        // 加redis锁，防止同时并发查询出现不一致的情况
/*        Mutex mutex = null;
        try {
            log.info("自动生成订单数据准备入库，List<OrderEntity>：[{}],batchId:[{}]", JSONUtil.toJsonStr(list),batchId);
            mutex = redisLockService.lock(AUTO_CREATE_ORDER_LOCK_KEY, batchId);
            log.info(AUTO_CREATE_ORDER_LOCK_KEY + LOCK_SERVICE_LOCK_MUTEX_PARAM_KEY, mutex.getLockKey());*/

            //同一个人一个类别一个月可能有多条订单（除了系统生成的订单外，还有可能存在用户手动导入）
            Map<Integer, Integer> map = queryOrderMap(ComputationalMonthEnum.CURRENT_MONTH, OrderEntity::getType,type,targetDate);
            list.removeIf(x -> Objects.equals(x.getType(), map.get(x.getStudentId())));

            if(CollectionUtil.isNotEmpty(list)){
                List<List<OrderEntity>> partitionOrderEntityList = Lists.partition(list, 500);
                partitionOrderEntityList.forEach(partition -> orderRepository.addBatch(partition));

                List<List<OrderChargeAmountRecordEntity>> partitionRecordEntityList = Lists.partition(listFrom, 500);
                partitionRecordEntityList.forEach(partition -> orderChargeAmountRecordRepository.batchInsert(partition));

                List<List<OrderOperationRecordEntityBo>> partitionList = Lists.partition(recordEntityBoList, 500);
                partitionList.forEach(partition -> orderOperationRecordService.batchInsert(partition));

            }


       /* } catch (Exception e) {
            log.error("自动生成订单数据准备入库失败", e);
            throw e;
        } finally {
            log.info(AUTO_CREATE_ORDER_LOCK_KEY + LOCK_SERVICE_UNLOCK_MUTEX_PARAM_KEY, Optional.ofNullable(mutex).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutex);
        }*/
    }

    /**
     *
     * @param type
     * @param value
     * @param typeEnum OrderTypeEnum的key
     * @param <T>
     * @return
     */
    public <T> Map<Integer, T> queryOrderMap(ComputationalMonthEnum type,Function<OrderEntity, T> value,Integer typeEnum,LocalDate targetDate){
        //月初日期
        LocalDate firstDayOfMonth = targetDate.minusMonths(type.getMonthsToSubtract()).with(TemporalAdjusters.firstDayOfMonth());
        //月末日期
        LocalDate lastDayOfMonth = targetDate.minusMonths(type.getMonthsToSubtract()).with(TemporalAdjusters.lastDayOfMonth());

        List<OrderEntity> orderEntities = orderRepository.getListByCreateTime(firstDayOfMonth, lastDayOfMonth,typeEnum);
        return orderEntities.stream()
                .collect(Collectors.toMap(OrderEntity::getStudentId, value,(o1,o2)->o2));
    }

    public Map<Integer, OrderEntity> queryOrderEntityMap(ComputationalMonthEnum type, OrderTypeEnum typeEnum,LocalDate targetDate){
        //月初日期
        LocalDate firstDayOfMonth = targetDate.minusMonths(type.getMonthsToSubtract()).with(TemporalAdjusters.firstDayOfMonth());
        //月末日期
        LocalDate lastDayOfMonth = targetDate.minusMonths(type.getMonthsToSubtract()).with(TemporalAdjusters.lastDayOfMonth());

        List<OrderEntity> orderEntities = orderRepository.getListByCreateTime(firstDayOfMonth, lastDayOfMonth,typeEnum.getKey());
        //如果出现有多个订单，将多个订单的订单金额字段分别合并；
        return orderEntities.stream()
                .collect(Collectors.toMap(OrderEntity::getStudentId,item ->item,(o1,o2)->{

            o1.setPreviousActualSurplus(BigDecimalUtil.add(o1.getPreviousActualSurplus(),o2.getPreviousActualSurplus()));
            o1.setChargeAmount(BigDecimalUtil.add(o1.getChargeAmount(),o2.getChargeAmount()));

            return o1;
        }));
    }

    public Map<Integer, BigDecimal> queryOrderEntityMap2(ComputationalMonthEnum type, OrderTypeEnum typeEnum,LocalDate targetDate){
        //月初日期
        LocalDate firstDayOfMonth = targetDate.minusMonths(type.getMonthsToSubtract()).with(TemporalAdjusters.firstDayOfMonth());
        //月末日期
        LocalDate lastDayOfMonth = targetDate.minusMonths(type.getMonthsToSubtract()).with(TemporalAdjusters.lastDayOfMonth());

        List<OrderEntity> orderEntities = orderRepository.getListByCreateTime(firstDayOfMonth, lastDayOfMonth,typeEnum.getKey());

        return orderEntities.stream()
                .collect(Collectors.toMap(OrderEntity::getStudentId, OrderEntity::getChargeAmount,(o1, o2) -> {
                    return BigDecimalUtil.add(o1, o2);
                }));
    }


    /**
     * 获取targetDate上个月的余额
     * @param studentIds
     * @param typeEnum
     * @param targetDate
     * @return
     */
    public Map<Integer,BigDecimal> queryPreviousActualSurplus(List<Integer> studentIds,OrderTypeEnum typeEnum,LocalDate targetDate){

        List<OrderMonthlyStatementEntity> entities = orderMonthlyStatementRepository.findListByStudentIdsAndMonth(studentIds, targetDate, typeEnum);
        return entities.stream()
                .collect(Collectors.toMap(OrderMonthlyStatementEntity::getStudentId, OrderMonthlyStatementEntity::getCurrentActualSurplus, (o1, o2) -> {
                    return BigDecimalUtil.add(o1, o2);
                }));
    }

    public Map<Integer,OrderMonthlyStatementEntity> queryOrderMonthlyStatementEntityMap(List<Integer> studentIds,OrderTypeEnum typeEnum,LocalDate targetDate){
        List<OrderMonthlyStatementEntity> entities = orderMonthlyStatementRepository.findListByStudentIdsAndMonth(studentIds, targetDate, typeEnum);
        return entities.stream()
                .collect(Collectors.toMap(OrderMonthlyStatementEntity::getStudentId,Function.identity()));
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void computeSundryCharges(Integer studentId){

        BigDecimal defaultValue = expenseDefaultQueryAppService.getByType(OrderTypeEnum.INCIDENTALS.getKey());

        StudentEntity student = studentRepository.getById(studentId);

        String batchId = UUID.fastUUID().toString(true);

        OrderEntity order = new OrderEntity();
        order.setCode(generateCode(OrderTypeEnum.INCIDENTALS.getKey()));
        order.setType(OrderTypeEnum.INCIDENTALS.getKey());
        order.setStatus(OrderStatusEnum.NORMAL.getKey());
        order.setPaymentStatus(PaymentStatusEnum.NO_PAY.getKey());
        order.setBusinessForms(BusinessUnitEnum.KINDERGARTEN.getKey());
        order.setStudentId(student.getId());
        order.setStudentCode(student.getStudentNo());
        order.setStudentName(student.getStudentName());
        order.setGrade(student.getStudentClass());
        order.setContactInfo(student.getContactPhoneNumber());
        order.setChargingStandard(defaultValue);
        order.setChargeItem(OrderTypeEnum.INCIDENTALS.getDescription());
        order.setChargeAmount(defaultValue);
        order.setBatchCode(batchId);
        order.setPreviousActualSurplus(BigDecimal.ZERO);
        order.setDataSource(OrderSourceEnum.SYSTEM_OPERATION.getKey());
        orderRepository.add(order);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateCode(int type) {
        String codePrefix = orderCodePrefix;
        String dateStr = DateUtil.format(new Date(), "yyyyMMdd");
        String dateTimeStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        String datePrefix = codePrefix + dateStr;
        String dateTimePrefix = codePrefix + dateTimeStr;
        Integer sequence = idSequenceService.incrAndGetSequence(datePrefix);
        return dateTimePrefix + StringUtils.leftPad(sequence.toString(), 4, '0');
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> generateCodes(int type, int increment) {
        String codePrefix = orderCodePrefix;
        String dateStr = DateUtil.format(new Date(), "yyyyMMdd");
        String dateTimeStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        String datePrefix = codePrefix + dateStr;
        String dateTimePrefix = codePrefix + dateTimeStr;
        Integer sequence = idSequenceService.incrAndGetSequence(datePrefix, increment);
        List<String> codes = Lists.newArrayList();
        for (int i = 0; i < increment; i++) {
            String code = dateTimePrefix + StringUtils.leftPad(Integer.toString((sequence - i)), 4, '0');
            codes.add(code);
        }
        Collections.reverse(codes);
        return codes;
    }

    @Override
    public void dinnerOperate(List<Integer> studentIdList,String dateStr){
        log.info("生成餐费订单操作开始-------操作人员：[{}],时间：[{}]",ExtServiceContext.getUserCode(),LocalDate.now());

        Mutex mutex = null;

        try {
            mutex = redisLockService.lock(ORDER_IMPORT_LOCK_KEY, "order");
            log.info("餐费订单生成加锁: "+ORDER_IMPORT_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutex.getLockKey());


            LocalDate targetDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());;
            if(StringUtils.isNotBlank(dateStr)){
                targetDate = LocalDate.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE).with(TemporalAdjusters.firstDayOfMonth());;
            }
            LocalDate currentLastDayOfMonth = targetDate.with(TemporalAdjusters.lastDayOfMonth());
            LocalDate currentFirstDayOfMonth = targetDate.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate previousLastDayOfMonth = targetDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
            LocalDate previousFirstDayOfMonth = targetDate.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
            LocalDate twoMonthFirstDay = targetDate.minusMonths(2).with(TemporalAdjusters.firstDayOfMonth());


            List<StudentEntity> studentEntityList = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(studentIdList)){
                studentEntityList = studentRepository.queryRegisterStudentsByIds(studentIdList);
            }else {
                studentEntityList = studentRepository.queryRegisterStudents();
            }
            List<Integer> studentIds = studentEntityList.stream()
                    .map(StudentEntity::getId)
                    .collect(Collectors.toList());
            //上个月出勤天数
            Map<Integer, Long> attendanceMap = attendanceRecordQueryAppService.countAttendanceMonthByStudentIds(AttendanceRecordFlgEnum.NORMAL.getKey(),
                    Lists.newArrayList(studentIds),previousFirstDayOfMonth);

            long workDaysWithoutColse = calendarQueryAppService.countWorkDaysWithoutColse(targetDate);
            //返回当月工作日期取dd的列表
            List<Integer> dayOfMonthList = calendarQueryAppService.getDayOfMonthList(targetDate);
            List<Integer> dayOfMonthHolidayList = calendarQueryAppService.getDayOfMonthHolidayList(targetDate);


            Map<Integer, Boolean> currentMonthlongTermMap = askedForLeaveQueryAppService.judgementLongTermByStudentIds(studentIds, targetDate);

            Map<Integer, OrderEntity> OrderEntityMap = queryOrderEntityMap(ComputationalMonthEnum.A_MONTH_AGO,OrderTypeEnum.DINNER,targetDate);

            Map<Integer, BigDecimal> previousActualSurplusMap = queryPreviousActualSurplus(studentIds, OrderTypeEnum.DINNER, previousFirstDayOfMonth);
            //上上个月的结余需要从月结表获取
            Map<Integer, BigDecimal> twoMonthActualSurplusMap = queryPreviousActualSurplus(studentIds, OrderTypeEnum.DINNER, twoMonthFirstDay);

            Map<Integer, OrderMonthlyStatementEntity> OrderMonthlyStatementEntityMap = queryOrderMonthlyStatementEntityMap(studentIds, OrderTypeEnum.DINNER, previousFirstDayOfMonth);


            //当月长期病假
            Map<Integer, Boolean> longTermMap = askedForLeaveQueryAppService.judgementLongTermByStudentIds(studentIds, targetDate);


            DateTimeFormatter lastFormate = DateTimeFormatter.ofPattern("yyyy-MM");
            //当前月减免
            Map<Integer, BigDecimal> derateAmountMap = rateReliefQueryAppService.queryMapByStudentIdsAndMonth(studentIds,
                    currentFirstDayOfMonth.format(lastFormate));

            List<String> codes = service.generateCodes(OrderTypeEnum.DINNER.getKey(), studentEntityList.size());
            String batchId = UUID.fastUUID().toString(true);
            BigDecimal everyDayDinner = expenseDefaultQueryAppService.getByType(OrderTypeEnum.DINNER.getKey());

            long currentAllHolidays = calendarQueryAppService.countWinterVacationAndSummerVacationDaysWithoutWeekend(targetDate);

            String previousVerson = calendarQueryAppService.queryCurrentVersion(previousFirstDayOfMonth);
            //这个月日历版本号
            String currentVersion = calendarQueryAppService.queryCurrentVersion(targetDate);

            List<ComputationalOrderAddRespDTO> dtos = new ArrayList<>();
            for(int i=0;i<studentEntityList.size();i++){
                StudentEntity entity = studentEntityList.get(i);
                StudentEntityBo studentEntityBo = new StudentEntityBo();
                BeanUtil.copyProperties(entity,studentEntityBo);

                ComputationalCostsBo bo = new ComputationalCostsBo();
                bo.setStudentBo(studentEntityBo);
                bo.setOrderCode(codes.get(i));
                bo.setBatchId(batchId);
                bo.setChargingStandard(everyDayDinner);
                bo.setAttendanceDay(attendanceMap.getOrDefault(entity.getId(),0L).intValue());
                //未来入园
                boolean flg = currentFirstDayOfMonth.isBefore(entity.getAdmissionDate()) || currentFirstDayOfMonth.isEqual(entity.getAdmissionDate());
                //当月
                boolean currentFlg = currentLastDayOfMonth.isAfter(entity.getAdmissionDate()) || currentLastDayOfMonth.isEqual(entity.getAdmissionDate());

                //当月入园标记
                Boolean currentRegisterFlg = flg && currentFlg;

                // 第一次入园且长期病假的学生，且没有考勤记录,不生成订单，例如：入园日期5月10日，学生状态为长期病假，5月不生成订单
                if(currentRegisterFlg && longTermMap.getOrDefault(entity.getId(),Boolean.FALSE)){
                    continue;
                }


                OrderMonthlyStatementEntity monthlyStatementEntity = OrderMonthlyStatementEntityMap.getOrDefault(entity.getId(),new OrderMonthlyStatementEntity());
                //上上个月结余
                BigDecimal twoMonthAgoSurplus = twoMonthActualSurplusMap.getOrDefault(entity.getId(),BigDecimal.ZERO);
                BigDecimal previousActualSurplus = Optional.ofNullable(monthlyStatementEntity.getCurrentActualSurplus()).orElse(BigDecimal.ZERO);
                BigDecimal previousActual = Optional.ofNullable(monthlyStatementEntity.getActualPayableCharge()).orElse(BigDecimal.ZERO);
                BigDecimal previousChargeAmount = Optional.ofNullable(monthlyStatementEntity.getChargeAmount()).orElse(BigDecimal.ZERO);



                bo.setPreviousRelief(BigDecimal.ZERO);
                bo.setPreviousChargeAmount(previousChargeAmount);
                bo.setTwoMonthAgoSurplus(twoMonthAgoSurplus);
                //上期预缴余额 = 上期订单+上上期余额
                bo.setPreviousRemainingSum(BigDecimalUtil.add(previousChargeAmount,twoMonthAgoSurplus));
                bo.setCurrentRelief(derateAmountMap.getOrDefault(entity.getId(),BigDecimal.ZERO));
                bo.setCurrentAllWeekdays(workDaysWithoutColse);
                bo.setWithoutWeekend(0L);
                bo.setLastMonthCountWithoutWeekend(0L);
                bo.setLastMonthAllHolidays(0L);
                bo.setHolidayAttendanceDays(0L);
                bo.setWeekdayAttendanceDays(0L);
                bo.setDayOfMonthList(dayOfMonthList);
                bo.setCurrentAllHolidays(new BigDecimal(String.valueOf(currentAllHolidays)));
                bo.setCurrentRegisterFlg(currentRegisterFlg);
                bo.setCurrentLongTerm(currentMonthlongTermMap.getOrDefault(entity.getId(),Boolean.FALSE));
                bo.setTargetDate(targetDate);
                bo.setPreviousSurplus(previousActualSurplus);
                bo.setDayOfMonthHolidayList(dayOfMonthHolidayList);
                bo.setPreviousActual(previousActual);


                AutoCalHandler handler = HandlerAutoCalFactory.getInvokeStrategy(OrderTypeEnum.DINNER);
                ComputationalOrderAddRespDTO dto = handler.handler(bo);
                dto.setPreviousVerson(previousVerson);
                dto.setCurrentVersion(currentVersion);
                dtos.add(dto);
            }
            service.batchAddOrderEntityList(dtos);



        } catch (Exception e) {
            log.error("餐费订单自动生成失败", e);
            throw e;
        } finally {
            log.info("餐费订单生成解锁: "+ORDER_IMPORT_LOCK_KEY + LOCK_SERVICE_UNLOCK_MUTEX_PARAM_KEY, Optional.ofNullable(mutex).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutex);
        }
    }

    @Override
    public void childCareOperate(List<Integer> studentIdList,String dateStr){
        log.info("生成保育费订单操作：操作人员：[{}],时间：[{}]",ExtServiceContext.getUserCode(),LocalDate.now());

        Mutex mutex = null;

        try {
            mutex = redisLockService.lock(ORDER_IMPORT_LOCK_KEY, "order");
            log.info("保育费订单生成加锁: "+ORDER_IMPORT_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutex.getLockKey());


            LocalDate targetDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());;
            if(StringUtils.isNotBlank(dateStr)){
                targetDate = LocalDate.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE).with(TemporalAdjusters.firstDayOfMonth());;
            }
            LocalDate lastMonthFirstDay = targetDate.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
            LocalDate twoMonthFirstDay = targetDate.minusMonths(2).with(TemporalAdjusters.firstDayOfMonth());
            LocalDate currentFirstDayOfMonth = targetDate.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate currentLastDayOfMonth = targetDate.with(TemporalAdjusters.lastDayOfMonth());


            List<StudentEntity> studentEntityList = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(studentIdList)){
                studentEntityList = studentRepository.queryRegisterStudentsByIds(studentIdList);
            }else {
                studentEntityList = studentRepository.queryRegisterStudents();
            }





            List<Integer> studentIds = studentEntityList.stream()
                    .map(StudentEntity::getId)
                    .collect(Collectors.toList());

            //   费用标准/每月工作日 = 每日标准保育费，再用 每日标准保育费 * 当月工作日（去除闭园日）
            List<ExpenseStandardRespDTO> expenseStandardRespDTOS = expenseStandardQueryAppService.findListByStudentIdsAndExpenseType(studentIds, ExpenseTypeEnum.CHILD_CARE.getKey());
            Map<Integer, BigDecimal> expenseStandardMap = expenseStandardRespDTOS.stream()
                    .collect(Collectors.toMap(ExpenseStandardRespDTO::getStudentId, ExpenseStandardRespDTO::getExpenseStandard));

            //返回当月工作日期取dd的列表
            List<Integer> dayOfMonthList = calendarQueryAppService.getDayOfMonthList(targetDate);
            List<Integer> dayOfMonthHolidayList = calendarQueryAppService.getDayOfMonthHolidayList(targetDate);
            List<Integer> lastMonthDayOfMonthList = calendarQueryAppService.getDayOfMonthList(lastMonthFirstDay);
            List<Integer> lastMonthDaydayOfMonthHolidayList = calendarQueryAppService.getDayOfMonthHolidayList(lastMonthFirstDay);

            long withoutWeekend = calendarQueryAppService.countWithoutWeekend(targetDate);
            long lastMonthCountWithoutWeekend = calendarQueryAppService.countWithoutWeekend(lastMonthFirstDay);

            //上个月长期病假
            Map<Integer, Boolean> lastMonthlongTermMap = askedForLeaveQueryAppService.judgementLongTermByStudentIds(studentIds, lastMonthFirstDay);
            //当月长期病假
            Map<Integer, Boolean> longTermMap = askedForLeaveQueryAppService.judgementLongTermByStudentIds(studentIds, targetDate);


            //上个月减免
            Map<Integer, BigDecimal> lastMonthderateAmountMap = rateReliefQueryAppService.queryMapByStudentIdsAndMonth(studentIds, lastMonthFirstDay.format(LAST_FORMATE));
            //当前月减免
            Map<Integer, BigDecimal> derateAmountMap = rateReliefQueryAppService.queryMapByStudentIdsAndMonth(studentIds, targetDate.format(LAST_FORMATE));



            long weekDays = calendarQueryAppService.countWeekDays(targetDate);
            long workDaysWithoutColse = calendarQueryAppService.countWorkDaysWithoutColse(targetDate);

            long lastWorkDay = calendarQueryAppService.countWeekDays(lastMonthFirstDay);
            long lastWorkDaysWithoutColse = calendarQueryAppService.countWorkDaysWithoutColse(lastMonthFirstDay);
            long lastMonthAllHolidays = calendarQueryAppService.countWinterVacationAndSummerVacationDaysWithoutWeekend(lastMonthFirstDay);
            long currentAllHolidays = calendarQueryAppService.countWinterVacationAndSummerVacationDaysWithoutWeekend(targetDate);


            Map<Integer, Long> holidayMap = attendanceRecordQueryAppService.countAttendanceMonthByStudentIds(AttendanceRecordFlgEnum.HOLIDAY.getKey(), Lists.newArrayList(studentIds),lastMonthFirstDay);
            //所有出勤天数
            Map<Integer, Long> normalMap = attendanceRecordQueryAppService.countAttendanceMonthByStudentIds(AttendanceRecordFlgEnum.NORMAL.getKey(), Lists.newArrayList(studentIds),lastMonthFirstDay);

            Map<Integer, OrderEntity> OrderEntityMap = queryOrderEntityMap(ComputationalMonthEnum.A_MONTH_AGO,OrderTypeEnum.CHILD_CARE,targetDate);
            // 上个月的结余需要从月结表获取
//            Map<Integer, BigDecimal> previousActualSurplusMap = queryPreviousActualSurplus(studentIds, OrderTypeEnum.CHILD_CARE, lastMonthFirstDay);

            Map<Integer, OrderMonthlyStatementEntity> OrderMonthlyStatementEntityMap = queryOrderMonthlyStatementEntityMap(studentIds, OrderTypeEnum.CHILD_CARE, lastMonthFirstDay);

            // 上上个月的结余需要从月结表获取
            Map<Integer, BigDecimal> twoMonthActualSurplusMap = queryPreviousActualSurplus(studentIds, OrderTypeEnum.CHILD_CARE, twoMonthFirstDay);

            //获取学生第一次有考勤的时间
            Map<Integer, LocalDate> firstDayMap = attendanceRecordQueryAppService.queryStudentFirstDayMap();

            List<String> codes = service.generateCodes(OrderTypeEnum.CHILD_CARE.getKey(), studentEntityList.size());
            //这个月日历版本号
            String currentVersion = calendarQueryAppService.queryCurrentVersion(targetDate);


            String batchId = UUID.fastUUID().toString(true);

            List<ComputationalOrderAddRespDTO> dtos = new ArrayList<>();
            for (int i=0;i<studentEntityList.size();i++){
                StudentEntity studentEntity = studentEntityList.get(i);

                //未来入园
                boolean flg = currentFirstDayOfMonth.isBefore(studentEntity.getAdmissionDate()) || currentFirstDayOfMonth.isEqual(studentEntity.getAdmissionDate());
                //当月
                boolean currentFlg = currentLastDayOfMonth.isAfter(studentEntity.getAdmissionDate()) || currentLastDayOfMonth.isEqual(studentEntity.getAdmissionDate());

                //当月入园标记
                Boolean currentRegisterFlg = flg && currentFlg;
                // 第一次入园且长期病假的学生，且没有考勤记录,不生成订单，例如：入园日期5月10日，学生状态为长期病假，5月不生成订单
                if(currentRegisterFlg && longTermMap.getOrDefault(studentEntity.getId(),Boolean.FALSE)){
                    continue;
                }

                LocalDate firstDay = firstDayMap.getOrDefault(studentEntity.getId(), null);

                ComputationalCostsBo bo =new ComputationalCostsBo();
                bo.setBatchId(batchId);
                bo.setOrderCode(codes.get(i));
                StudentEntityBo studentEntityBo = new StudentEntityBo();
                BeanUtil.copyProperties(studentEntity,studentEntityBo);
                bo.setStudentBo(studentEntityBo);

                bo.setAttendanceDay(normalMap.getOrDefault(studentEntity.getId(),0L).intValue());
                bo.setCurrentRelief(derateAmountMap.getOrDefault(studentEntity.getId(),BigDecimal.ZERO));

                //上上个月结余从结余表获取
                BigDecimal twoMonthAgoSurplus = twoMonthActualSurplusMap.getOrDefault(studentEntity.getId(),BigDecimal.ZERO);

                OrderMonthlyStatementEntity entity = OrderMonthlyStatementEntityMap.getOrDefault(studentEntity.getId(),new OrderMonthlyStatementEntity());

                BigDecimal previousActualSurplus = Optional.ofNullable(entity.getCurrentActualSurplus()).orElse(BigDecimal.ZERO);
                BigDecimal previousActual = Optional.ofNullable(entity.getActualPayableCharge()).orElse(BigDecimal.ZERO);
                BigDecimal previousChargeAmount = Optional.ofNullable(entity.getChargeAmount()).orElse(BigDecimal.ZERO);
                String previousPercentage = entity.getPercentage();
                if(StringUtils.isBlank(entity.getPercentage())){
                    previousPercentage = "假期期间：0%,教学期间：0%";
                }

                bo.setPreviousRelief(lastMonthderateAmountMap.getOrDefault(studentEntity.getId(),BigDecimal.ZERO));
                bo.setPreviousChargeAmount(previousChargeAmount);
                bo.setTwoMonthAgoSurplus(twoMonthAgoSurplus);
                bo.setPreviousActual(previousActual);
                //上期预缴余额 = 上期订单+上上期余额
                bo.setPreviousRemainingSum(BigDecimalUtil.add(previousChargeAmount,twoMonthAgoSurplus));
                bo.setChargingStandard(expenseStandardMap.getOrDefault(studentEntity.getId(),BigDecimal.ZERO));
                bo.setCurrentLongTerm(longTermMap.getOrDefault(studentEntity.getId(),Boolean.FALSE));
                bo.setPreviousLongTerm(lastMonthlongTermMap.getOrDefault(studentEntity.getId(),Boolean.FALSE));
                bo.setHolidayAttendanceDays(holidayMap.getOrDefault(studentEntity.getId(),0L));

                //所有出勤-寒暑假出勤 = 开园日出勤
                Long days = normalMap.getOrDefault(studentEntity.getId(),0L) - holidayMap.getOrDefault(studentEntity.getId(),0L);
                bo.setWeekdayAttendanceDays(days);
                bo.setCurrentAllWeekdays(workDaysWithoutColse);
                bo.setLastMonthAllWeekdays(lastWorkDaysWithoutColse);
                bo.setDayOfMonthList(dayOfMonthList);
                bo.setPreviousPercentage(previousPercentage);

                bo.setWithoutWeekend(withoutWeekend);
                bo.setLastMonthCountWithoutWeekend(lastMonthCountWithoutWeekend);
                bo.setLastMonthDayOfMonthList(lastMonthDayOfMonthList);
                bo.setLastMonthDaydayOfMonthHolidayList(lastMonthDaydayOfMonthHolidayList);
                bo.setLastMonthAllHolidays(lastMonthAllHolidays);
                bo.setCurrentAllHolidays(new BigDecimal(String.valueOf(currentAllHolidays)));
                bo.setDayOfMonthHolidayList(dayOfMonthHolidayList);
                bo.setCurrentRegisterFlg(currentRegisterFlg);
                bo.setTargetDate(targetDate);
                bo.setPreviousSurplus(previousActualSurplus);
                bo.setFirstDay(firstDay);
                bo.setCurrentVersion(currentVersion);


                AutoCalHandler handler = HandlerAutoCalFactory.getInvokeStrategy(OrderTypeEnum.CHILD_CARE);
                ComputationalOrderAddRespDTO dto = handler.handler(bo);
                dtos.add(dto);
            }
            service.batchAddOrderEntityList(dtos);

        } catch (Exception e) {
            log.error("餐费订单生成失败", e);
            throw e;
        } finally {
            log.info("保育费订单生成解锁: "+ORDER_IMPORT_LOCK_KEY + LOCK_SERVICE_UNLOCK_MUTEX_PARAM_KEY, Optional.ofNullable(mutex).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutex);
        }
    }

    /**
     * 月结保育费
     */
    @Override
    public void monthlySettlementOperate(List<Integer> studentIdList, String dateStr){
        log.info("月结保育费操作：操作人员：[{}],时间：[{}]",ExtServiceContext.getUserCode(),LocalDate.now());
        Mutex mutex = null;

        try {
            mutex = redisLockService.lock(ORDER_IMPORT_LOCK_KEY, "order");
            log.info("月结保育费加锁: "+ORDER_IMPORT_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutex.getLockKey());


            LocalDate targetDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
            if(StringUtils.isNotBlank(dateStr)){
                targetDate = LocalDate.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE).with(TemporalAdjusters.firstDayOfMonth());
            }
            LocalDate lastMonthFirstDay = targetDate.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
            LocalDate lastDayOfMonth = targetDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
            LocalDate twoMonthAgoFirstDay = targetDate.minusMonths(2).with(TemporalAdjusters.firstDayOfMonth());


            List<StudentEntity> studentEntityList = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(studentIdList)){
                studentEntityList = studentRepository.queryRegisterStudentsByIds(studentIdList);
            }else {
//                studentEntityList = studentRepository.queryRegisterStudents();
                studentEntityList = studentRepository.queryRegisterStudentsByTarget(lastDayOfMonth);
            }
            List<Integer> studentIds = studentEntityList.stream()
                    .map(StudentEntity::getId)
                    .collect(Collectors.toList());

            //   费用标准/每月工作日 = 每日标准保育费，再用 每日标准保育费 * 当月工作日（去除闭园日）
            List<ExpenseStandardRespDTO> expenseStandardRespDTOS = expenseStandardQueryAppService.findListByStudentIdsAndExpenseType(studentIds, ExpenseTypeEnum.CHILD_CARE.getKey());
            Map<Integer, BigDecimal> expenseStandardMap = expenseStandardRespDTOS.stream()
                    .collect(Collectors.toMap(ExpenseStandardRespDTO::getStudentId, ExpenseStandardRespDTO::getExpenseStandard));

            //返回当月工作日期取dd的列表
            List<Integer> lastMonthDayOfMonthList = calendarQueryAppService.getDayOfMonthList(lastMonthFirstDay);
            List<Integer> lastMonthDaydayOfMonthHolidayList = calendarQueryAppService.getDayOfMonthHolidayList(lastMonthFirstDay);

            long lastMonthCountWithoutWeekend = calendarQueryAppService.countWithoutWeekend(lastMonthFirstDay);

            //上个月长期病假
            Map<Integer, Boolean> lastMonthlongTermMap = askedForLeaveQueryAppService.judgementLongTermByStudentIds(studentIds, lastMonthFirstDay);

            //上个月减免
            Map<Integer, BigDecimal> lastMonthderateAmountMap = rateReliefQueryAppService.queryMapByStudentIdsAndMonth(studentIds, lastMonthFirstDay.format(LAST_FORMATE));


            long lastWorkDaysWithoutColse = calendarQueryAppService.countWorkDaysWithoutColse(lastMonthFirstDay);
            long lastMonthAllHolidays = calendarQueryAppService.countWinterVacationAndSummerVacationDaysWithoutWeekend(lastMonthFirstDay);


            Map<Integer, Long> holidayMap = attendanceRecordQueryAppService.countAttendanceMonthByStudentIds(AttendanceRecordFlgEnum.HOLIDAY.getKey(), Lists.newArrayList(studentIds),lastMonthFirstDay);
            //所有出勤天数
            Map<Integer, Long> normalMap = attendanceRecordQueryAppService.countAttendanceMonthByStudentIds(AttendanceRecordFlgEnum.NORMAL.getKey(), Lists.newArrayList(studentIds),lastMonthFirstDay);

            Map<Integer, OrderEntity> OrderEntityMap = queryOrderEntityMap(ComputationalMonthEnum.A_MONTH_AGO,OrderTypeEnum.CHILD_CARE,targetDate);

            Map<Integer, BigDecimal> twoMonthAgeMap = queryPreviousActualSurplus(studentIds, OrderTypeEnum.CHILD_CARE, twoMonthAgoFirstDay);

            //获取学生第一次有考勤的时间
            Map<Integer, LocalDate> firstDayMap = attendanceRecordQueryAppService.queryStudentFirstDayMap();


            String dateVersion = calendarQueryAppService.queryCurrentVersion(lastMonthFirstDay);
            List<MonthlyStatementBo> boList = new ArrayList<>();

            String batchId = UUID.fastUUID().toString(true);

            for (int i=0;i<studentEntityList.size();i++){
                StudentEntity studentEntity = studentEntityList.get(i);

                ComputationalCostsBo bo =new ComputationalCostsBo();

                StudentEntityBo studentEntityBo = new StudentEntityBo();
                BeanUtil.copyProperties(studentEntity,studentEntityBo);
                bo.setStudentBo(studentEntityBo);
                LocalDate firstDay = firstDayMap.getOrDefault(studentEntity.getId(), null);

                bo.setAttendanceDay(normalMap.getOrDefault(studentEntity.getId(),0L).intValue());
                //上个月订单
                BigDecimal previousChargeAmount = Objects.isNull(OrderEntityMap.get(studentEntity.getId()))
                        ?
                        BigDecimal.ZERO
                        :
                        OrderEntityMap.get(studentEntity.getId()).getChargeAmount();

                BigDecimal twoMonthAgoSurplus = twoMonthAgeMap.getOrDefault(studentEntity.getId(),BigDecimal.ZERO);

                bo.setPreviousRelief(lastMonthderateAmountMap.getOrDefault(studentEntity.getId(),BigDecimal.ZERO));
                bo.setPreviousChargeAmount(previousChargeAmount);
                bo.setTwoMonthAgoSurplus(twoMonthAgoSurplus);
                //上期预缴余额 = 上期订单+上上期余额
                bo.setPreviousRemainingSum(BigDecimalUtil.add(previousChargeAmount,twoMonthAgoSurplus));
                bo.setChargingStandard(expenseStandardMap.getOrDefault(studentEntity.getId(),BigDecimal.ZERO));
                bo.setPreviousLongTerm(lastMonthlongTermMap.getOrDefault(studentEntity.getId(),Boolean.FALSE));
                bo.setHolidayAttendanceDays(holidayMap.getOrDefault(studentEntity.getId(),0L));

                //所有出勤-寒暑假出勤 = 开园日出勤
                Long days = normalMap.getOrDefault(studentEntity.getId(),0L) - holidayMap.getOrDefault(studentEntity.getId(),0L);
                bo.setWeekdayAttendanceDays(days);
                bo.setLastMonthAllWeekdays(lastWorkDaysWithoutColse);
                bo.setLastMonthAllHolidays(lastMonthAllHolidays);
                bo.setLastMonthCountWithoutWeekend(lastMonthCountWithoutWeekend);
                bo.setLastMonthDayOfMonthList(lastMonthDayOfMonthList);
                bo.setLastMonthDaydayOfMonthHolidayList(lastMonthDaydayOfMonthHolidayList);

                bo.setTargetDate(targetDate);
                bo.setCurrentRegisterFlg(false);
                bo.setFirstDay(firstDay);

                AutoCalHandler handler = HandlerAutoCalFactory.getInvokeStrategy(OrderTypeEnum.CHILD_CARE);
                MonthlyStatementBo monthlyStatementBo = handler.monthlyStatement(bo);
                monthlyStatementBo.setCalendarVersion(dateVersion);
                monthlyStatementBo.setBatchCode(batchId);
                boList.add(monthlyStatementBo);
            }
            orderMonthlyStatementAppService.batchInsert(boList);
        } catch (Exception e) {
            log.error("订单月结失败", e);
            throw e;
        } finally {
            log.info("月结保育费解锁: "+ORDER_IMPORT_LOCK_KEY + LOCK_SERVICE_UNLOCK_MUTEX_PARAM_KEY, Optional.ofNullable(mutex).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutex);
        }
    }

    @Override
    public void monthlySettlementForDinnerOperate(List<Integer> studentIdList, String dateStr){
        log.info("月结餐费操作：操作人员：[{}],时间：[{}]",ExtServiceContext.getUserCode(),LocalDate.now());

        Mutex mutex = null;

        try {
            mutex = redisLockService.lock(ORDER_IMPORT_LOCK_KEY, "order");
            log.info("月结餐费加锁: "+ORDER_IMPORT_LOCK_KEY + ":lockService.lock_mutex: [{}]", mutex.getLockKey());


            LocalDate targetDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
            if(StringUtils.isNotBlank(dateStr)){
                targetDate = LocalDate.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE).with(TemporalAdjusters.firstDayOfMonth());
            }
            LocalDate lastMonthFirstDay = targetDate.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
            LocalDate lastDayOfMonth = targetDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());

            List<StudentEntity> studentEntityList = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(studentIdList)){
                studentEntityList = studentRepository.queryRegisterStudentsByIds(studentIdList);
            }else {
                studentEntityList = studentRepository.queryRegisterStudentsByTarget(lastDayOfMonth);
//                studentEntityList = studentRepository.queryRegisterStudents();
            }
            LocalDate previousFirstDayOfMonth = targetDate.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
            LocalDate twoMonthFirstDay = targetDate.minusMonths(2).with(TemporalAdjusters.firstDayOfMonth());

            List<Integer> studentIds = studentEntityList.stream()
                    .map(StudentEntity::getId)
                    .collect(Collectors.toList());
            //结余月出勤天数
            Map<Integer, Long> attendanceMap = attendanceRecordQueryAppService.countAttendanceMonthByStudentIds(AttendanceRecordFlgEnum.NORMAL.getKey(),
                    Lists.newArrayList(studentIds),previousFirstDayOfMonth);

            Map<Integer, Boolean> currentMonthlongTermMap = askedForLeaveQueryAppService.judgementLongTermByStudentIds(studentIds, targetDate);

            Map<Integer, OrderEntity> OrderEntityMap = queryOrderEntityMap(ComputationalMonthEnum.A_MONTH_AGO,OrderTypeEnum.DINNER,targetDate);

            // 上上个月的结余需要从月结表获取
            Map<Integer, BigDecimal> twoMonthActualSurplusMap = queryPreviousActualSurplus(studentIds, OrderTypeEnum.DINNER, twoMonthFirstDay);
            //结余月寒暑假天数
            Map<Integer, Long> holidayMap = attendanceRecordQueryAppService.countAttendanceMonthByStudentIds(AttendanceRecordFlgEnum.HOLIDAY.getKey(), Lists.newArrayList(studentIds),lastMonthFirstDay);
            //所有出勤天数
            Map<Integer, Long> normalMap = attendanceRecordQueryAppService.countAttendanceMonthByStudentIds(AttendanceRecordFlgEnum.NORMAL.getKey(), Lists.newArrayList(studentIds),lastMonthFirstDay);


            BigDecimal everyDayDinner = expenseDefaultQueryAppService.getByType(OrderTypeEnum.DINNER.getKey());

            String dateVersion = calendarQueryAppService.queryCurrentVersion(lastMonthFirstDay);

            List<MonthlyStatementBo> boList = new ArrayList<>();
            String batchId = UUID.fastUUID().toString(true);

            for(int i=0;i<studentEntityList.size();i++){
                StudentEntity entity = studentEntityList.get(i);
                StudentEntityBo studentEntityBo = new StudentEntityBo();
                BeanUtil.copyProperties(entity,studentEntityBo);

                ComputationalCostsBo bo = new ComputationalCostsBo();
                bo.setStudentBo(studentEntityBo);
                bo.setChargingStandard(everyDayDinner);
                bo.setAttendanceDay(attendanceMap.getOrDefault(entity.getId(),0L).intValue());
                //寒暑假出勤
                Long holidayAttendanceDays = holidayMap.getOrDefault(entity.getId(),0L);
                //所有出勤-寒暑假出勤 = 开园日出勤
                Long weekdayAttendanceDays = normalMap.getOrDefault(entity.getId(),0L) - holidayAttendanceDays;

                //上个月预收从上个月订单获取
                BigDecimal previousChargeAmount = Objects.isNull(OrderEntityMap.get(entity.getId()))
                        ?
                        BigDecimal.ZERO
                        :
                        OrderEntityMap.get(entity.getId()).getChargeAmount();
                //上上个月结余
                BigDecimal twoMonthAgoSurplus = twoMonthActualSurplusMap.getOrDefault(entity.getId(),BigDecimal.ZERO);
                //餐费目前没有减免
                bo.setPreviousChargeAmount(previousChargeAmount);
                bo.setTwoMonthAgoSurplus(twoMonthAgoSurplus);
                //上期预缴余额 = 上期订单+上上期余额
                bo.setPreviousRemainingSum(BigDecimalUtil.add(previousChargeAmount,twoMonthAgoSurplus));
                bo.setHolidayAttendanceDays(holidayAttendanceDays);
                bo.setWeekdayAttendanceDays(weekdayAttendanceDays);
                bo.setCurrentRegisterFlg(Boolean.FALSE);
                bo.setTargetDate(targetDate);
                bo.setPreviousLongTerm(currentMonthlongTermMap.getOrDefault(entity.getId(),Boolean.FALSE));

                AutoCalHandler handler = HandlerAutoCalFactory.getInvokeStrategy(OrderTypeEnum.DINNER);
                MonthlyStatementBo monthlyStatementBo = handler.monthlyStatement(bo);
                monthlyStatementBo.setCalendarVersion(dateVersion);
                monthlyStatementBo.setBatchCode(batchId);
                boList.add(monthlyStatementBo);

            }
            orderMonthlyStatementAppService.batchInsert(boList);
        } catch (Exception e) {
            log.error("餐费订单月结失败", e);
            throw e;
        } finally {
            log.info("月结餐费解锁: "+ORDER_IMPORT_LOCK_KEY + LOCK_SERVICE_UNLOCK_MUTEX_PARAM_KEY, Optional.ofNullable(mutex).map(Mutex::getLockKey).orElse(StringUtils.EMPTY));
            redisLockService.unlock(mutex);
        }
    }
}
