package cathayfuture.opm.app.order.service;

import cathayfuture.opm.client.order.api.PaymentDetailAppService;
import cathayfuture.opm.client.order.dto.request.PaymentDetailReqDTO;
import cathayfuture.opm.client.order.dto.response.PaymentDetailRespDTO;
import cathayfuture.opm.domain.order.PaymentDetailEntity;
import cathayfuture.opm.domain.order.enums.PaymentStatusEnum;
import cathayfuture.opm.domain.order.gateway.repository.PaymentDetailRepository;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/29/22
 */
@Service
public class PaymentDetailAppServiceImpl implements PaymentDetailAppService {

    private PaymentDetailRepository paymentDetailRepository;

    public PaymentDetailAppServiceImpl(PaymentDetailRepository paymentDetailRepository) {
        this.paymentDetailRepository = paymentDetailRepository;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentDetailRespDTO add(PaymentDetailReqDTO paymentDetailReqDTO) {
        PaymentDetailEntity paymentDetailEntity = new PaymentDetailEntity();
        BeanUtils.copyProperties(paymentDetailReqDTO, paymentDetailEntity);
        PaymentDetailEntity addedPaymentDetail = paymentDetailRepository.add(paymentDetailEntity);
        PaymentDetailRespDTO result = new PaymentDetailRespDTO();
        BeanUtils.copyProperties(addedPaymentDetail, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PaymentDetailRespDTO> batchAdd(List<PaymentDetailReqDTO> paymentDetailReqDTOList) {
        List<PaymentDetailRespDTO> result = Lists.newArrayList();
        paymentDetailReqDTOList.forEach(paymentDeq -> {
            PaymentDetailRespDTO addedDto = this.add(paymentDeq);
            result.add(addedDto);
        });
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refund(Integer orderId) {
        return paymentDetailRepository.refund(orderId);
    }
}
