package cathayfuture.opm.app.order.service.computationalCosts;

import cathayfuture.opm.client.order.api.AutoCalHandler;
import cathayfuture.opm.client.order.dto.bo.*;
import cathayfuture.opm.client.order.dto.response.ComputationalOrderAddRespDTO;
import cathayfuture.opm.domain.order.enums.*;

import java.math.BigDecimal;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 抽象计算类
 * @date 2023/4/12 16:36
 */
public abstract class AbstractCompute implements AutoCalHandler {

    @Override
    public ComputationalOrderAddRespDTO handler(ComputationalCostsBo bo){

        BigDecimal currentPlan = currentPlan(bo);

//        BigDecimal previousRemainingSum = PreviousRemainingSum(bo);
        //上个月的考勤实际产生费用需要从月结表中获取
        BigDecimal previousActual = bo.getPreviousActual();

        BigDecimal currentRelief = currentRelief(bo);

        //上月余额直接从bo获取，此字段在数据准备时从余额表获取
        BigDecimal previousSurplus = bo.getPreviousSurplus();
        //当月订单
/*        BigDecimal orderAmount = currentPlan.subtract(previousRemainingSum)
                .add(previousActual)
//                .subtract(currentRelief)
                .setScale(4,BigDecimal.ROUND_HALF_UP);*/
//        当月订单 = 本期预计 - 上期结余
        BigDecimal orderAmount = currentPlan.subtract(previousSurplus);

        BuildOrderBo buildRecordBo = new BuildOrderBo();
        buildRecordBo.setBatchId(bo.getBatchId());
        buildRecordBo.setOrderCode(bo.getOrderCode());
        buildRecordBo.setChargingStandard(bo.getChargingStandard());
        buildRecordBo.setCurrentPlan(currentPlan);
        buildRecordBo.setPreviousActual(previousActual);
        buildRecordBo.setCurrentRelief(currentRelief);
        buildRecordBo.setOrderAmount(orderAmount);
        buildRecordBo.setStudentBo(bo.getStudentBo());
        buildRecordBo.setPreviousAttendanceDays((int) bo.getAttendanceDay());
        buildRecordBo.setNextAttendanceDays(bo.getCurrentAllWeekdays().intValue());
        buildRecordBo.setPercentage(bo.getPreviousPercentage());
        buildRecordBo.setPreviousActualSurplus(previousSurplus);
        // 上期预缴余额 = 上月订单金额 + 上上月余额，都从订单表取
        buildRecordBo.setPreviousRemainingSum(bo.getPreviousRemainingSum());
        buildRecordBo.setTargetDate(bo.getTargetDate());
        buildRecordBo.setCurrentLongTerm(bo.getCurrentLongTerm());
        buildRecordBo.setHoliday(bo.getCurrentAllHolidays().intValue());
        buildRecordBo.setWeekday(bo.getCurrentAllWeekdays().intValue());
        buildRecordBo.setVersion(bo.getCurrentVersion());

        ComputationalOrderAddRespDTO dto = buildComputationalOrderAddRespDTO(buildRecordBo);

        OrderChargeAmountRecordBo recoreBo = OrderChargeAmountRecordBo.buildBo(dto.getType(), buildRecordBo);
        dto.setOrderChargeAmountRecordBo(recoreBo);

        return dto;
    }

    /**
     * 本月预计
     * @param bo
     * @return
     */
    protected abstract BigDecimal currentPlan(ComputationalCostsBo bo);
    /**
     * 上月预计余额
     * @param bo
     * @return
     */
    public abstract BigDecimal PreviousRemainingSum(ComputationalCostsBo bo);
    /**
     * 上月实收
     * @param bo
     * @return
     */
    public abstract BigDecimal previousActual(ComputationalCostsBo bo);
    /**
     * 当月减免费用
     * @param bo
     * @return
     */
    public abstract BigDecimal currentRelief(ComputationalCostsBo bo);

    /**
     * 上个月余额
     * @param bo
     * @return
     */
    public abstract  BigDecimal previousSurplus(ComputationalCostsBo bo);

    /**
     * 组建dto
     * @param bo
     * @return
     */
    public abstract ComputationalOrderAddRespDTO buildComputationalOrderAddRespDTO(BuildOrderBo bo);

    /**
     * 获取订单类型
     * @return
     */
    public abstract Integer orderType();

    public abstract MonthlyStatementBo buildHolidaysAndWeekdays(MonthlyStatementBo monthlyStatementBo,ComputationalCostsBo bo);


    protected ComputationalOrderAddRespDTO common(BuildOrderBo bo){
        ComputationalOrderAddRespDTO dto = new ComputationalOrderAddRespDTO();
        dto.setBatchCode(bo.getBatchId());
        dto.setCode(bo.getOrderCode());

        StudentEntityBo studentEntityBo = bo.getStudentBo();
        dto.setStudentId(studentEntityBo.getId());
        dto.setStudentCode(studentEntityBo.getStudentNo());
        dto.setStudentName(studentEntityBo.getStudentName());
        dto.setGrade(studentEntityBo.getStudentClass());
        dto.setContactInfo(studentEntityBo.getContactPhoneNumber());
        dto.setStatus(OrderStatusEnum.NORMAL.getKey());
        //2023.05.23 订单金额是0或者负数，支付状态应该是无需支付
        Integer paymentStatus = PaymentStatusEnum.NO_PAY.getKey();
        if(Optional.ofNullable(bo.getOrderAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO)<=0){
            paymentStatus = PaymentStatusEnum.NEEDLESS.getKey();
        }
        dto.setPaymentStatus(paymentStatus);
        dto.setBusinessForms(BusinessUnitEnum.KINDERGARTEN.getKey());
        //上期预缴余额 = 上月订单金额 + 上上月余额，都从订单表取
        dto.setPreviousRemainingSum(bo.getPreviousRemainingSum());
        dto.setPreviousAttendanceDays(bo.getPreviousAttendanceDays());
        dto.setPreviousActualSurplus(bo.getPreviousActualSurplus());
        dto.setPreviousPayableCharge(bo.getPreviousActual());

        dto.setNextAttendanceDays(bo.getNextAttendanceDays());
        dto.setChargeAmount(bo.getOrderAmount());
        dto.setTargetDate(bo.getTargetDate());
        return dto;
    }


    @Override
    public MonthlyStatementBo monthlyStatement(ComputationalCostsBo bo){
        //实际应缴费用（根据考勤实际应缴费）
        BigDecimal actualPayableCharge = previousActual(bo);
        //当月余额
        BigDecimal currentSurplus = previousSurplus(bo);

        MonthlyStatementBo monthlyStatementBo = MonthlyStatementBo.buildByComputationalCostsBo(bo);
        monthlyStatementBo.setOrderType(orderType());
        monthlyStatementBo.setCurrentActualSurplus(currentSurplus);
        monthlyStatementBo.setActualPayableCharge(actualPayableCharge);

        buildHolidaysAndWeekdays(monthlyStatementBo,bo);

        return monthlyStatementBo;
    }


}
