package cathayfuture.opm.app.order.service.computationalCosts;

import cathayfuture.opm.client.order.dto.bo.BuildOrderBo;
import cathayfuture.opm.client.order.dto.bo.ComputationalCostsBo;
import cathayfuture.opm.client.order.dto.bo.MonthlyStatementBo;
import cathayfuture.opm.client.order.dto.response.ComputationalOrderAddRespDTO;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import cathayfuture.opm.domain.order.enums.ProportionEnum;
import cathayfuture.opm.infra.common.utils.BigDecimalUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 餐费
 * @date 2023/4/17 13:35
 */
@Component
public class Dinner extends AbstractCompute{

    @Override
    public void afterPropertiesSet() throws Exception {
        HandlerAutoCalFactory.register(OrderTypeEnum.DINNER,this);
    }

    @Override
    protected BigDecimal currentPlan(ComputationalCostsBo bo) {
        // 如果是长期病假，收费是0
        if(bo.getCurrentLongTerm()){
            return BigDecimal.ZERO;
        }
        BigDecimal schoolDays = bo.getCurrentAllWeekdays();
        //当月预收费 = 当月天数 * 每日收费标准
        return schoolDays.multiply(bo.getChargingStandard());
    }

    @Override
    public BigDecimal PreviousRemainingSum(ComputationalCostsBo bo) {
        //上个月预收费
        return bo.getPreviousRemainingSum();
    }

    @Override
    public BigDecimal previousActual(ComputationalCostsBo bo) {
        //上个月实收 = 上个月出勤 * 每日收费标准
        BigDecimal daily = bo.getChargingStandard();
        BigDecimal result = new BigDecimal(String.valueOf(bo.getAttendanceDay())).multiply(daily);
        bo.setPreviousActual(result);
        return result;
    }

    @Override
    public BigDecimal currentRelief(ComputationalCostsBo bo) {
        return bo.getCurrentRelief();
    }

    @Override
    public BigDecimal previousSurplus(ComputationalCostsBo bo) {
        BigDecimal sub = BigDecimalUtil.sub(bo.getPreviousRemainingSum(), bo.getPreviousActual());
        return BigDecimalUtil.add(sub,bo.getPreviousRelief());
    }

    @Override
    public ComputationalOrderAddRespDTO buildComputationalOrderAddRespDTO(BuildOrderBo bo) {
        ComputationalOrderAddRespDTO dto = super.common(bo);
        dto.setType(OrderTypeEnum.DINNER.getKey());
        dto.setChargingStandard(bo.getChargingStandard());
        dto.setChargeItem(bo.getTargetDate().getYear()+"年"+ bo.getTargetDate().getMonthValue()+"月预收"+OrderTypeEnum.DINNER.getDescription());
        dto.setPreviousChargePercentage(ProportionEnum.ONE_HUNDRED_PERCENT.getDescription());
        dto.setNextPayableCharge(bo.getCurrentPlan());
        //预收餐费金额  = 下期应预收餐费-上期结余
        dto.setChargeAmount(bo.getCurrentPlan().subtract(bo.getPreviousActualSurplus()));
        // 长期病假下期应出勤天数是0
        if(bo.getCurrentLongTerm()){
            dto.setNextAttendanceDays(BigDecimal.ZERO.intValue());
        }
        return dto;
    }

    @Override
    public Integer orderType() {
        return OrderTypeEnum.DINNER.getKey();
    }

    @Override
    public MonthlyStatementBo buildHolidaysAndWeekdays(MonthlyStatementBo monthlyStatementBo, ComputationalCostsBo bo) {

        monthlyStatementBo.setHolidays(Optional.ofNullable(bo.getHolidayAttendanceDays().intValue()).orElse(0));
        monthlyStatementBo.setWeekdays(Optional.ofNullable(bo.getWeekdayAttendanceDays().intValue()).orElse(0));
        return monthlyStatementBo;
    }


}
