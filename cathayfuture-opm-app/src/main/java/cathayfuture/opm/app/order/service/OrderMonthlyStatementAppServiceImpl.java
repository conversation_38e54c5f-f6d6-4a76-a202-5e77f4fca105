package cathayfuture.opm.app.order.service;

import cathayfuture.opm.client.order.api.OrderMonthlyStatementAppService;
import cathayfuture.opm.client.order.api.OrderOperationRecordService;
import cathayfuture.opm.client.order.dto.bo.MonthlyStatementBo;
import cathayfuture.opm.client.order.dto.bo.OrderOperationRecordEntityBo;
import cathayfuture.opm.domain.attendance.ennums.CommonBooleanEnum;
import cathayfuture.opm.domain.order.OrderMonthlyStatementEntity;
import cathayfuture.opm.domain.order.enums.OrderCreateOperationTypeEnum;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import cathayfuture.opm.domain.order.gateway.repository.OrderMonthlyStatementRepository;
import cathayfuture.opm.infra.common.ExtServiceContext;
import cathayfuture.opm.infra.common.beantools.BeanTools;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 月结
 * @date 2023/5/23 13:40
 */
@Service
@Slf4j
public class OrderMonthlyStatementAppServiceImpl implements OrderMonthlyStatementAppService {

    private OrderMonthlyStatementRepository repository;

    public OrderMonthlyStatementAppServiceImpl(OrderMonthlyStatementRepository repository) {
        this.repository = repository;
    }
    @Resource
    private OrderOperationRecordService orderOperationRecordService;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<MonthlyStatementBo> boList){
        if(CollectionUtil.isEmpty(boList)){
            return;
        }
        List<OrderMonthlyStatementEntity> entities = BeanTools.createListFrom(boList,OrderMonthlyStatementEntity.class);
        for(OrderMonthlyStatementEntity entity : entities){
            entity.setDr(CommonBooleanEnum.NO.getKey());
        }
        //过滤已经存在的数据
        OrderMonthlyStatementEntity newEntity = entities.stream()
                .findFirst()
                .orElse(new OrderMonthlyStatementEntity());
        LocalDate month = newEntity.getMonth();
        Integer orderType = newEntity.getOrderType();
        OrderTypeEnum enumType = OrderTypeEnum.getEnumByKey(orderType);


        Set<Integer> collect = entities.stream()
                .map(OrderMonthlyStatementEntity::getStudentId)
                .collect(Collectors.toSet());
        List<OrderMonthlyStatementEntity> listByStudentIdsAndMonth = repository.findListByStudentIdsAndMonth(new ArrayList<>(collect), month,enumType);

        List<Integer> studentIds = listByStudentIdsAndMonth.stream()
                .map(OrderMonthlyStatementEntity::getStudentId)
                .collect(Collectors.toList());

        entities.removeIf(x->studentIds.contains(x.getStudentId()));
        repository.batchInsert(entities);


        List<OrderOperationRecordEntityBo> recordEntityBoList = new ArrayList<>();
        String userCode = ExtServiceContext.getUserCode();
        Map<String, List<OrderMonthlyStatementEntity>> groupByBatchCode = entities.stream().collect(Collectors.groupingBy(OrderMonthlyStatementEntity::getBatchCode));
        groupByBatchCode.forEach((x,y)->{
            OrderMonthlyStatementEntity entity = y.stream().findAny().orElse(new OrderMonthlyStatementEntity());
            OrderOperationRecordEntityBo recordEntityBo = new OrderOperationRecordEntityBo();
            recordEntityBo.setBatchCode(entity.getBatchCode());
            recordEntityBo.setType(entity.getOrderType());
            recordEntityBo.setOperator(userCode);
            recordEntityBo.setDr(CommonBooleanEnum.NO.getKey());
            recordEntityBo.setOperationType(OrderCreateOperationTypeEnum.MONTHLY_SETTLEMENT.getKey());
            recordEntityBoList.add(recordEntityBo);
        });
        orderOperationRecordService.batchInsert(recordEntityBoList);
    }


    @Override
    public Map<Integer,BigDecimal> queryPreviousActualSurplusMap(List<Integer> list, LocalDate date, Integer orderType){
        Map<Integer,BigDecimal> result =new HashMap<>(list.size());
        List<OrderMonthlyStatementEntity> entity = repository.findListByStudentIdsAndMonth(list, date, OrderTypeEnum.getEnumByKey(orderType));
        if(CollectionUtil.isNotEmpty(entity)){
            result = entity.stream()
                    .collect(Collectors.toMap(OrderMonthlyStatementEntity::getStudentId,OrderMonthlyStatementEntity::getCurrentActualSurplus,(o1,o2)->o2));
        }
        return result;
    }







}
