package cathayfuture.opm.app.order.service.query;

import cathayfuture.opm.client.account.api.query.RAccountStudentQueryAppService;
import cathayfuture.opm.client.account.dto.response.AccountRespDTO;
import cathayfuture.opm.client.account.dto.response.RAccountStudentRespDTO;
import cathayfuture.opm.client.order.api.OrderAppService;
import cathayfuture.opm.client.order.api.query.OrderQueryAppService;
import cathayfuture.opm.client.order.api.query.PaymentDetailQueryAppService;
import cathayfuture.opm.client.order.dto.request.OrderReqDTO;
import cathayfuture.opm.client.order.dto.response.MobileOrderRespDTO;
import cathayfuture.opm.client.order.dto.response.OrderRespDTO;
import cathayfuture.opm.client.order.dto.response.PageRespDTO;
import cathayfuture.opm.client.order.dto.response.PaymentDetailRespDTO;
import cathayfuture.opm.client.order.exception.OrderQueryException;
import cathayfuture.opm.client.student.api.query.StudentQueryAppService;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import cathayfuture.opm.client.student.dto.response.StudentRespDTO;
import cathayfuture.opm.client.utils.WxAccountHolder;
import cathayfuture.opm.domain.order.OrderEntity;
import cathayfuture.opm.domain.order.enums.BusinessUnitEnum;
import cathayfuture.opm.domain.order.enums.OrderStatusEnum;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import cathayfuture.opm.domain.order.enums.PaymentStatusEnum;
import cathayfuture.opm.domain.order.gateway.projection.PaymentProjection;
import cathayfuture.opm.domain.student.enums.StudentTypeEnum;
import cathayfuture.opm.infra.order.repository.mapper.OrderMapper;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderQueryAppServiceImpl implements OrderQueryAppService {

    @Resource
    private RAccountStudentQueryAppService rAccountStudentQueryAppService;
    @Resource
    private StudentQueryAppService studentQueryAppService;
    @Resource
    private PaymentProjection paymentProjection;
    @Resource
    private PaymentDetailQueryAppService paymentDetailQueryAppService;
    @Resource
    private OrderAppService orderAppService;
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    private OrderMapper orderMapper;

    public OrderQueryAppServiceImpl(OrderMapper orderMapper) {
        this.orderMapper = orderMapper;
    }

    @Override
    public PageRespDTO<OrderRespDTO> pageOrder(OrderReqDTO orderReqDTO, int pageNum, int pageSize) {
        LocalDateTime startCreateTime = null;
        LocalDateTime endCreateTime = null;
        LocalDateTime startPaymentTime = null;
        LocalDateTime endPaymentTime = null;
        LocalDateTime startDeleteTime = null;
        LocalDateTime endDeleteTime = null;
        try {
            if (StringUtils.isNotBlank(orderReqDTO.getStartCreateTime())) {
                startCreateTime = LocalDateTime.parse(orderReqDTO.getStartCreateTime(), DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
            }
            if (StringUtils.isNotBlank(orderReqDTO.getEndCreateTime())) {
                endCreateTime = LocalDateTime.parse(orderReqDTO.getEndCreateTime(), DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
            }
            if (StringUtils.isNotBlank(orderReqDTO.getStartPaymentTime())) {
                startPaymentTime = LocalDateTime.parse(orderReqDTO.getStartPaymentTime(), DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
            }
            if (StringUtils.isNotBlank(orderReqDTO.getEndPaymentTime())) {
                endPaymentTime = LocalDateTime.parse(orderReqDTO.getEndPaymentTime(), DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
            }
            if (StringUtils.isNotBlank(orderReqDTO.getStartDeleteTime())) {
                startDeleteTime = LocalDateTime.parse(orderReqDTO.getStartDeleteTime(), DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
            }
            if (StringUtils.isNotBlank(orderReqDTO.getEndDeleteTime())) {
                endDeleteTime = LocalDateTime.parse(orderReqDTO.getEndDeleteTime(), DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
            }

            Page<OrderEntity> orderEntityPage = orderMapper
                    .selectPage(new Page<>(pageNum, pageSize),
                            Wrappers.lambdaQuery(OrderEntity.class)
                                    .orderByDesc(OrderEntity::getCreateTime, OrderEntity::getId)
                                    .like(StringUtils.isNotBlank(orderReqDTO.getStudentName()), OrderEntity::getStudentName, orderReqDTO.getStudentName())
                                    .like(StringUtils.isNotBlank(orderReqDTO.getStudentCode()), OrderEntity::getStudentCode, orderReqDTO.getStudentCode())
                                    .like(StringUtils.isNotBlank(orderReqDTO.getContactInfo()), OrderEntity::getContactInfo, orderReqDTO.getContactInfo())
                                    .like(StringUtils.isNotBlank(orderReqDTO.getChargeItem()), OrderEntity::getChargeItem, orderReqDTO.getChargeItem())
                                    .like(StringUtils.isNotBlank(orderReqDTO.getCode()), OrderEntity::getCode, orderReqDTO.getCode())
                                    .like(StringUtils.isNotBlank(orderReqDTO.getPaymentMode()), OrderEntity::getPaymentMode, orderReqDTO.getPaymentMode())
                                    .eq(Objects.nonNull(orderReqDTO.getType()), OrderEntity::getType, orderReqDTO.getType())
                                    .eq(Objects.nonNull(orderReqDTO.getStatus()), OrderEntity::getStatus, orderReqDTO.getStatus())
                                    .eq(Objects.nonNull(orderReqDTO.getPaymentStatus()), OrderEntity::getPaymentStatus, orderReqDTO.getPaymentStatus())
                                    .ge(Objects.nonNull(startPaymentTime), OrderEntity::getPaymentTime, startPaymentTime)
                                    .le(Objects.nonNull(endPaymentTime), OrderEntity::getPaymentTime, endPaymentTime)
                                    .ge(Objects.nonNull(startCreateTime), OrderEntity::getCreateTime, startCreateTime)
                                    .le(Objects.nonNull(endCreateTime), OrderEntity::getCreateTime, endCreateTime)
                                    .ge(Objects.nonNull(startDeleteTime), OrderEntity::getDeleteTime, startDeleteTime)
                                    .le(Objects.nonNull(endDeleteTime), OrderEntity::getDeleteTime, endDeleteTime));

            PageRespDTO<OrderRespDTO> result = new PageRespDTO<>();
            result.setPageNum(orderEntityPage.getCurrent())
                    .setPageSize(orderEntityPage.getSize())
                    .setTotal(orderEntityPage.getTotal())
                    .setRecords(orderEntityPage.getRecords().stream().map(order -> {
                        OrderRespDTO orderRespDTO = new OrderRespDTO();
                        BeanUtils.copyProperties(order, orderRespDTO);
                        List<PaymentDetailRespDTO> paymentDetailRespDTOList = paymentDetailQueryAppService.listPaymentDetail(order.getId());
                        orderRespDTO.setPaymentDetails(paymentDetailRespDTOList);
                        if (Objects.nonNull(order.getStudentId())) {
                            orderRespDTO.setBirthday(Optional.ofNullable(studentQueryAppService.getById(order.getStudentId())).orElse(new StudentRespDTO()).getBirthday());
                        }
                        return orderRespDTO;
                    }).collect(Collectors.toList()));

            return result;
        } catch (DateTimeParseException e) {
            log.info("查询时间格式错误！异常信息为:[{}]", e.getMessage(), e);
            throw new OrderQueryException("查询时间格式错误！");
        } catch (Exception e) {
            log.info("订单查询错误, 异常信息为:[{}]", e.getMessage(), e);
            throw new OrderQueryException("订单查询错误");
        }
    }

    @Override
    public List<OrderRespDTO> listOrder(OrderReqDTO orderReqDTO) {
        LocalDateTime startCreateTime = null;
        LocalDateTime endCreateTime = null;
        LocalDateTime startPaymentTime = null;
        LocalDateTime endPaymentTime = null;
        if (StringUtils.isNotBlank(orderReqDTO.getStartCreateTime())) {
            startCreateTime = LocalDateTime.parse(orderReqDTO.getStartCreateTime(), DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
        }
        if (StringUtils.isNotBlank(orderReqDTO.getEndCreateTime())) {
            endCreateTime = LocalDateTime.parse(orderReqDTO.getEndCreateTime(), DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
        }
        if (StringUtils.isNotBlank(orderReqDTO.getStartPaymentTime())) {
            startPaymentTime = LocalDateTime.parse(orderReqDTO.getStartPaymentTime(), DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
        }
        if (StringUtils.isNotBlank(orderReqDTO.getEndPaymentTime())) {
            endPaymentTime = LocalDateTime.parse(orderReqDTO.getEndPaymentTime(), DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
        }

        List<OrderEntity> orderEntityList = orderMapper
                .selectList(Wrappers.lambdaQuery(OrderEntity.class)
                        .orderByDesc(OrderEntity::getCreateTime, OrderEntity::getId)
                        .like(StringUtils.isNotBlank(orderReqDTO.getStudentName()), OrderEntity::getStudentName, orderReqDTO.getStudentName())
                        .like(StringUtils.isNotBlank(orderReqDTO.getStudentCode()), OrderEntity::getStudentCode, orderReqDTO.getStudentCode())
                        .like(StringUtils.isNotBlank(orderReqDTO.getContactInfo()), OrderEntity::getContactInfo, orderReqDTO.getContactInfo())
                        .like(StringUtils.isNotBlank(orderReqDTO.getChargeItem()), OrderEntity::getChargeItem, orderReqDTO.getChargeItem())
                        .like(StringUtils.isNotBlank(orderReqDTO.getCode()), OrderEntity::getCode, orderReqDTO.getCode())
                        .like(StringUtils.isNotBlank(orderReqDTO.getPaymentMode()), OrderEntity::getPaymentMode, orderReqDTO.getPaymentMode())
                        .eq(Objects.nonNull(orderReqDTO.getType()), OrderEntity::getType, orderReqDTO.getType())
                        .eq(Objects.nonNull(orderReqDTO.getStatus()), OrderEntity::getStatus, orderReqDTO.getStatus())
                        .eq(Objects.nonNull(orderReqDTO.getPaymentStatus()), OrderEntity::getPaymentStatus, orderReqDTO.getPaymentStatus())
                        .ge(Objects.nonNull(startPaymentTime), OrderEntity::getPaymentTime, startPaymentTime)
                        .le(Objects.nonNull(endPaymentTime), OrderEntity::getPaymentTime, endPaymentTime)
                        .ge(Objects.nonNull(startCreateTime), OrderEntity::getCreateTime, startCreateTime)
                        .le(Objects.nonNull(endCreateTime), OrderEntity::getCreateTime, endCreateTime));

        Map<Integer, StudentRespDTO> studentsMap = studentQueryAppService.list(new StudentReqDTO()).stream().collect(Collectors.toMap(StudentRespDTO::getId, Function.identity(), (o, n) -> o));
        Map<Integer, List<PaymentDetailRespDTO>> paymentDetailsMap = paymentDetailQueryAppService.listPaymentDetails(orderEntityList
                        .stream()
                        .filter(order -> Objects.equals(PaymentStatusEnum.PAID.getKey(), order.getPaymentStatus()))
                        .map(OrderEntity::getId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(PaymentDetailRespDTO::getOrderId, detail -> {
                    List<PaymentDetailRespDTO> details = Lists.newArrayList();
                    details.add(detail);
                    return details;
                }, (List<PaymentDetailRespDTO> val1, List<PaymentDetailRespDTO> val2) -> {
                    val1.addAll(val2);
                    return val1;
                }));

        return orderEntityList.stream().map(orderEntity -> {
            OrderRespDTO orderRespDTO = new OrderRespDTO();
            BeanUtils.copyProperties(orderEntity, orderRespDTO);
            List<PaymentDetailRespDTO> paymentDetailRespDTOList = paymentDetailsMap.get(orderEntity.getId());
            orderRespDTO.setPaymentDetails(paymentDetailRespDTOList);
            if (Objects.nonNull(orderEntity.getStudentId())) {
                orderRespDTO.setBirthday(Optional.ofNullable(studentsMap.get(orderEntity.getStudentId())).orElse(new StudentRespDTO()).getBirthday());
            }
            return orderRespDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public PageRespDTO<MobileOrderRespDTO> pageOrderForMobile(Integer status, Integer pageNum, Integer pageSize) {
        PageRespDTO<MobileOrderRespDTO> result = new PageRespDTO<>();
        AccountRespDTO currentUser = WxAccountHolder.get();
        List<RAccountStudentRespDTO> rAccountStudentRespDTOS = rAccountStudentQueryAppService.listByAccountId(currentUser.getId());
        if (CollectionUtils.isEmpty(rAccountStudentRespDTOS)) {
            return result;
        }
        LambdaQueryWrapper<OrderEntity> queryWrapper = Wrappers.lambdaQuery(OrderEntity.class)
                .in(OrderEntity::getStudentId, rAccountStudentRespDTOS.stream().map(RAccountStudentRespDTO::getStudentId).collect(Collectors.toList()))
                .orderByDesc(OrderEntity::getUpdateTime);
        if (Objects.equals(status, PaymentStatusEnum.NO_PAY.getKey())) {
            queryWrapper.eq(OrderEntity::getPaymentStatus, PaymentStatusEnum.NO_PAY.getKey());
            queryWrapper.eq(OrderEntity::getStatus, OrderStatusEnum.NORMAL.getKey());
        } else if (Objects.equals(status, PaymentStatusEnum.PAID.getKey())) {
            //todo: 2023.05.23 订单金额是0或者负数，支付状态应该是无需支付,已缴费栏中应该可以查出无需支付的数据
            queryWrapper.in(OrderEntity::getPaymentStatus, Lists.newArrayList(PaymentStatusEnum.PAID.getKey(), PaymentStatusEnum.REFUND.getKey(),PaymentStatusEnum.NEEDLESS.getKey()));
            queryWrapper.eq(OrderEntity::getStatus, OrderStatusEnum.NORMAL.getKey());
        }else {
            queryWrapper.eq(OrderEntity::getStatus, OrderStatusEnum.DELETED.getKey());
        }
        Page<OrderEntity> orderEntityPage = orderMapper.selectPage(
                new Page<>(pageNum, pageSize),
                queryWrapper
        );
        Map<Integer, StudentRespDTO> studentMap;
        if (!CollectionUtils.isEmpty(orderEntityPage.getRecords())) {
            List<StudentRespDTO> studentList = studentQueryAppService.listByIds(new ArrayList<>(orderEntityPage.getRecords().stream().map(OrderEntity::getStudentId).collect(Collectors.toSet())));
            studentMap = studentList.stream().collect(Collectors.toMap(StudentRespDTO::getId, Function.identity()));
        } else {
            studentMap = new HashMap<>();
        }
        result.setPageNum(orderEntityPage.getCurrent())
                .setPageSize(orderEntityPage.getSize())
                .setTotal(orderEntityPage.getTotal())
                .setRecords(orderEntityPage.getRecords().stream().map(order -> {
                    MobileOrderRespDTO orderRespDTO = new MobileOrderRespDTO();
                    BeanUtils.copyProperties(order, orderRespDTO);
                    orderRespDTO.setOrderType(BusinessUnitEnum.getEnumDescription(order.getBusinessForms()) + "-" + OrderTypeEnum.getEnumDescription(order.getType()));
                    orderRespDTO.setBirthday(Optional.ofNullable(studentMap.get(order.getStudentId())).map(StudentRespDTO::getBirthday).orElse(StringUtils.EMPTY));
                    if (Objects.equals(status, PaymentStatusEnum.NO_PAY.getKey())) {
                        boolean canPrePay = Objects.isNull(order.getPayer()) || Objects.equals(currentUser.getId(), order.getPayer());
                        orderRespDTO.setShowPayButton(canPrePay);
                        orderRespDTO.setShowReplaceButton(canPrePay && Objects.nonNull(order.getPayer()));
                    } else {
                        orderRespDTO.setShowPayButton(false);
                        orderRespDTO.setShowReplaceButton(false);
                    }
                    return orderRespDTO;
                }).collect(Collectors.toList()));
        return result;
    }

    @Override
    public MobileOrderRespDTO queryOrderById(Integer orderId) {
        OrderEntity orderEntity = orderMapper.selectById(orderId);
        MobileOrderRespDTO result = new MobileOrderRespDTO();
        BeanUtils.copyProperties(orderEntity, result);
        StudentRespDTO student = studentQueryAppService.getById(orderEntity.getStudentId());
        result.setBirthday(Optional.ofNullable(student).map(StudentRespDTO::getBirthday).orElse(null));
        result.setOrderType(BusinessUnitEnum.getEnumDescription(orderEntity.getBusinessForms()) + "-" + OrderTypeEnum.getEnumDescription(orderEntity.getType()));
        result.setPaymentStatus(PaymentStatusEnum.getEnumDescription(orderEntity.getPaymentStatus()));
        result.setPaymentTime(Optional.ofNullable(orderEntity.getPaymentTime()).map(t -> t.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).orElse(null));
        return result;
    }

    @Override
    public Integer queryOrderPaymentStatusById(Integer orderId) {
        OrderEntity orderEntity = orderMapper.selectById(orderId);
        Integer paymentStatus = orderEntity.getPaymentStatus();
        if (Objects.equals(paymentStatus, PaymentStatusEnum.NO_PAY) && Objects.nonNull(orderEntity.getUpdateTime()) && orderEntity.getUpdateTime().before(DateUtil.offsetSecond(DateUtil.date(), -5))) {
            ThreadUtil.execute(() -> {
                log.info("开始查询订单支付结果，orderId[{}]", orderId);
                String result = paymentProjection.queryPayResult(orderEntity);
                log.info("结束查询订单支付结果，orderId[{}]，result[{}]", orderId, result);
                if (StringUtils.isNotEmpty(result)) {
                    JSONObject resultObj = JSONObject.parseObject(result);
                    orderAppService.payComplete(resultObj.getString("tenantOrderNo"), resultObj.getString("orderStatus"), resultObj.getBigDecimal("payAmount"), resultObj.getString("tripartiteOrderNo"));
                }
            });
        }
        return paymentStatus;
    }

    @Override
    public OrderRespDTO queryOrder(Integer orderId) {
        OrderRespDTO orderRespDTO = new OrderRespDTO();
        OrderEntity orderEntity = orderMapper.selectById(orderId);
        BeanUtils.copyProperties(orderEntity, orderRespDTO);
        StudentRespDTO student = studentQueryAppService.getById(orderRespDTO.getStudentId());
        orderRespDTO.setBirthday(Optional.ofNullable(student).map(StudentRespDTO::getBirthday).orElse(null));
        List<PaymentDetailRespDTO> paymentDetailRespDTOList = paymentDetailQueryAppService.listPaymentDetail(orderId);
        orderRespDTO.setPaymentDetails(paymentDetailRespDTOList);
        return orderRespDTO;
    }

    @Override
    public Integer notPayOrderCount() {
        AccountRespDTO currentUser = WxAccountHolder.get();
        List<RAccountStudentRespDTO> rAccountStudentRespDTOS = rAccountStudentQueryAppService.listByAccountId(currentUser.getId());
        if (CollectionUtils.isEmpty(rAccountStudentRespDTOS)) {
            return 0;
        }
        LambdaQueryWrapper<OrderEntity> queryWrapper = Wrappers.lambdaQuery(OrderEntity.class)
                .in(OrderEntity::getStudentId, rAccountStudentRespDTOS.stream().map(RAccountStudentRespDTO::getStudentId).collect(Collectors.toList()))
                .eq(OrderEntity::getPaymentStatus, PaymentStatusEnum.NO_PAY.getKey())
                .eq(OrderEntity::getStatus, OrderStatusEnum.NORMAL.getKey());
        return orderMapper.selectCount(queryWrapper);
    }

    @Override
    public Integer queryPrePayAndPayStatus(Integer orderId) {
        OrderEntity orderEntity = orderMapper.selectById(orderId);
        if (Objects.equals(orderEntity.getPaymentStatus(), PaymentStatusEnum.PAID.getKey())) {
            return 1;
        }
        if (Objects.isNull(orderEntity.getPayer()) || Objects.equals(orderEntity.getPayer(), WxAccountHolder.get().getId())) {
            return 0;
        }
        return -1;
    }

    @Override
    public List<OrderRespDTO> queryOrderTemplate(Integer orderType) {

        Integer studentType;
        if (Integer.valueOf(OrderTypeEnum.INCIDENTALS.getKey()).equals(orderType)){
            studentType = StudentTypeEnum.NOT_SIGNED.getKey();
        }else{
            studentType = StudentTypeEnum.SIGNED.getKey();
        }

        List<StudentRespDTO> studentRespDTOS = studentQueryAppService.listByType(studentType);
        return studentRespDTOS.stream().map(student -> {
            OrderRespDTO orderRespDTO = new OrderRespDTO();
            orderRespDTO.setStudentId(student.getId());
            orderRespDTO.setBirthday(student.getBirthday());
            orderRespDTO.setGrade(student.getStudentClass());
            orderRespDTO.setStudentName(student.getStudentName());
            orderRespDTO.setStudentCode(student.getStudentNo());
            orderRespDTO.setContactInfo(student.getContactPhoneNumber());
            orderRespDTO.setChargingStandard(BigDecimal.ZERO);
            orderRespDTO.setChargeItem(null);
            orderRespDTO.setPreviousRemainingSum(BigDecimal.ZERO);
            orderRespDTO.setPreviousAttendanceDays(0);
            orderRespDTO.setPreviousChargePercentage("0");
            orderRespDTO.setPreviousPayableCharge(BigDecimal.ZERO);
            orderRespDTO.setNextAttendanceDays(0);
            orderRespDTO.setNextPayableCharge(BigDecimal.ZERO);
            orderRespDTO.setChargeAmount(BigDecimal.ZERO);
            orderRespDTO.setDetailRemark(null);
            orderRespDTO.setRemark(null);
            return orderRespDTO;
        }).collect(Collectors.toList());
    }


    @Override
    public List<OrderRespDTO> queryOrderListByStudentIdListAndTypeAndPaymentStatus(List<Integer> studentIds, Integer type, List<Integer> paymentStatusList){
        List<OrderEntity> orderEntities = orderMapper.selectList(Wrappers.lambdaQuery(OrderEntity.class)
                .eq(Objects.nonNull(type), OrderEntity::getType, type)
                .in(CollectionUtil.isNotEmpty(paymentStatusList),OrderEntity::getPaymentStatus, paymentStatusList)
                .in(CollectionUtil.isNotEmpty(studentIds), OrderEntity::getStudentId, studentIds)
                .eq(OrderEntity::getStatus,OrderStatusEnum.NORMAL.getKey())
        );

        return orderEntities.stream().map(x->{
            OrderRespDTO respDTO = new OrderRespDTO();
            respDTO.setId(x.getId());
            respDTO.setType(x.getType());
            respDTO.setStatus(x.getStatus());
            respDTO.setPaymentStatus(x.getPaymentStatus());
            respDTO.setStudentId(x.getStudentId());
            return respDTO;
        }).collect(Collectors.toList());
    }

}
