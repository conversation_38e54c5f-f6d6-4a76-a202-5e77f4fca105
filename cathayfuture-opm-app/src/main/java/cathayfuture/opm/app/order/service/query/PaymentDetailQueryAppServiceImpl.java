package cathayfuture.opm.app.order.service.query;

import cathayfuture.opm.client.order.api.query.PaymentDetailQueryAppService;
import cathayfuture.opm.client.order.dto.response.PaymentDetailRespDTO;
import cathayfuture.opm.domain.order.PaymentDetailEntity;
import cathayfuture.opm.domain.order.enums.PaymentStatusEnum;
import cathayfuture.opm.infra.order.repository.mapper.PaymentDetailMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/26/22
 */
@Service
public class PaymentDetailQueryAppServiceImpl implements PaymentDetailQueryAppService {

    private PaymentDetailMapper paymentDetailMapper;

    public PaymentDetailQueryAppServiceImpl(PaymentDetailMapper paymentDetailMapper) {
        this.paymentDetailMapper = paymentDetailMapper;
    }

    @Override
    public List<PaymentDetailRespDTO> listPaymentDetail(Integer orderId) {
        List<PaymentDetailEntity> paymentDetailEntities = paymentDetailMapper
                .selectList(Wrappers.lambdaQuery(PaymentDetailEntity.class)
                        .eq(PaymentDetailEntity::getOrderId, orderId)
                        .and(o -> o.eq(PaymentDetailEntity::getPaymentStatus, PaymentStatusEnum.PAID.getKey()).or().eq(PaymentDetailEntity::getPaymentStatus, PaymentStatusEnum.REFUND.getKey())));

        return paymentDetailEntities.stream().map(paymentDetailEntity -> {
            PaymentDetailRespDTO paymentDetailRespDTO = new PaymentDetailRespDTO();
            BeanUtils.copyProperties(paymentDetailEntity, paymentDetailRespDTO);
            return paymentDetailRespDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<PaymentDetailRespDTO> listPaymentDetails(List<Integer> orderIds) {
        List<PaymentDetailEntity> paymentDetailEntities = Lists.newArrayList();
        Lists.partition(orderIds, 500).forEach(orderIdList -> {
            paymentDetailEntities.addAll(paymentDetailMapper
                    .selectList(Wrappers.lambdaQuery(PaymentDetailEntity.class)
                            .in(PaymentDetailEntity::getOrderId, orderIdList)
                            .and(o -> o.eq(PaymentDetailEntity::getPaymentStatus, PaymentStatusEnum.PAID.getKey()).or().eq(PaymentDetailEntity::getPaymentStatus, PaymentStatusEnum.REFUND.getKey()))));
        });

        return paymentDetailEntities.stream().map(paymentDetailEntity -> {
            PaymentDetailRespDTO paymentDetailRespDTO = new PaymentDetailRespDTO();
            BeanUtils.copyProperties(paymentDetailEntity, paymentDetailRespDTO);
            return paymentDetailRespDTO;
        }).collect(Collectors.toList());
    }
}
