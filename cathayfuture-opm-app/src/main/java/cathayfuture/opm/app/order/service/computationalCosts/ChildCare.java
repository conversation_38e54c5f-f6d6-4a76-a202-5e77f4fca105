package cathayfuture.opm.app.order.service.computationalCosts;

import cathayfuture.opm.client.order.dto.bo.BuildOrderBo;
import cathayfuture.opm.client.order.dto.bo.ComputationalCostsBo;
import cathayfuture.opm.client.order.dto.bo.MonthlyStatementBo;
import cathayfuture.opm.client.order.dto.bo.OrderChargeAmountRecordBo;
import cathayfuture.opm.client.order.dto.response.ComputationalOrderAddRespDTO;
import cathayfuture.opm.domain.attendance.ennums.AttendanceRecordFlgEnum;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import cathayfuture.opm.domain.order.enums.ProportionEnum;
import cathayfuture.opm.infra.common.utils.BigDecimalUtil;
import cn.hutool.core.util.NumberUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 保育费
 * @date 2023/4/17 15:04
 */
@Component
public class ChildCare extends AbstractCompute{

    @Override
    public void afterPropertiesSet() throws Exception {
        HandlerAutoCalFactory.register(OrderTypeEnum.CHILD_CARE,this);
    }

    @Override
    protected BigDecimal currentPlan(ComputationalCostsBo bo) {
        //本月预计（没有考虑上月结余的情况）

        BigDecimal withoutWeekend  = new BigDecimal(String.valueOf(bo.getWithoutWeekend()));
        //每日保育费
        BigDecimal daily = bo.getChargingStandard()
                .divide(new BigDecimal(String.valueOf(bo.getWithoutWeekend())), 4, BigDecimal.ROUND_HALF_UP);

        // 月初寒暑假应该按照20%计费
        BigDecimal result = BigDecimal.ZERO;
        //费用比例
        BigDecimal weekdayPercentage = ProportionEnum.ONE_HUNDRED_PERCENT.getValue();
        BigDecimal holidayPercentage = ProportionEnum.TWENTY_PERCENT.getValue();
        if(bo.getCurrentLongTerm()){
            //长期病假
            weekdayPercentage = ProportionEnum.TWENTY_PERCENT.getValue();
            if(!bo.hasAttendance()){
                weekdayPercentage = ProportionEnum.ZERO_PERCENT.getValue();
                holidayPercentage = ProportionEnum.ZERO_PERCENT.getValue();
            }
        }

        BigDecimal currentAllHolidays = bo.getCurrentAllHolidays();
        //寒暑假
        BigDecimal holiday = daily.multiply(bo.getCurrentAllHolidays())
                .multiply(holidayPercentage);

        BigDecimal currentAllWeekdays = bo.getCurrentAllWeekdays();
        //开园日
        BigDecimal weekday = daily.multiply(bo.getCurrentAllWeekdays())
                .multiply(weekdayPercentage);
        //计划 = 开园日 + 寒暑假
        result = BigDecimalUtil.add(weekday,holiday);

        return result;
    }

    @Override
    public BigDecimal PreviousRemainingSum(ComputationalCostsBo bo) {
        return bo.getPreviousRemainingSum();
    }




    @Override
    public BigDecimal previousActual(ComputationalCostsBo bo) {
        if(bo.getCurrentRegisterFlg()){
            return BigDecimal.ZERO;
        }
        //上月实际（没有考虑结余情况）
        BigDecimal c = new BigDecimal(String.valueOf(bo.getLastMonthCountWithoutWeekend()));
        //每日费用
        BigDecimal daily = bo.getChargingStandard()
                .divide(new BigDecimal(String.valueOf(bo.getLastMonthCountWithoutWeekend())),4,BigDecimal.ROUND_HALF_UP);
        //寒暑假比例
        ProportionEnum holidayProportionEnum = null;
        //开园日比例
        ProportionEnum weekdayProportionEnum = null;
        Set<String> percentageListStr = new LinkedHashSet<>();
        //上个月实际
        BigDecimal result = BigDecimal.ZERO;

        //上月实际
        if(bo.getPreviousLongTerm()){
            //长期病假：没有出勤整月按 20% 计算，如果有出勤，需要把对应的部分（寒暑假或者开学日）按照100%计算；（需要记录考勤，用于补齐额外费用）
            //寒暑假
            holidayProportionEnum = ProportionEnum.getProportionByLongTerm(
                    BigDecimalUtil.add(bo.getHolidayAttendanceDays(),bo.getWeekdayAttendanceDays()),
                    bo.getHolidayAttendanceDays(),
                    AttendanceRecordFlgEnum.HOLIDAY,
                    bo.getLastMonthAllHolidaysForPreviousActual()
                    );
            BigDecimal holidayAmountPreviousLongTerm = bo.getLastMonthAllHolidaysForPreviousActual()
                    .multiply(holidayProportionEnum.getValue())
                    .multiply(daily);


            //开园日
            weekdayProportionEnum = ProportionEnum.getProportionByLongTerm(
                    BigDecimalUtil.add(bo.getHolidayAttendanceDays(),bo.getWeekdayAttendanceDays()),
                    bo.getWeekdayAttendanceDays(),
                    AttendanceRecordFlgEnum.NORMAL,
                    bo.getLastMonthAllWeekdaysForPreviousActual()
                    );
            BigDecimal weekdayAmountPreviousLongTerm = bo.getLastMonthAllWeekdaysForPreviousActual()
                    .multiply(weekdayProportionEnum.getValue())
                    .multiply(daily);

            result = BigDecimalUtil.add(holidayAmountPreviousLongTerm,weekdayAmountPreviousLongTerm);


            bo.setPreviousActual(result);
            bo.setPreviousPercentage("假期期间："+holidayProportionEnum.getDescription()+",教学期间："+weekdayProportionEnum.getDescription());
            return result;
        }
        //计算比例
        holidayProportionEnum = ProportionEnum
                .getHolidayProportion(bo.getHolidayAttendanceDays(),bo.getStudentBo().getAdmissionDate(),
                        bo.getTargetDate(),bo.getLastMonthAllHolidaysForPreviousActual());

        weekdayProportionEnum = ProportionEnum.getWeekdayProportion(bo.getWeekdayAttendanceDays(),bo.getStudentBo().getAdmissionDate(),
                bo.getTargetDate(),bo.getLastMonthAllWeekdaysForPreviousActual());

        percentageListStr.add("假期期间："+holidayProportionEnum.getDescription());
        percentageListStr.add("教学期间："+weekdayProportionEnum.getDescription());

        BigDecimal a = bo.getLastMonthAllHolidaysForPreviousActual();
        //寒暑假
        BigDecimal holidayAmount = bo.getLastMonthAllHolidaysForPreviousActual()
                .multiply(holidayProportionEnum.getValue())
                .multiply(daily);

        BigDecimal b = bo.getLastMonthAllWeekdaysForPreviousActual();
        //开园日
        BigDecimal weekdayAmount = bo.getLastMonthAllWeekdaysForPreviousActual()
                .multiply(weekdayProportionEnum.getValue())
                .multiply(daily);
        //寒假费用+开园日费用-上个月减免
        result = holidayAmount.add(weekdayAmount);
        bo.setPreviousActual(result);
        bo.setPreviousPercentage("假期期间："+holidayProportionEnum.getDescription()+",教学期间："+weekdayProportionEnum.getDescription());
        return result;
    }

    @Override
    public BigDecimal currentRelief(ComputationalCostsBo bo) {
        return Optional.ofNullable(bo.getCurrentRelief()).orElse(BigDecimal.ZERO);
    }

    @Override
    public BigDecimal previousSurplus(ComputationalCostsBo bo) {
        //上个月余额 = 上个月订单金额 + 上上个月余额 - 上个月实际 - 减免
        //上期预缴余额 = 上个月订单金额 + 上上个月余额
    /*    return Optional.ofNullable(bo.getPreviousRemainingSum()).orElse(BigDecimal.ZERO)
                .subtract(Optional.ofNullable(bo.getPreviousActual()).orElse(BigDecimal.ZERO));*/
        BigDecimal sub = BigDecimalUtil.sub(bo.getPreviousRemainingSum(), bo.getPreviousActual());
        return BigDecimalUtil.add(sub,bo.getPreviousRelief());
    }


    @Override
    public ComputationalOrderAddRespDTO buildComputationalOrderAddRespDTO(BuildOrderBo bo) {
        ComputationalOrderAddRespDTO dto = super.common(bo);

        dto.setType(OrderTypeEnum.CHILD_CARE.getKey());
        dto.setChargingStandard(bo.getChargingStandard());
        dto.setChargeItem(bo.getTargetDate().getYear()+"年"+ bo.getTargetDate().getMonthValue()+"月预收"+OrderTypeEnum.CHILD_CARE.getDescription());
        dto.setPreviousChargePercentage(bo.getPercentage());
        dto.setNextPayableCharge(bo.getCurrentPlan());
        return dto;
    }

    @Override
    public Integer orderType() {
        return OrderTypeEnum.CHILD_CARE.getKey();
    }

    @Override
    public MonthlyStatementBo buildHolidaysAndWeekdays(MonthlyStatementBo monthlyStatementBo, ComputationalCostsBo bo) {
        monthlyStatementBo.setHolidays(Optional.of(bo.getLastMonthAllHolidaysForPreviousActual().intValue()).orElse(0));
        monthlyStatementBo.setWeekdays(Optional.of(bo.getLastMonthAllWeekdaysForPreviousActual().intValue()).orElse(0));
        return monthlyStatementBo;
    }


}
