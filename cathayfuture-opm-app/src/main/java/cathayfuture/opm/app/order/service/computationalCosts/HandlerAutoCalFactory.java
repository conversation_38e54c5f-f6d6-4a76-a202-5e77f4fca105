package cathayfuture.opm.app.order.service.computationalCosts;

import cathayfuture.opm.client.order.api.AutoCalHandler;
import cathayfuture.opm.domain.order.enums.OrderTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 策略工厂
 * @date 2023/4/18 14:28
 */
@Component
public class HandlerAutoCalFactory {

    private static Map<OrderTypeEnum, AutoCalHandler> map = new HashMap<>();

    public static void register(OrderTypeEnum key,AutoCalHandler handler){
        if(Objects.isNull(key)||Objects.isNull(handler)){
            return;
        }
        map.put(key,handler);
    }

    public static AutoCalHandler getInvokeStrategy(OrderTypeEnum key){
        return map.get(key);
    }
}
