package cathayfuture.opm.app.order.service;

import cathayfuture.opm.client.order.api.OrderOperationRecordService;
import cathayfuture.opm.client.order.dto.bo.OrderOperationRecordEntityBo;
import cathayfuture.opm.client.order.exception.OrderCreateException;
import cathayfuture.opm.domain.order.OrderOperationRecordEntity;
import cathayfuture.opm.domain.order.gateway.repository.OrderOperationRecordRepository;
import cathayfuture.opm.infra.common.beantools.BeanTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/6/9 16:47
 */
@Service
@Slf4j
public class OrderOperationRecordServiceImpl implements OrderOperationRecordService {

    private OrderOperationRecordRepository repository;

    public OrderOperationRecordServiceImpl(OrderOperationRecordRepository repository) {
        this.repository = repository;
    }

    @Override
    public void batchInsert(List<OrderOperationRecordEntityBo> boList){
        List<OrderOperationRecordEntity> list = BeanTools.createListFrom(boList, OrderOperationRecordEntity.class);
        Boolean boo = repository.batchInsert(list);
        if(!boo){
            throw new OrderCreateException("写入操作人表失败");
        }

    }



}
