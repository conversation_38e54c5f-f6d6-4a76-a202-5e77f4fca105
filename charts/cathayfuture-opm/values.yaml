apiVersion: v1
# Default values for register-server.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  # 应用docker镜像地址
  repository: registry.dtyunxi.cn/operation-${gitGroup}/cathayfuture-opm
  pullPolicy: IfNotPresent

config:
  port: 18080

env:
  envFrom:
    cm: common
  open:
    APP_OPTS: >-
      -Dfile.encoding=utf-8
      -Dspring.profiles.active=${PROFILES_ACTIVE}
      -Dspring.cloud.nacos.discovery.server-addr=${DISCOVERY_SERVER}
      -Dspring.cloud.nacos.discovery.namespace=${DISCOVERY_NAMESPACE}
      -Dspring.cloud.nacos.config.server-addr=${CONFIG_SERVER}
      -Dspring.cloud.nacos.config.namespace=${CONFIG_NAMESPACE}

logs:
  parser: docker
livenessProbe:
  path: "/actuator/health"
  port: 8080
  initialDelaySeconds: 40
  periodSeconds: 30

readinessProbe:
  path: "/actuator/health"
  port: 8080
  initialDelaySeconds: 30
  periodSeconds: 30
resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources,such as Minikube. If you do want to specify resources,uncomment the following
  # lines,adjust them as necessary,and remove the curly braces after 'resources:'.
  limits:
    # cpu: 100m
    memory: 500Mi
  requests:
    # cpu: 100m
    memory: 256Mi

service:
  name: cathayfuture-tuition
  enabled: false
  type: NodePort
  ports:
    #需要根据实际调整
    port: 18080
    #需要根据实际调整
    targetPort: 18080
    #需要根据实际调整
    nodePort: 30111
