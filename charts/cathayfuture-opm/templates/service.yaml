{{- if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.service.name }}
  labels:
{{ include "service.labels.standard" . | indent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.ports.port }}
      {{ if eq .Values.service.type "NodePort" }}
      nodePort: {{ .Values.service.ports.nodePort }}
      {{ end }}
      targetPort: {{ .Values.service.ports.targetPort }}
      protocol: TCP
      name: http
  selector:
{{ include "service.labels.standard" . | indent 4 }}
{{- end }}