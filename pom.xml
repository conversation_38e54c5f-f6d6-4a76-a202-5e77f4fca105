<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>cathayfuture.opm</groupId>
  <artifactId>cathayfuture-opm</artifactId>
  <packaging>pom</packaging>
  <version>1.0-SNAPSHOT</version>
  <modules>
    <module>cathayfuture-opm-domain</module>
    <module>cathayfuture-opm-infra</module>
    <module>cathayfuture-opm-app</module>
    <module>cathayfuture-opm-client</module>
    <module>cathayfuture-opm-adapter</module>
  </modules>

  <properties>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <mysql.version>5.1.47</mysql.version>
    <guava.version>21.0</guava.version>
    <hibernate-validator.version>6.0.18.Final</hibernate-validator.version>
    <hutool.version>4.6.1</hutool.version>
    <spring-cloud-starter-alibaba-nacos.version>2.1.2.RELEASE</spring-cloud-starter-alibaba-nacos.version>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <mybatis-plus-boot-starter.version>3.4.2</mybatis-plus-boot-starter.version>
    <mybatis-plus-annotation.version>3.4.2</mybatis-plus-annotation.version>
    <jsqlparser.version>3.1</jsqlparser.version>
    <pagehelper.version>5.1.8</pagehelper.version>
    <weixin-java-miniapp.version>4.4.0</weixin-java-miniapp.version>
    <java-jwt.version>4.0.0</java-jwt.version>
  </properties>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.1.4.RELEASE</version>
    <relativePath/>
  </parent>

  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>${mysql.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis-plus-boot-starter.version}</version>
      </dependency>

      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator</artifactId>
        <version>${hibernate-validator.version}</version>

      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>Greenwich.SR5</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool.version}</version>
      </dependency>

      <!-- 移除nacos依赖，使用Spring Security替代 -->

      <dependency>
        <groupId>com.github.pagehelper</groupId>
        <artifactId>pagehelper</artifactId>
        <version>${pagehelper.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.jsqlparser</groupId>
        <artifactId>jsqlparser</artifactId>
        <version>${jsqlparser.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-annotation</artifactId>
        <version>${mybatis-plus-annotation.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-core</artifactId>
        <version>${mybatis-plus-annotation.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-extension</artifactId>
        <version>${mybatis-plus-annotation.version}</version>
      </dependency>

      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>weixin-java-miniapp</artifactId>
        <version>${weixin-java-miniapp.version}</version>
      </dependency>

      <dependency>
        <groupId>com.auth0</groupId>
        <artifactId>java-jwt</artifactId>
        <version>${java-jwt.version}</version>
      </dependency>
      <dependency>
        <groupId>com.taslyware</groupId>
        <artifactId>taslyware-excel-tools</artifactId>
        <version>1.0.0</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-cache</artifactId>
        <version>2.2.1.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
        <version>2.2.1.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.dtyunxi.cube</groupId>
        <artifactId>cube-starter-lock</artifactId>
        <version>2.4.3-RC-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.dtyunxi.huieryun</groupId>
        <artifactId>huieryun-lockredis</artifactId>
        <version>1.1.8-ENERGY-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
        <version>2.1.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>2.17.2</version>
      </dependency>
      <!-- 移除微服务依赖，使用本地认证替代 -->
      <!-- Spring Security依赖由spring-boot-starter-parent自动管理版本 -->

      <!--OSS-->
      <dependency>
        <groupId>com.dtyunxi.huieryun</groupId>
        <artifactId>huieryun-starter-objectstorage</artifactId>
        <version>1.1.8.3-ENERGY-RELEASE</version>
      </dependency>
     <!-- ali sms -->
      <dependency>
        <groupId>com.aliyun</groupId>
        <artifactId>tea-openapi</artifactId>
        <version>0.2.2</version>
      </dependency>
      <dependency>
        <groupId>com.aliyun</groupId>
        <artifactId>dysmsapi20170525</artifactId>
        <version>2.0.18</version>
      </dependency>

      <dependency>
        <groupId>com.dtyunxi.cube</groupId>
        <artifactId>cube-starter-cache</artifactId>
        <version>2.4.3-RC-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>redis.clients</groupId>
        <artifactId>jedis</artifactId>
        <version>3.1.0</version>
      </dependency>
    </dependencies>
  </dependencyManagement>


  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
