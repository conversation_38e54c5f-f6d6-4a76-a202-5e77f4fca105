2025-08-12 16:48:44,144 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App v1.0-SNAPSHOT on chengzhx-MBP with PID 77008 (/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/cathayfuture-opm.jar started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm)
2025-08-12 16:48:44,149 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: dev
2025-08-12 16:48:45,539 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 16:48:45,540 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 16:48:45,589 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 37ms. Found 0 repository interfaces.
2025-08-12 16:48:45,950 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$d65a19a7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 16:48:46,228 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 16:48:46,243 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 16:48:46,252 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 16:48:46,252 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 16:48:46,329 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 16:48:46,329 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 2137 ms
2025-08-12 16:48:47,471 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 16:48:47,695 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 16:48:47,694 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-12 16:48:48,391 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 65 ms to scan 5 urls, producing 127 keys and 453 values 
2025-08-12 16:48:48,683 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-12 16:48:48,699 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-12 16:48:48,699 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-12 16:48:48,699 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-12 16:48:48,787 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7ed49a7f, org.springframework.security.web.context.SecurityContextPersistenceFilter@14c99bf6, org.springframework.security.web.header.HeaderWriterFilter@5aa781f2, org.springframework.security.web.authentication.logout.LogoutFilter@6ffd4c0d, cathayfuture.opm.adapter.security.JwtAuthenticationFilter@5ef6fd7f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@38883a31, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@541afb85, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1cd6b1bd, org.springframework.security.web.session.SessionManagementFilter@7e3236d, org.springframework.security.web.access.ExceptionTranslationFilter@11b5f4e2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7742a45c]
2025-08-12 16:48:49,051 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 16:48:49,436 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 16:48:49,507 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 16:48:49,520 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 16:48:49,522 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 6.008 seconds (JVM running for 6.474)
2025-08-12 16:48:53,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 16:57:01,967 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App v1.0-SNAPSHOT on chengzhx-MBP with PID 83479 (/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/cathayfuture-opm.jar started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm)
2025-08-12 16:57:01,971 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-12 16:57:03,389 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 16:57:03,391 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 16:57:03,441 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 41ms. Found 0 repository interfaces.
2025-08-12 16:57:03,884 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$5d2d89d1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 16:57:04,180 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 16:57:04,197 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 16:57:04,207 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 16:57:04,208 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 16:57:04,293 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 16:57:04,293 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 2285 ms
2025-08-12 16:57:05,347 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 16:57:05,548 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-12 16:57:05,548 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-12 16:57:06,440 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 64 ms to scan 5 urls, producing 127 keys and 453 values 
2025-08-12 16:57:06,714 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-12 16:57:06,729 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-12 16:57:06,729 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-12 16:57:06,729 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-12 16:57:06,816 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3c88191b, org.springframework.security.web.context.SecurityContextPersistenceFilter@739831a4, org.springframework.security.web.header.HeaderWriterFilter@21a46ff1, org.springframework.security.web.authentication.logout.LogoutFilter@19ae2ee5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@38a4e2b0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@27a6fef2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@74960e9d, org.springframework.security.web.session.SessionManagementFilter@3830f918, org.springframework.security.web.access.ExceptionTranslationFilter@195113de, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6e24ce51]
2025-08-12 16:57:07,072 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 16:57:07,473 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 16:57:07,540 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 16:57:07,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 16:57:07,554 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 6.33 seconds (JVM running for 6.729)
2025-08-12 16:57:15,824 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 16:57:24,527 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App v1.0-SNAPSHOT on chengzhx-MBP with PID 84330 (/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/cathayfuture-opm.jar started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm)
2025-08-12 16:57:24,531 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-12 16:57:25,901 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 16:57:25,903 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 16:57:25,950 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 39ms. Found 0 repository interfaces.
2025-08-12 16:57:26,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$728ebc14] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 16:57:26,595 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 16:57:26,611 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 16:57:26,618 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 16:57:26,619 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 16:57:26,697 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 16:57:26,698 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 2126 ms
2025-08-12 16:57:27,643 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 16:57:27,838 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-12 16:57:27,839 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-12 16:57:28,748 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 68 ms to scan 5 urls, producing 127 keys and 453 values 
2025-08-12 16:57:29,021 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-12 16:57:29,036 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-12 16:57:29,036 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-12 16:57:29,036 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-12 16:57:29,116 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2fc49538, org.springframework.security.web.context.SecurityContextPersistenceFilter@541afb85, org.springframework.security.web.header.HeaderWriterFilter@7e3236d, org.springframework.security.web.authentication.logout.LogoutFilter@8c12524, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7db40fd5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@44b940a2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@670342a2, org.springframework.security.web.session.SessionManagementFilter@7fe8c7db, org.springframework.security.web.access.ExceptionTranslationFilter@5efe47fd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7926352f]
2025-08-12 16:57:29,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 16:57:29,747 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 16:57:29,822 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 16:57:29,833 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 16:57:29,834 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 5.978 seconds (JVM running for 6.305)
2025-08-12 16:57:49,741 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 16:57:49,741 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-12 16:57:49,749 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 8 ms
2025-08-12 16:57:49,790 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-12 16:57:49,915 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-12 16:58:41,125 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-12 16:58:41,147 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-12 16:58:41,152 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-12 17:20:41,444 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App v1.0-SNAPSHOT on chengzhx-MBP with PID 96428 (/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/cathayfuture-opm.jar started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm)
2025-08-12 17:20:41,446 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat2
2025-08-12 17:20:42,787 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 17:20:42,789 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 17:20:42,836 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 38ms. Found 0 repository interfaces.
2025-08-12 17:20:43,209 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$80e84fe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 17:20:43,461 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 17:20:43,476 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 17:20:43,482 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 17:20:43,483 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 17:20:43,551 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 17:20:43,551 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 2070 ms
2025-08-12 17:20:43,965 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-12 17:20:43,993 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-12 17:21:17,149 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App v1.0-SNAPSHOT on chengzhx-MBP with PID 97366 (/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/cathayfuture-opm.jar started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm)
2025-08-12 17:21:17,152 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: prod
2025-08-12 17:21:18,508 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-12 17:21:18,510 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-12 17:21:18,557 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 39ms. Found 0 repository interfaces.
2025-08-12 17:21:18,939 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$319d7548] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-12 17:21:19,185 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-12 17:21:19,200 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-12 17:21:19,208 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-12 17:21:19,208 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-12 17:21:19,299 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-12 17:21:19,299 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 2113 ms
2025-08-12 17:21:20,329 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-12 17:21:20,501 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.r.cluster.ClusterConnectionManager [L:125] - Redis cluster nodes configuration got from **********/**********:6379:
29f39d5699c641c8cd54dde6813aefcbe70e2f46 **********:6379@16379 master - 0 1754990478525 3 connected 10923-16383
2100af0f16b6585b6740022fabb6748ec9f3e440 **********:6379@16379 slave c5835329cceffd5ef7fb9e3ac23d53beaa0ac1c5 0 1754990479527 6 connected
da1f4d721027dd8c75cdc082391c7db7ab392023 **********:6379@16379 slave 29f39d5699c641c8cd54dde6813aefcbe70e2f46 0 1754990477000 4 connected
5175b6dc3962939a3d14e94a5460b2e254d2f294 10.9.1.143:6379@16379 master - 0 1754990480529 7 connected 0-5460
c5835329cceffd5ef7fb9e3ac23d53beaa0ac1c5 10.9.1.140:6379@16379 master - 0 1754990479000 2 connected 5461-10922
70083a7f07043d28ab806820a67ff7dd8ac92b50 **********:6379@16379 myself,slave 5175b6dc3962939a3d14e94a5460b2e254d2f294 0 1754990479000 1 connected

2025-08-12 17:21:20,519 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-5] o.r.cluster.ClusterConnectionManager [L:242] - slaves: [redis://**********:6379] added for slot ranges: [[5461-10922]]
2025-08-12 17:21:20,520 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.cluster.ClusterConnectionManager [L:242] - slaves: [redis://**********:6379] added for slot ranges: [[0-5460]]
2025-08-12 17:21:20,519 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.cluster.ClusterConnectionManager [L:242] - slaves: [redis://**********:6379] added for slot ranges: [[10923-16383]]
2025-08-12 17:21:20,540 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-21] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for **********/**********:6379
2025-08-12 17:21:20,540 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-18] o.r.c.pool.SlaveConnectionPool [L:168] - 1 connections initialized for **********/**********:6379
2025-08-12 17:21:20,540 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-28] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for 10.9.1.140/10.9.1.140:6379
2025-08-12 17:21:20,540 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-27] o.r.c.pool.SlaveConnectionPool [L:168] - 1 connections initialized for **********/**********:6379
2025-08-12 17:21:20,540 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-17] o.r.c.pool.SlaveConnectionPool [L:168] - 1 connections initialized for **********/**********:6379
2025-08-12 17:21:20,540 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-22] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for 10.9.1.143/10.9.1.143:6379
2025-08-12 17:21:20,541 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-2] o.r.c.pool.PubSubConnectionPool [L:168] - 1 connections initialized for **********/**********:6379
2025-08-12 17:21:20,542 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-23] o.r.c.pool.PubSubConnectionPool [L:168] - 1 connections initialized for **********/**********:6379
2025-08-12 17:21:20,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-20] o.r.cluster.ClusterConnectionManager [L:263] - master: redis://**********:6379 added for slot ranges: [[10923-16383]]
2025-08-12 17:21:20,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-20] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for **********/**********:6379
2025-08-12 17:21:20,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-3] o.r.cluster.ClusterConnectionManager [L:263] - master: redis://10.9.1.140:6379 added for slot ranges: [[5461-10922]]
2025-08-12 17:21:20,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-4] o.r.cluster.ClusterConnectionManager [L:263] - master: redis://10.9.1.143:6379 added for slot ranges: [[0-5460]]
2025-08-12 17:21:20,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-3] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for 10.9.1.140/10.9.1.140:6379
2025-08-12 17:21:20,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-4] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for 10.9.1.143/10.9.1.143:6379
2025-08-12 17:21:20,553 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-5] o.r.c.pool.PubSubConnectionPool [L:168] - 1 connections initialized for **********/**********:6379
2025-08-12 17:21:21,452 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 69 ms to scan 5 urls, producing 128 keys and 454 values 
2025-08-12 17:21:21,809 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-12 17:21:21,835 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-12 17:21:21,835 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-12 17:21:21,835 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-12 17:21:21,919 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7493d937, org.springframework.security.web.context.SecurityContextPersistenceFilter@62058742, org.springframework.security.web.header.HeaderWriterFilter@710ae6a7, org.springframework.security.web.authentication.logout.LogoutFilter@7b211077, cathayfuture.opm.adapter.security.JwtAuthenticationFilter@40bb4f87, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@36df4c26, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2fee69a1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5dc7841c, org.springframework.security.web.session.SessionManagementFilter@bb6f3f7, org.springframework.security.web.access.ExceptionTranslationFilter@3ea48c37, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@45cd8607]
2025-08-12 17:21:22,161 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-12 17:21:22,575 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-12 17:21:22,645 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-12 17:21:22,657 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-12 17:21:22,659 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 6.125 seconds (JVM running for 6.466)
2025-08-12 17:21:24,194 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
