2025-08-12 17:20:43,971 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.loader.WebappClassLoaderBase [L:173] - The web application [ROOT] appears to have started a thread named [lettuce-eventExecutorLoop-1-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@11.0.19/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@11.0.19/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:234)
 java.base@11.0.19/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2123)
 java.base@11.0.19/java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:458)
 io.netty.util.concurrent.SingleThreadEventExecutor.takeTask(SingleThreadEventExecutor.java:251)
 io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:64)
 io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:905)
 io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base@11.0.19/java.lang.Thread.run(Thread.java:829)
2025-08-12 17:20:43,974 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
