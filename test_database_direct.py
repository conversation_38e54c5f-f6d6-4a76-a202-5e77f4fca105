#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接测试数据库连接和用户数据
"""

import requests
import json

def test_user_management_api():
    """测试用户管理API来检查数据库数据"""
    print("=== 测试用户管理API ===")
    
    base_url = "http://localhost:8080"
    
    # 首先尝试获取用户列表（可能需要认证）
    endpoints_to_test = [
        "/bms/system/user/enabled",
        "/bms/system/user/list", 
        "/actuator/health",
        "/actuator/info"
    ]
    
    for endpoint in endpoints_to_test:
        url = f"{base_url}{endpoint}"
        print(f"测试端点: {endpoint}")
        
        try:
            response = requests.get(url, timeout=5)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("  ✅ 访问成功")
                try:
                    data = response.json()
                    print(f"  响应数据: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
                except:
                    print(f"  响应内容: {response.text[:200]}...")
            elif response.status_code == 401:
                print("  ⚠️ 需要认证")
            elif response.status_code == 403:
                print("  ⚠️ 权限不足")
            elif response.status_code == 404:
                print("  ❌ 端点不存在")
            else:
                print(f"  ❌ 其他错误: {response.text[:100]}")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
        
        print()

def create_test_user():
    """尝试创建测试用户"""
    print("=== 尝试创建测试用户 ===")
    
    base_url = "http://localhost:8080"
    create_url = f"{base_url}/bms/system/user"
    
    # 测试用户数据
    test_user = {
        "username": "testuser",
        "userCode": "TEST001",
        "password": "test123",
        "realName": "测试用户",
        "oaName": "测试用户OA",
        "email": "<EMAIL>",
        "status": 1
    }
    
    try:
        response = requests.post(
            create_url,
            json=test_user,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"创建用户状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 测试用户创建成功")
            return True
        elif response.status_code == 401:
            print("⚠️ 需要管理员权限创建用户")
        else:
            print("❌ 创建用户失败")
            
    except Exception as e:
        print(f"❌ 创建用户异常: {e}")
    
    return False

def test_login_with_simple_password():
    """测试使用简单密码登录"""
    print("=== 测试简单密码登录 ===")
    
    base_url = "http://localhost:8080"
    login_url = f"{base_url}/bms/auth/login"
    
    # 如果我们刚创建了测试用户，尝试用它登录
    test_cases = [
        {"username": "testuser", "password": "test123"},
        {"username": "admin", "password": "admin"},  # 尝试简单密码
        {"username": "admin", "password": "123456"}, # 尝试常见密码
    ]
    
    for test_case in test_cases:
        print(f"测试登录: {test_case['username']} / {test_case['password']}")
        
        try:
            response = requests.post(
                login_url,
                json=test_case,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("  ✅ 登录成功")
                try:
                    data = response.json()
                    if data.get("token"):
                        print(f"  Token: {data['token'][:30]}...")
                        return data["token"]  # 返回token用于后续测试
                except:
                    pass
            else:
                print("  ❌ 登录失败")
                try:
                    data = response.json()
                    if data.get("error"):
                        print(f"  错误: {data['error']}")
                except:
                    print(f"  响应: {response.text}")
                    
        except Exception as e:
            print(f"  ❌ 异常: {e}")
        
        print()
    
    return None

def check_application_properties():
    """检查应用配置"""
    print("=== 检查应用配置信息 ===")
    
    base_url = "http://localhost:8080"
    
    # 尝试访问配置信息端点
    config_endpoints = [
        "/actuator/env",
        "/actuator/configprops", 
        "/actuator/info",
        "/actuator/health/db"
    ]
    
    for endpoint in config_endpoints:
        url = f"{base_url}{endpoint}"
        print(f"检查: {endpoint}")
        
        try:
            response = requests.get(url, timeout=5)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    # 查找数据库相关配置
                    if "datasource" in str(data).lower() or "database" in str(data).lower():
                        print("  ✅ 找到数据库配置信息")
                    if "cathay.security" in str(data):
                        print("  ✅ 找到安全配置信息")
                except:
                    pass
            elif response.status_code == 404:
                print("  ❌ 端点不存在")
            else:
                print(f"  ⚠️ 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
        
        print()

def main():
    """主函数"""
    print("数据库直接测试")
    print("=" * 50)
    
    # 检查应用配置
    check_application_properties()
    
    # 测试用户管理API
    test_user_management_api()
    
    # 尝试创建测试用户
    created = create_test_user()
    
    # 测试登录
    token = test_login_with_simple_password()
    
    print("=" * 50)
    print("测试总结:")
    if token:
        print("✅ 找到可用的登录凭据")
    else:
        print("❌ 所有登录尝试都失败")
        print("可能的问题:")
        print("1. 数据库中没有用户数据")
        print("2. 密码哈希不正确")
        print("3. 数据库连接问题")
        print("4. 认证服务配置问题")

if __name__ == "__main__":
    main()
