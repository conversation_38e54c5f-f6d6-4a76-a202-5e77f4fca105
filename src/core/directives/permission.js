/**
 * 按钮权限指令
 * 获取当前路由的meta中的permissions与传入的权限做对比，如果无权限，则不append到dom上。
 */
import store from '@/store'
import router from '@/router'
import Vue from 'vue'

let permissions = []

const findCurrentPermissionsByName = (name, routers) => {
  if (routers.length === 0) return
  for (let i = 0; i < routers.length; i++) {
    if (routers[i].name === name) {
      permissions = routers[i].meta.permissions || null
    } else {
      if (routers[i].children && Array.isArray(routers[i].children)) {
        findCurrentPermissionsByName(name, routers[i].children)
      }
    }
  }
}

function renderDomEvent(el, binding) {
  const { value } = binding
  // const permissions = store.getters && store.getters.permissions
  // 当前页面所有按钮权限
  const allRouters = store.getters && store.getters.addRouters
  // console.log('router ->', router)
  const name = router.history.current.name
  findCurrentPermissionsByName(name, allRouters)
  // const permissions = router.history.current.meta.permissions
  if (value && value instanceof Array && value.length > 0) {
    // 当前按钮所需权限
    const pm = value

    const hasPermission = permissions.some(role => {
      return pm.includes(role)
    })

    if (!hasPermission) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  } else {
    throw new Error(`need page rescode! Like v-permission="$route.meta.permissionResCode"`)
  }
}
let permission = {
  inserted(el, binding, vnode) {
    renderDomEvent(el, binding)
  },
  update(el, binding) {
    renderDomEvent(el, binding)
  }
}
Vue.directive('permission', permission)
