// eslint-disable-next-line
import { UserLayout, TslUserLayout, BasicLayout, BlankLayout } from '@/layouts'
import { bxAnaalyse } from '@/core/icons'

const RouteView = {
  name: 'RouteView',
  render: h => h('router-view')
}

export const asyncRouterMap = [
  {
    path: '/',
    name: 'index',
    component: BasicLayout,
    redirect: '/home',
    children: [
      // {
      //   path: '/home',
      //   name: 'home',
      //   meta: { title: '首页', icon: '' },
      //   component: () => import('@/views/common/home.vue')
      // },
      // {
      //   path: '/student-info-manage',
      //   name: 'studentInfoManage',
      //   component: () => import('@/views/studentInfoManage/index.vue'),
      //   meta: { title: '学生信息管理', icon: 'idcard' }
      // },
      // {
      //   path: '/order-management',
      //   name: 'orderManagement',
      //   redirect: '/order-management/conservation-fee',
      //   component: RouteView,
      //   meta: { title: '订单管理', icon: 'profile' },
      //   children: [
      //     {
      //       path: '/order-management/conservation-fee',
      //       name: 'conservationFee',
      //       component: () => import('@/views/orderManagement/conservationFee/index.vue'),
      //       meta: { title: '保育费订单', icon: '' }
      //     },
      //     {
      //       path: '/order-management/food-bill',
      //       name: 'foodBill',
      //       component: () => import('@/views/orderManagement/foodBill/index.vue'),
      //       meta: { title: '餐费订单', icon: '' }
      //     },
      //     {
      //       path: '/order-management/charge-bill',
      //       name: 'chargeBill',
      //       component: () => import('@/views/orderManagement/chargeBill/index.vue'),
      //       meta: { title: '代收费订单', icon: '' }
      //     },
      //     {
      //       path: '/order-management/deposit-bill',
      //       name: 'depositBill',
      //       component: () => import('@/views/orderManagement/depositBill/index.vue'),
      //       meta: { title: '定金订单', icon: '' }
      //     },
      //     {
      //       path: '/order-management/incidental-bill',
      //       name: 'incidentalBill',
      //       component: () => import('@/views/orderManagement/incidentalBill/index.vue'),
      //       meta: { title: '杂费订单', icon: '' }
      //     }
      //   ]
      // },
      // example
      {
        hidden: true,
        path: '/example',
        redirect: '/example/form-framework',
        component: RouteView,
        meta: { title: '示例', icon: 'form', permission: ['form'] },
        children: [
          {
            path: '/example/form-framework',
            name: 'formFramework',
            component: () => import('@/views/example/formFramework/index.vue'),
            meta: { title: 'FormFramework示例', keepAlive: true, permission: ['form'] }
          },
          {
            path: '/example/more-state-solutions',
            name: 'moreStateSolutions',
            component: () => import('@/views/example/moreStateSolutions/index.vue'),
            meta: { title: '多状态解决方案示例', keepAlive: true, permission: ['form'] }
          },
          {
            path: '/example/table-demo',
            name: 'tableDemo',
            component: () => import('@/views/example/table/index.vue'),
            meta: { title: '表格示例', keepAlive: true, permission: ['form'] }
          },
          {
            path: '/example/refresh',
            name: 'refresh',
            hidden: true,
            component: () => import('@/views/example/refresh/index.vue'),
            meta: { title: '重新载入', keepAlive: true, permission: ['form'] }
          },
          {
            path: '/example/status-demo',
            name: 'statusDemo',
            component: () => import('@/views/example/status/index.vue'),
            meta: { title: '状态demo', keepAlive: true, permission: ['form'] }
          }
        ]
      },
      // Exception
      {
        path: '/exception',
        name: 'exception',
        component: RouteView,
        hidden: true,
        redirect: '/exception/403',
        meta: { title: 'menu.exception', icon: 'warning', permission: ['exception'] },
        children: [
          {
            path: '/exception/403',
            name: 'Exception403',
            component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/403'),
            meta: { title: 'menu.exception.not-permission', permission: ['exception'] }
          },
          {
            path: '/exception/404',
            name: 'Exception404',
            component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404'),
            meta: { title: 'menu.exception.not-find', permission: ['exception'] }
          },
          {
            path: '/exception/500',
            name: 'Exception500',
            component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/500'),
            meta: { title: 'menu.exception.server-error', permission: ['exception'] }
          }
        ]
      }
    ]
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/user',
    component: TslUserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/TsLogin')
      },
      {
        path: 'register',
        name: 'register',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Register')
      },
      {
        path: 'register-result',
        name: 'registerResult',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/RegisterResult')
      },
      {
        path: 'recover',
        name: 'recover',
        component: undefined
      }
    ]
  },

  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
  }
]
