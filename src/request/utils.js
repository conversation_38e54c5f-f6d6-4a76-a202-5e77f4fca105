import axios from 'axios'
import router from '@/router/index.js'
import store from '../store'
import { message } from 'ant-design-vue'

/**
 * 跳转到登录界面接口
 */

function toLogin() {
  store.dispatch('user/ResetInfo')
  // store.dispatch('user/Logout').then(() => {
  window.location.reload()
  // })
}

// 判断对象中key所组成的数组的长度
export function judgeObjParamsLength(obj) {
  return Object.keys(obj).length
}
/**
 * 判断数据类型
 */
export function judgeDataType(data) {
  return Object.prototype.toString.call(data)
}
/**
 * 请求失败后的错误统一处理
 * @params {Number} status 请求失败的状态码
 */
export function errorHandle(response) {
  let { status, config, statusText, request } = response
  // 状态码判断
  switch (status) {
    // 401:未登录状态，跳转登录页
    case 401:
      // 携带当前页面路由，以期在登录页面完成登录后返回当前页面
      message.warning(`登录过期，2S后跳转登录页`)
      setTimeout(() => {
        toLogin()
      }, 2000)
      break
    // 403:token过期，清除token并跳转登录页
    case 403:
      localStorage.removeItem('token')
      // store.commit('setToken',null)
      message.warning(`token过期，2S后跳转登录页`)
      setTimeout(() => {
        toLogin()
      }, 2000)
      break
    // 404请求不存在
    case 404:
      // message.warning(config.url,'请求不存在');
      message.warning(`请求不存在`)
      break
    default:
      try {
        message.warning(`${JSON.parse(request.responseText).resultMsg}`)
      } catch (err) {
        message.warning(`请求错误`)
      }
  }
}
export class PendingRequest {
  constructor() {
    // 定义请求执行队列
    this.pendingRequestQueue = new Map()
  }
  generateReqKey(config) {
    /**
     * 根据传入的config获取对应的值，返回一个字符串
     */
    const { method, url, params, data } = config
    return [method, url, JSON.stringify(params), JSON.stringify(data)].join('&')
  }
  // 从请求队列中删除请求
  removePendingRequest(config) {
    // 将请求参数加工成字符串
    const requestKey = this.generateReqKey(config)
    // 如果执行队列中包含此次请求
    if (this.pendingRequestQueue.has(requestKey)) {
      // 根据requestKey从执行队列中获取到对应的取消函数
      const cancelToken = this.pendingRequestQueue.get(requestKey)
      // 调用取消函数
      cancelToken(requestKey)
      // 从执行队列中删除以requestKey为key的请求队列
      this.pendingRequestQueue.delete(requestKey)
    }
  }
  // 添加请求到请求队列中
  addPendingRequest(config) {
    // 将请求参数加工成字符串
    const requestKey = this.generateReqKey(config)
    config.cancelToken =
      config.cancelToken ||
      new axios.CancelToken(cancel => {
        if (!this.pendingRequestQueue.has(requestKey)) {
          //如果执行队列不包含此次请求，将此次请求添加到执行队列中
          this.pendingRequestQueue.set(requestKey, cancel)
        }
      })
  }
  // 清空请求队列中的请求(在路由跳转时调用)
  clearPendingRequest() {
    for (const [requestKey, cancelToken] of this.pendingRequestQueue) {
      // 调用取消请求函数
      cancelToken(requestKey)
    }
    // 清空执行队列
    this.pendingRequestQueue.clear()
  }
  // 只清空执行队列
  clearPendingRequestQueue() {
    this.pendingRequestQueue.clear()
  }
}
