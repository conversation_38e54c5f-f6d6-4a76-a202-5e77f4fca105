import qs from 'qs'
import axios from 'axios'
import './base.js'
import { judgeObjParamsLength, judgeDataType } from '../utils.js'

const api = {
  tableListUrl: '/mock/944938/api/medicine-mgmt/v1/rebate/table/list'
}
export const statusAjax = {
  getData(params) {
    return axios({
      url: 'http://httpbin.org/get',
      method: 'get',
      params: params
    })
  },
  postData(params) {
    return axios({
      url: 'http://httpbin.org/post',
      method: 'post',
      params: params
    })
  },
  get401(params) {
    return axios({
      url: 'http://httpbin.org/status/401',
      method: 'get',
      params: params
    })
  },
  get403(params) {
    return axios({
      url: 'http://httpbin.org/status/403',
      method: 'get',
      params: params
    })
  },
  get404(params) {
    return axios({
      url: 'http://httpbin.org/status/404',
      method: 'get',
      params: params
    })
  },
  get500(params) {
    return axios({
      url: 'http://httpbin.org/status/500',
      method: 'get',
      params: params
    })
  }
}
