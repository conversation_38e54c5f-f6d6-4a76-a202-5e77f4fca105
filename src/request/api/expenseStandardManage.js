import qs from 'qs'
import axios from 'axios'
import './base'

const api = {
  tableListUrl: '/api/cathayfuture/bms/expenseStandard/page',
  getDetail: '/api/cathayfuture/bms/expenseStandard/',
  editExpenseStandard: '/api/cathayfuture/bms/expenseStandard/edit'
}
export const expenseStandardManageAjax = {
  getTableData(params) {
    return axios({
      url: api.tableListUrl,
      method: 'get',
      params: params
    })
  },
  getExpenseStandardDetailAjax(id) {
    return axios({
      url: api.getDetail + id,
      method: 'get'
    })
  },

  editExpenseStandard(data) {
    return axios({
      url: api.editExpenseStandard,
      method: 'post',
      data
    })
  }
}
