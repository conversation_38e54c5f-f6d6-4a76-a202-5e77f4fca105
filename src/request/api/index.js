import { appAjax } from './app.js'
import { conservationFeeAjax } from './conservationFee.js'
import { studentInfoManageAjax } from './studentInfoManage.js'
import { cascaderLevelAjax } from './cascader.js'
import { actionListAjax } from './action.js'
import { statusAjax } from './status'
import { attendanceStatisticsAjax } from './attendanceStatistics.js'
import { mitigateManageAjax } from './mitigateManage.js'
import { expenseStandardManageAjax } from './expenseStandardManage.js'
import { orderGeneratedAjax } from './orderGenerated.js'
export default {
  appAjax,
  statusAjax,
  conservationFeeAjax,
  studentInfoManageAjax,
  cascaderLevelAjax,
  actionListAjax,
  attendanceStatisticsAjax,
  mitigateManageAjax,
  expenseStandardManageAjax,
  orderGeneratedAjax
}
