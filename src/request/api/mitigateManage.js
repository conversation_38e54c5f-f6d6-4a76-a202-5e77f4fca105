import axios from 'axios'
import './base.js'

const api = {
  tableListUrl: '/api/cathayfuture/bms/rateRelief/page',
  delRowUrl: '/api/cathayfuture/bms/rateRelief/delete/',
  addTableDataUrl: '/api/cathayfuture/bms/rateRelief/save',
  getSelectOptionsDataUrl: '/api/cathayfuture/bms/rateRelief/queryStudentInfoListByStudentName'
}
export const mitigateManageAjax = {
  getTableDataAjax(params) {
    return axios({
      url: api.tableListUrl,
      method: 'get',
      params: params
    })
  },
  getSelectOptionsDataAjax(params) {
    return axios({
      url: api.getSelectOptionsDataUrl,
      method: 'get',
      params
    })
  },
  delRowAjax(id) {
    return axios({
      url: api.delRowUrl + id,
      method: 'post'
    })
  },
  addTableDataAjax(params) {
    return axios({
      url: api.addTableDataUrl,
      method: 'post',
      data: params
    })
  }
}
