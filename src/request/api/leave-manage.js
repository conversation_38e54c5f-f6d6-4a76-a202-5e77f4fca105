import request from '@/utils/request'

const BASE_URL = '/api/cathayfuture'
export const getLeaveList = (params = {}) => {
  return request({
    url: `${BASE_URL}/bms/askedForLeave/page`,
    method: 'GET',
    params
  })
}

export const getStudentsByName = (name = '') => {
  return request({
    url: `${BASE_URL}/bms/students/queryListByStudentName?studentName=${name}`,
    method: 'GET'
  })
}

export const returnClass = (data = {}) => {
  return request({
    url: `${BASE_URL}/bms/askedForLeave/returnOperate`,
    method: 'POST',
    data
  })
}

export const askForLeave = (data = {}) => {
  return request({
    url: `${BASE_URL}/bms/askedForLeave/save`,
    method: 'POST',
    data
  })
}

export const deleteLeave = (id = '') => {
  return request({
    url: `${BASE_URL}/bms/askedForLeave/delete/${id}`,
    method: 'POST'
  })
}
