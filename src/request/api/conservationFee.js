import qs from 'qs'
import axios from 'axios'
import './base.js'

const api = {
  tableListUrl: '/api/cathayfuture/bms/orders/page',
  orderDetails: '/api/cathayfuture/bms/orders/',
  abolish: '/api/cathayfuture/bms/orders/deleteOrder',
  payment: '/api/cathayfuture/bms/orders/payOrderOffline',
  importTemplateDownload: '/api/cathayfuture/bms/orders/export/template/',
  exportUrl: '/api/cathayfuture/bms/orders/export/',
  importUrl: '/api/cathayfuture/v1/excel/upload/',
  refundUrl: '/api/cathayfuture/bms/orders/refund',
  createOrderUrl: '/api/cathayfuture/bms/orders/page'
}
export const conservationFeeAjax = {
  getTableData(params) {
    return axios({
      url: api.tableListUrl,
      method: 'get',
      params: params
    })
  },
  getOrderDetails(id) {
    return axios({
      url: api.orderDetails + id,
      method: 'get'
    })
  },
  abolish(data) {
    return axios({
      url: api.abolish,
      method: 'put',
      data: data
    })
  },
  refundAjax(data) {
    return axios({
      url: api.refundUrl,
      method: 'put',
      data: data
    })
  },
  paymentAjax(data) {
    return axios({
      url: api.payment,
      method: 'put',
      data: data
    })
  },
  // 导出模板ajax
  exportTemplateAjax(params) {
    return axios({
      url: api.importTemplateDownload + params.type,
      method: 'get',
      responseType: 'blob',
      params: {
        filter: {}
      }
    })
  },
  // 导出数据
  exportDataAjax(parameter) {
    return axios({
      url: api.exportUrl + parameter.type,
      method: 'get',
      params: parameter,
      responseType: 'blob'
    })
  },
  // 导入文件
  importAjax(parameter, type = 'BY_ORDER') {
    return axios({
      url: api.importUrl + type,
      method: 'post',
      data: parameter,
      responseType: 'blob'
    })
  },
  // 生成订单
  createOrderAjax(params) {
    return axios({
      url: api.createOrderUrl,
      method: 'post',
      data: params
    })
  }
}
