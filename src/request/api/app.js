import qs from 'qs'
import axios from 'axios'
import './base.js'
import { judgeObjParamsLength, judgeDataType } from '../utils.js'

const api = {
  tableListUrl: '/mock/944938/api/medicine-mgmt/v1/rebate/table/list'
}
export const appAjax = {
  getTableListAjax(params) {
    return axios({
      url: api.tableListUrl,
      method: 'get',
      params: params
    })
  },
  getData(params) {
    return axios({
      url: 'http://httpbin.org/get',
      method: 'get',
      params: params
    })
  }
}
