import qs from 'qs'
import axios from 'axios'
import './base.js'

const api = {
  shengChengBaoYueFeiDingDanUrl: '/api/cathayfuture/bms/sysCreateOrder/childCareOperate', //生成保育费订单
  shengChengCanFeiDingDanUrl: '/api/cathayfuture/bms/sysCreateOrder/dinnerOperate', //生成餐费订单
  baoYuFeiYueJieUrl: '/api/cathayfuture/bms/sysCreateOrder/monthlySettlementOperate', //保育费月结
  canFeiYueJieUrl: '/api/cathayfuture/bms/sysCreateOrder/monthlySettlementForDinnerOperate' //餐费月结
}
export const orderGeneratedAjax = {
  shengChengBaoYueFeiDingDanAjax(params) {
    return axios({
      url: api.shengChengBaoYueFeiDingDanUrl,
      method: 'get'
    })
  },
  shengChengCanFeiDingDanAjax(params) {
    return axios({
      url: api.shengChengCanFeiDingDanUrl,
      method: 'get'
    })
  },
  baoYuFeiYueJieAjax(params) {
    return axios({
      url: api.baoYuFeiYueJieUrl,
      method: 'get'
    })
  },
  canFeiYueJieAjax(params) {
    return axios({
      url: api.canFeiYueJieUrl,
      method: 'get'
    })
  }
}
