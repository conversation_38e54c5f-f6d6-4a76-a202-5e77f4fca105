import request from '@/utils/request'

const api = {
  getSignature:
    process.env.NODE_ENV === 'production'
      ? '/api/cathayfuture/v1/cathayfuture/oss/tasly/getpolicy'
      : '/api/medicine-mgmt/v1/rebate/oss/tasly/getpolicy',
  getSignatureDownLoad: '/api/medicine-mgmt/v1/rebate/oss/geturl/tasly/get-authorized-file-url?fileURI' // 获取文件下载签名
}

export default api

// 获取文件上传签名
export function getSignature(parameter) {
  console.log('test', parameter)
  return request({
    url: api.getSignature,
    method: 'get',
    params: parameter
  })
}

// 获取文件下载签名
export function getSignatureDownLoad(url) {
  return request({
    url: api.getSignatureDownLoad + url,
    method: 'get'
  })
}

// 文件上传
export function postUpload(params = {}, url) {
  return request({
    url,
    method: 'post',
    data: params,
    type: 'form-data'
  })
}
