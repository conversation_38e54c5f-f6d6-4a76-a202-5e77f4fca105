import request from '@/utils/request'

const BASE_URL = '/api/cathayfuture'

export const fetchClasses = () => {
  // return request({
  //   url: `${BASE_URL}/bms/studentClass/queryAllClassNames`,
  //   method: 'GET'
  // })
  return request({
    url: `${BASE_URL}/bms/attendanceRecord/queryDataIntegrityByClass`,
    method: 'GET'
  })
}

export const fetchStudents = (className, currentDate) => {
  return request({
    url: `${BASE_URL}/bms/attendanceRecord/queryStudentInfoList?studentClass=${className}&currentDate=${currentDate}`,
    method: 'GET'
  })
}

export const saveLeaveStatus = data => {
  return request({
    url: `${BASE_URL}/bms/attendanceRecord/save`,
    method: 'POST',
    data
  })
}
