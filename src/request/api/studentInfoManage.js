import qs from 'qs'
import axios from 'axios'
import './base.js'

const api = {
  tableListUrl: '/api/cathayfuture/bms/students/page',
  exportUrl: '/api/cathayfuture/bms/students/export',
  editStudents: '/api/cathayfuture/bms/students/',
  countByStudentNoUrl: '/api/cathayfuture/bms/students/countByStudentNo',
  saveStudentsUrl: '/api/cathayfuture/bms/students/save',
  registerStudentsUrl: '/api/cathayfuture/bms/students/register',
  exitUrl: '/api/cathayfuture/bms/students/quit/'
}
export const studentInfoManageAjax = {
  getTableData(params) {
    return axios({
      url: api.tableListUrl,
      method: 'get',
      params: params
    })
  },
  editStudentsAjax(id) {
    return axios({
      url: api.editStudents + id,
      method: 'get'
    })
  },
  // 导出数据
  exportDataAjax(parameter) {
    return axios({
      url: api.exportUrl,
      method: 'get',
      params: parameter,
      responseType: 'blob'
    })
  },
  saveStudentsUrl(data) {
    return axios({
      url: api.saveStudentsUrl,
      method: 'post',
      data
    })
  },
  registerStudentsUrl(data) {
    return axios({
      url: api.registerStudentsUrl,
      method: 'post',
      data
    })
  },
  countByStudentNoUrlAjax(params) {
    return axios({
      url: api.countByStudentNoUrl,
      method: 'get',
      params: params
    })
  },
  exitAjax(id) {
    return axios({
      url: api.exitUrl + id,
      method: 'post'
    })
  }
}
