import qs from 'qs'
import axios from 'axios'
import './base.js'

const api = {
  tableListUrl: '/mock/getTableData',
  importTemplateDownload: '/api/medicine-mgmt/v1/export/excel/template/firstLevelEnterpriseTemplate',
  exportUrl: '/api/medicine-mgmt/v1/export/excel/firstLevelEnterprise-excel',
  importUrl: '/api/medicine-mgmt/v1/excel/upload/'
}
export const cascaderLevelAjax = {
  getCascaderLevel1() {
    return axios({
      url: '/mock/getCascaderLevel1',
      method: 'get'
    })
  },
  getCascaderLevel2(params) {
    return axios({
      url: '/mock/getCascaderLevel2',
      method: 'get',
      params: params
    })
  },
  getCascaderLevel3(params) {
    return axios({
      url: '/mock/getCascaderLevel3',
      method: 'get',
      params: params
    })
  }
}
