/**
 * axios封装
 * 完成的功能
 * * 请求拦截
 * * 响应拦截
 * * 错误统一处理
 * * 取消重复请求
 */
import axios from 'axios'
import store from '@/store'
import { PendingRequest, errorHandle } from './utils.js'
import { message } from 'ant-design-vue'
let pendingRequest = new PendingRequest()
// 设置axios基本信息
axios.defaults.timeout = 10000

// 设置post请求头
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded'

// 添加请求拦截器
axios.interceptors.request.use(
  // 在请求发送之前做些什么
  config => {
    pendingRequest.removePendingRequest(config)
    pendingRequest.addPendingRequest(config)
    // 登录流程控制中，根据本地是否存在token来判断用户的登录情况
    // 但是即使是token存在，也有可能token是过期的，所以在每次请求头中携带token
    // 后台根据携带的token判断用户的登录情况，并返回给我们对应的状态码
    // 而后我们可以在响应拦截器中，根据状态码进行一些统一的操作

    // const token = store.state.token;
    const token = localStorage.getItem('message_token')
    token && (config.headers['Access-Token'] = token)
    return config
  },
  // 对请求错误做些什么
  error => {
    // 这里出现的错误可能是网络波动造成的，清空执行队列
    pendingRequest.clearPendingRequestQueue()
    return Promise.reject(error)
  }
)

// 添加响应拦截器
axios.interceptors.response.use(
  // 对响应数据做些什么
  response => {
    // 数据正确返回，证明联网中
    store.commit('changeNetwork', true)
    pendingRequest.removePendingRequest(response.config)
    let res = response.data
    if (res.resultCode && res.resultCode !== 'SUCCESS' && res.resultCode !== '0') {
      if (res.data && res.data.errorMessage) {
        message.error(res.data.errorMessage)
      } else {
        message.error(res.resultMsg)
      }
      return Promise.reject(res)
    } else {
      return Promise.resolve(res)
    }
  },
  // 对响应错误做些什么
  error => {
    // 判断是否是调用取消函数取消请求队列
    if (axios.isCancel(error)) {
      console.warn(error)
      return Promise.reject(error)
    } else {
      // 添加其他异常处理
      let { response } = error
      if (response) {
        // 请求已经发出，但是不在2XX的范围
        errorHandle(response)
      } else {
        // 处理断网的情况
        // 请求超时或者断网时，更新state的network状态
        // network状态在app.vue组件中控制着一个全局断网组件的显示隐藏
        // 关于断网组件中的刷新冲洗获取数据，会在断网组件内中说明
        if (!window.navigator.onLine) {
          // 断网了
          store.commit('changeNetwork', false)
        } else {
          store.commit('changeNetwork', true)
          return Promise.reject(error)
        }
      }
    }
  }
)
