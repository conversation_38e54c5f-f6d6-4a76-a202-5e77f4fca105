<template>
  <div>
    <div class="header" style="text-align: center; padding: 18px 0">
      <img src="../assets/login/logo_1.png" alt="" />
    </div>
    <div id="userLayout" :class="['user-layout-wrapper', isMobile && 'mobile']">
      <div class="container">
        <div class="smaller-container">
          <div>
            <div
              style="
                width: 469px;
                height: 103px;
                background: #258583;
                font-size: 37px;
                font-weight: 500;
                color: #ffffff;
                text-align: center;
                line-height: 103px;
                margin-right: 24px;
                position: relative;
              "
            >
              <span
                style="
                  position: absolute;
                  display: inline-block;
                  width: 0;
                  height: 0;
                  border-top: 10px solid transparent;
                  border-left: 10px solid #fff;
                  border-right: 10px solid transparent;
                  border-bottom: 10px solid transparent;
                  left: 0;
                  top: 40px;
                "
              ></span>
              华夏未来天人智慧幼儿园
            </div>
            <p
              style="
                width: 469px;
                height: 61px;
                font-size: 43px;
                font-weight: 500;
                color: #ffffff;
                line-height: 61px;
                text-align: center;
              "
            >
              TSY WISDOM
            </p>
          </div>
          <div class="tsl-content">
            <router-view />
          </div>
        </div>
      </div>
    </div>
    <div class="footer" style="text-align: center; padding: 12px 0 0 0">
      <span>Copyright 1994 - 2021 TASLY Holding Group, All Rights Reserved</span>
    </div>
  </div>
</template>

<script>
import { deviceMixin } from '@/store/device-mixin'

export default {
  name: 'TslUserLayout',
  mixins: [deviceMixin],
  mounted() {
    document.body.classList.add('userLayout')
  },
  beforeDestroy() {
    document.body.classList.remove('userLayout')
  }
}
</script>

<style lang="less" scoped>
#userLayout.user-layout-wrapper {
  height: 100%;
  .container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    min-height: 500px;
    background: #fff url(~@/assets/login/bg.png) no-repeat 50%;
    background-size: cover;
    position: relative;

    .smaller-container {
      display: flex;
      justify-content: flex-end;
      width: 80%;
      height: 64%;
      min-height: 500px;
      padding: 34px 6%;
      padding-top: 115px;
      // background: #364fcc url(~@/assets/login/card.png) no-repeat ~'calc(50% + 10px)' 50%;
      background-size: cover;
      // box-shadow: 0 2px 9px rgba(0, 0, 0, 0.23);
    }
    .tsl-content {
      position: relative;
      width: 35%;
      min-width: 430px;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.12);
      background-color: #fff;
      padding: 0px 50px;
      height: 280px;
    }
  }
  .copyright {
    position: absolute;
    left: 0;
    bottom: 20px;
    width: 100%;
    font-size: 10px;
    color: #999999;
    text-align: center;
  }
}
</style>
