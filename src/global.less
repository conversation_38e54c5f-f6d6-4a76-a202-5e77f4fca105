@import '~ant-design-vue/es/style/themes/default.less';

html,
body,
#app,
#root {
  height: 100%;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, YaHei, '\5FAE\8F6F\96C5\9ED1', Arial,
    sans-serif;
}
#printOrder {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, YaHei, '\5FAE\8F6F\96C5\9ED1', Arial,
    sans-serif;
  font: 12px Helvetica Neue, Helvetica, PingFang SC, Tahoma, Arial, sans-serif;
  color: #000;
}
.colorWeak {
  filter: invert(80%);
}

.ant-layout.layout-basic {
  height: 100vh;
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

// 数据列表 样式
.table-alert {
  margin-bottom: 16px;
}
// 数据列表 操作
.table-operator {
  margin-bottom: 18px;

  button {
    margin-right: 8px;
  }
}
// 数据列表 搜索条件
.table-page-search-wrapper {
  .ant-form-inline {
    .ant-form-item {
      display: flex;
      margin-bottom: 24px;
      margin-right: 0;

      .ant-form-item-control-wrapper {
        flex: 1 1;
        display: inline-block;
        vertical-align: middle;
      }

      > .ant-form-item-label {
        line-height: 32px;
        padding-right: 8px;
        width: auto;
      }
      .ant-form-item-control {
        height: 32px;
        line-height: 32px;
      }
    }
  }

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}
footer {
  display: none;
}
.ant-collapse-header {
  color: #333 !important;
}
.ant-calendar-picker {
  width: 100% !important;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}