import request from '@/utils/request'

const api = {
  getSignature: 'https://mpgateway-dev.tasly.com/bigservice/bms/v1/huieryun/objectstorage/policy/dafuwu/getpolicy', // 获取文件上传签名
  getSignatureDownLoad: '/v1/huieryun/objectstorage/policy/tasly/get-authorized-file-url?fileURI' // 获取文件下载签名
}

// 获取文件上传签名
export function getSignature(parameter) {
  return request({
    url: api.getSignature,
    method: 'get',
    params: parameter
  })
}

// 获取文件下载签名
export function getSignatureDownLoad(url) {
  return request({
    url: api.getSignatureDownLoad + url,
    method: 'get'
  })
}

// 文件上传
export function postUpload(params = {}, url) {
  return request({
    url,
    method: 'post',
    data: params,
    type: 'form-data'
  })
}
