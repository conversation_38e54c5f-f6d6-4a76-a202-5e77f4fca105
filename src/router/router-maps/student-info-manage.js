export default {
  // 学生信息管理
  'student-manage/student-info-manage': {
    name: 'student-info-manage',
    component: () => import('@/views/studentManage/studentInfoManage/index.vue')
  },
  'student-manage/leave-manage': {
    name: 'leave-manage',
    component: () => import('@/views/studentManage/leaveManage/index.vue')
  },
  'student-manage/expense-standard-manage': {
    name: 'expense-standard-manage',
    component: () => import('@/views/studentManage/expenseStandardManage/index.vue')
  },
  'student-manage/attendance-record': {
    name: 'attendance-record',
    component: () => import('@/views/studentManage/attendanceRecord/index.vue')
  },
  'student-manage/class-attendance': {
    name: 'class-attendance',
    component: () => import('@/views/studentManage/attendanceRecord/classAttendance.vue'),
    hidden: true
  },
  'student-manage/attendance-statistics': {
    name: 'attendance-statistics',
    component: () => import('@/views/studentManage/attendanceStatistics/index.vue')
  },
  'student-manage/mitigate-manage': {
    name: 'mitigate-manage',
    component: () => import('@/views/studentManage/mitigateManage/index.vue')
  }
}
