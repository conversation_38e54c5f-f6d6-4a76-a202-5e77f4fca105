export default {
  // 订单管理
  'order-management/conservation-fee': {
    name: 'conservation-fee',
    component: () => import('@/views/orderManagement/conservationFee/index.vue')
  },
  'order-management/food-bill': {
    name: 'food-bill',
    component: () => import('@/views/orderManagement/foodBill/index.vue')
  },
  'order-management/charge-bill': {
    name: 'charge-bill',
    component: () => import('@/views/orderManagement/chargeBill/index.vue')
  },
  'order-management/deposit-bill': {
    name: 'deposit-bill',
    component: () => import('@/views/orderManagement/depositBill/index.vue')
  },
  'order-management/incidental-bill': {
    name: 'incidental-bill',
    component: () => import('@/views/orderManagement/incidentalBill/index.vue')
  },
  'order-management/orderGenerated': {
    name: 'order-generated',
    component: () => import('@/views/orderManagement/orderGenerated/index.vue')
  }
}
