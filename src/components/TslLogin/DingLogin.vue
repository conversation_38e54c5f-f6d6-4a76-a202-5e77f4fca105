<template>
  <div class="ding-login-container">
    <div id="ding-code"></div>
  </div>
</template>

<script>
const dingBackUrl = 'https://oapi.dingtalk.com/connect/oauth2/sns_authorize'

export default {
  name: 'DingLogin',
  data() {
    return {}
  },
  props: {
    // 钉钉 App ID
    dingAppID: {
      type: String,
      default: 'dingoaen8qlmogbyjhvo2t'
    },
    // 重定向的 URL
    redirectURL: {
      type: String,
      default: 'http://127.0.0.1:8000'
    }
  },
  beforeCreate() {
    // 引入钉钉 JS
    if (!window.DDLogin) {
      const s = document.createElement('script')
      s.type = 'text/javascript'
      s.src = 'https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js'
      document.body.appendChild(s)
    }
  },
  mounted() {
    if (window.DDLogin) {
      this.initDing()
    } else {
      window.addEventListener('load', () => {
        this.initDing()
      })
    }

    var handleMessage = function(event) {
      const { dingAppID } = this
      var origin = event.origin
      if (origin === 'https://login.dingtalk.com') {
        //判断是否来自ddLogin扫码事件。
        var loginTmpCode = event.data //拿到loginTmpCode后就可以在这里构造跳转链接进行跳转了

        // 跳转到首页
        window.location.href =
          `${dingBackUrl}?appid=${dingAppID}&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=REDIRECT_URI&loginTmpCode=` +
          loginTmpCode
      }
    }
    if (typeof window.addEventListener != 'undefined') {
      window.addEventListener('message', handleMessage, false)
    } else if (typeof window.attachEvent != 'undefined') {
      window.attachEvent('onmessage', handleMessage)
    }
  },
  methods: {
    // 初始化钉钉扫码
    initDing() {
      const { dingAppID, redirectURL } = this
      const goto = encodeURIComponent(
        `${dingBackUrl}?appid=${dingAppID}&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${redirectURL}`
      )
      const dingConfig = {
        id: 'ding-code',
        goto: goto,
        style: 'border:none;background-color:#FFFFFF;',
        width: '300',
        height: '300'
      }
      if (DDLogin) {
        DDLogin(dingConfig)
      }
    }
  }
}
</script>
<style lang="less" scoped>
.ding-login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}
</style>
