<template>
  <div class="phone-login-container">
    <a-form-item>
      <a-input
        size="large"
        type="text"
        placeholder="手机号"
        v-decorator="[
          'mobile',
          {
            rules: [{ required: true, pattern: /^1[34578]\d{9}$/, message: '请输入正确的手机号' }],
            validateTrigger: 'change'
          }
        ]"
      >
        <a-icon slot="prefix" type="mobile" :style="{ color: 'rgba(0,0,0,.25)' }" />
      </a-input>
    </a-form-item>

    <a-row :gutter="16">
      <a-col class="gutter-row" :span="16">
        <a-form-item>
          <a-input
            size="large"
            type="text"
            placeholder="验证码"
            v-decorator="['captcha', { rules: [{ required: true, message: '请输入验证码' }], validateTrigger: 'blur' }]"
          >
            <a-icon slot="prefix" type="mail" :style="{ color: 'rgba(0,0,0,.25)' }" />
          </a-input>
        </a-form-item>
      </a-col>
      <a-col class="gutter-row" :span="8">
        <a-button
          class="getCaptcha"
          tabindex="-1"
          :disabled="state.smsSendBtn"
          @click.stop.prevent="getCaptcha"
          v-text="(!state.smsSendBtn && '获取验证码') || state.time + ' s'"
        ></a-button>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import md5 from 'md5'
export default {
  name: 'PhoneLogin',
  methods: {
    handleSubmit(e) {
      e.preventDefault()
      const {
        form: { validateFields },
        state,
        Login
      } = this

      state.loginBtn = true

      const validateFieldsKey = ['mobile', 'captcha']

      validateFields(validateFieldsKey, { force: true }, (err, values) => {
        if (!err) {
          console.log('login form', values)
          const loginParams = { ...values }
          delete loginParams.username
          loginParams[!state.loginType ? 'email' : 'username'] = values.username
          loginParams.password = md5(values.password)
          Login(loginParams)
            .then(res => {
              this.$emit('submit-success', res)
              this.isLoginError = false
            })
            .catch(err => {
              this.$emit('submit-error', err)
              this.isLoginError = true
            })
            .finally(() => {
              state.loginBtn = false
              this.$emit('submit-done')
            })
        } else {
          setTimeout(() => {
            state.loginBtn = false
          }, 600)
        }
      })
    }
  }
}
</script>
