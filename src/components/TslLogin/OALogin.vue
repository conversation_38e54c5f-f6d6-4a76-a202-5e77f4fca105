<template>
  <a-form id="formLogin" class="user-layout-login" ref="formLogin" :form="form" @submit="handleSubmit">
    <a-form-item>
      <a-input
        size="large"
        type="text"
        :placeholder="accountPlaceholder"
        v-decorator="[
          'username',
          {
            rules: [{ required: true, message: '请输入 OA 账号' }, { validator: handleUsernameOrEmail }],
            validateTrigger: 'change'
          }
        ]"
      >
        <a-icon slot="prefix" type="user" :style="{ color: 'rgba(0,0,0,.25)' }" />
      </a-input>
    </a-form-item>
    <a-form-item>
      <a-input-password
        size="large"
        :placeholder="pwdPlaceholder"
        v-decorator="['password', { rules: pwdRules, validateTrigger: 'blur' }]"
      >
        <a-icon slot="prefix" type="lock" :style="{ color: 'rgba(0,0,0,.25)' }" />
      </a-input-password>
    </a-form-item>
    <a-form-item v-if="imageCaptcha">
      <a-input
        placeholder="验证码"
        class="captcha-input"
        v-decorator="['code', { rules: [{ required: true, message: '请输入验证码' }], validateTrigger: 'blur' }]"
      />
      <img :src="'data:image/jpeg;base64,' + imageCaptcha" alt="" @click="getImageCaptcha" />
      <a-icon @click="getImageCaptcha" class="refresh-captcha-btn" type="redo" />
    </a-form-item>
    <a-form-item style="margin-top:24px">
      <a-button
        size="large"
        type="primary"
        html-type="submit"
        class="login-button"
        :loading="state.loginBtn"
        :disabled="state.loginBtn"
        >登录</a-button
      >
    </a-form-item>
  </a-form>
</template>

<script>
import { rstr2b64 } from '@/utils/md5'
import { getCaptcha, bocLogin } from '@/api/login'

export default {
  name: 'AccountLogin',
  components: {},
  props: {
    accountPlaceholder: {
      type: String,
      default: 'OA 账号'
    },
    pwdPlaceholder: {
      type: String,
      default: '密码'
    },
    pwdRules: {
      type: Array,
      default: function() {
        return [{ required: true, message: '请输入密码' }]
      }
    }
  },
  data() {
    return {
      loginBtn: false,
      form: this.$form.createForm(this),
      state: {
        time: 60,
        loginBtn: false,
        // login type: 0 email, 1 username, 2 telephone
        loginType: 1
      },
      imageCaptcha: '',
      imageCaptchaId: ''
    }
  },
  created() {
    this.getImageCaptcha()
  },
  methods: {
    getImageCaptcha() {
      getCaptcha().then(res => {
        if (res.resultCode === '0') {
          this.imageCaptcha = res.data.image
          this.imageCaptchaId = res.data.uniqueId
        }
      })
    },
    // handler
    handleUsernameOrEmail(rule, value, callback) {
      const { state } = this
      const regex = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/
      if (regex.test(value)) {
        state.loginType = 0
      } else {
        state.loginType = 1
      }
      callback()
    },
    handleSubmit(e) {
      e.preventDefault()
      const {
        form: { validateFields },
        state
      } = this

      state.loginBtn = true

      const validateFieldsKey = ['username', 'password', 'code']

      validateFields(validateFieldsKey, { force: true }, (err, values) => {
        if (!err) {
          let loginParams = { ...values }
          const params = {
            phone: '',
            password: rstr2b64(values.password),
            loginType: 0,
            loginSource: String(new Date().getTime() + parseInt(Math.random() * 10000)).slice(3),
            checkCodeUniqueId: this.imageCaptchaId,
            // 需要更换成自己应用的
            instanceId: '1269982597500616710',
            tenantId: 1
          }
          loginParams = Object.assign(loginParams, params)
          bocLogin(loginParams)
            .then(res => {
              state.loginBtn = false
              if (res.resultCode == 0) {
                this.$emit('success', res)
              } else {
                this.$emit('error', res)
                this.getImageCaptcha()
                this.$notification['error']({
                  message: '错误',
                  description: res.resultMsg || '请求出现错误，请稍后再试',
                  duration: 4
                })
              }
            })
            .catch(e => {
              state.loginBtn = false
              this.getImageCaptcha()
              this.$notification['error']({
                message: '错误',
                description: e.resultMsg || '请求出现错误，请稍后再试',
                duration: 4
              })
              this.$emit('error', e)
            })
          // this.$emit('submit', loginParams)
        } else {
          setTimeout(() => {
            state.loginBtn = false
          }, 600)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.user-layout-login {
  .captcha-input {
    width: calc(100% - 140px);
    margin-right: 10px;
  }
  .refresh-captcha-btn {
    margin-left: 10px;
  }
  label {
    font-size: 14px;
  }

  button.login-button {
    padding: 0 15px;
    font-size: 16px;
    height: 40px;
    width: 100%;
  }
}
</style>
