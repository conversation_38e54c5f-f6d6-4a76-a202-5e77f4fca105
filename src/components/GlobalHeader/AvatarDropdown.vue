<template>
  <div>
    <resetPwd ref="resetPwd" user-type="system"></resetPwd>
    <Logout @comfirm="handleLogout" />
    <!-- <a-dropdown v-if="currentUser && currentUser.name" placement="bottomRight">
      <span class="ant-pro-account-avatar">
        <a-avatar
          size="small"
          src="https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png"
          class="antd-pro-global-header-index-avatar"
        />
        <span>{{ currentUser.name }}</span>
      </span>
      <template v-slot:overlay>
        <a-menu class="ant-pro-drop-down menu" :selected-keys="[]">
          <a-menu-item v-if="menu" key="resetPwd" @click="handleToResetPwd">
            <a-icon type="setting" />
            {{ $t('menu.account.resetPwd') }}
          </a-menu-item>
          <a-menu-divider v-if="menu" />
          <a-menu-item key="logout">
            <logout @comfirm="handleLogout" />
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <span v-else>
      <a-spin size="small" :style="{ marginLeft: 8, marginRight: 8 }" />
    </span> -->
  </div>
</template>

<script>
import resetPwd from '@/components/ResetPwd'
import Logout from '@/components/TsLogout/Logout'

export default {
  name: 'AvatarDropdown',
  components: {
    resetPwd: resetPwd,
    Logout
  },
  props: {
    currentUser: {
      type: Object,
      default: () => null
    },
    menu: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      userInfo: {}
    }
  },
  methods: {
    handleLogout() {
      localStorage.clear()
      this.$store.dispatch('user/ResetInfo')
      // this.$store.dispatch('user/Logout').then(() => {
      this.$router.push({ name: 'login' })
      // })
    },
    handleToCenter() {
      this.$router.push({ path: '/account/center' })
    },
    handleToSettings() {
      this.$router.push({ path: '/account/settings' })
    },
    handleToResetPwd() {
      this.userInfo = this.$store.getters.userInfo
      // 调用组件里的展示弹框的方法
      this.$refs.resetPwd.initDialog(this.userInfo.id)
    }
  }
}
</script>

<style lang="less" scoped>
.ant-pro-drop-down {
  /deep/ .action {
    margin-right: 8px;
  }
  /deep/ .ant-dropdown-menu-item {
    min-width: 160px;
  }
}
</style>
