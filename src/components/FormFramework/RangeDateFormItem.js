import BaseFormItem from './BaseFormItem.js'
import { formItemType } from './enum.js'

export default class RangeDateFormItem extends BaseFormItem {
  constructor(formItemConfig) {
    super(formItemType.RANGE_PICKER, formItemConfig)
  }
  getOutPutFormatFn() {
    return this.config.customFiled.outPutFormatFn || null
  }
  outPutFormat(val) {
    let type = this.getType()
    let outPutFormatFn = this.getOutPutFormatFn()
    let value = ''
    if (outPutFormatFn == null) {
      value = val ? val : null
    } else {
      value = outPutFormatFn(val)
    }
    return {
      type,
      value
    }
  }
}
