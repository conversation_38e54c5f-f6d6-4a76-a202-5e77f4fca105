import BaseFormItem from './BaseFormItem.js'
import { formatSelectModule } from './utils/formUtil.js'

export default class SelectFormItem extends BaseFormItem {
  constructor(formItemConfig) {
    super('Select', formItemConfig)
  }
  getApiFn() {
    return this.config.customFiled.apiFn
  }
  getOutPutFormatFn() {
    return this.config.customFiled.outPutFormatFn || null
  }
  // 初始化时要执行操作
  init(fApi) {
    let item = fApi.rule.find(item => item.field == this.getField())
    let apiFn = this.getApiFn()
    if (apiFn == null) {
      return
    }
    apiFn().then(res => {
      if (Object.prototype.toString.call(res.data) === '[object Array]') {
        let arr = res.data.map(item => {
          return {
            id: item,
            key: item,
            label: item,
            value: item
          }
        })
        fApi.set(item, 'options', arr)
      } else {
        fApi.set(item, 'options', formatSelectModule(res.data))
      }
    })
  }
  // 重置是要执行的操作
  reset(fApi) {
    this.init(fApi)
  }
  outPutFormat(val) {
    let type = this.getType()
    let value = ''
    let outPutFormatFn = this.getOutPutFormatFn()
    if (outPutFormatFn == null) {
      value = val ? val : null
    } else {
      value = outPutFormatFn(val)
    }
    return {
      type,
      value
    }
  }
}
