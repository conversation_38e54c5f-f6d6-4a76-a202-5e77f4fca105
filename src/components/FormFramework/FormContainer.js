import { formItemType } from './enum.js'

import InputFormItem from './InputFormItem.js'
import RangeDateFormItem from './RangeDateFormItem.js'
import MonthPickerFormItem from './MonthPickerFormItem.js'
import SingleSelectFormItem from './SingleSelectFormItem.js'
import MultipleSelectFormItem from './MultipleSelectFormItem.js'
import DatePickerFormItem from './DatePickerFormItem.js'

export default class FormContainer {
  constructor(formCreate) {
    this.formCreate = formCreate
    this.fApi = null
    this.configArr = [] //放formRules对象
    this.container = [] //放实例对象
    this.mappingObj = {
      [formItemType.INPUT]: InputFormItem,
      [formItemType.SINGLE_SELECT]: SingleSelectFormItem,
      [formItemType.MULTIPLE_SELECT]: MultipleSelectFormItem,
      [formItemType.RANGE_PICKER]: RangeDateFormItem,
      [formItemType.MONTH_PICKER]: MonthPickerFormItem,
      [formItemType.DATE_PICKER]: DatePickerFormItem
    }
  }
  //#region
  print() {
    let logs = []
    for (let i = 0; i < this.container.length; i++) {
      let formItem = this.container[i]
      let field = formItem.getField()
      let log = formItem.print()
      logs[field] = log
    }
    return logs
  }
  findItemByField(field) {
    let Instance = this.container.find(item => field == item.getField())
    return Instance
  }

  setfApi(fApi) {
    this.fApi = fApi
  }
  buildClass() {
    this.configArr.forEach(item => {
      let type = item.customFiled.FormItemType
      let constructorFunc = this.mappingObj[type]
      this.container.push(new constructorFunc(item))
    })
    return this.container
  }
  setValueByField(field, value) {
    this.fApi.setValue(field, value)
  }
  //#endregion
  setConfigArr(configArr) {
    this.configArr = this.formCreate.copyRules(configArr)
  }
  getFormRule() {
    //获取表单规则数组
    return this.configArr
  }
  build(fApi) {
    this.setfApi(fApi)
    this.buildClass()
  }
  initDataSource() {
    this.container.forEach(item => {
      item.init(this.fApi)
    })
  }
  submitFormat(fn) {
    //查询按钮触发事件
    this.fApi.submit((formData, fApi) => {
      let formDataFormat = []
      this.container.forEach(item => {
        let field = item.getField()
        let formatObj = item.outPutFormat(formData[field])
        formDataFormat[field] = formatObj
      })
      fn(formDataFormat)
    })
  }
  reset() {
    //重置按钮触发事件
    this.fApi.resetFields()
    this.container.forEach(item => {
      item.reset(this.fApi)
    })
  }
  initValidate(fieldArr) {
    let arr = fieldArr.map(item => item.field)
    for (let i in arr) {
      let item = this.fApi.rule.find(item => item.field == arr[i])
      if (item.customFiled && item.customFiled.validate) {
        let validate = item.customFiled.validate
        this.fApi.set(item, 'validate', validate)
      }
    }
  }
}
