export default class BaseFormItem {
  constructor(type, formItemConfig) {
    this.type = type || formItemConfig.type
    this.config = formItemConfig
  }
  getType() {
    return this.config.type
  }
  getFormItemType() {
    return this.config.customFiled.FormItemType
  }
  getValue() {
    return this.config.value
  }
  setValue(value) {
    this.config.value = value
  }
  getField() {
    return this.config.field
  }
  outPutFormat() {}
  init() {}
  reset() {}
  getApiFn() {
    return null
  }
  getOutPutFormatFn() {
    return null
  }
  print() {
    return {
      type: this.getType(),
      value: this.getValue()
    }
  }
}
