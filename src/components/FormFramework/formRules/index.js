import {
  discountAttributionMonth,
  rangePaymentTime,
  rangeOrderCreateTime,
  enterParkTime,
  rangeDeleteTime,
  date,
  leaveDate
} from './RangePicker.js'
import {
  orderStatus,
  payloadStatus,
  paymentMethod,
  singleGrade,
  businessStatus,
  sex,
  payloadStatusFourOptions,
  studentType,
  registerStatus,
  attendanceStatus,
  expenseStandardType,
  leaveStatus
} from './SingleSelect.js'
import {
  primaryEnterprise,
  studentName,
  studentID,
  studentContactType,
  payService,
  orderNumber,
  phone,
  parentName,
  grade,
  relation,
  nation,
  identityNumber,
  motherPhone,
  motherName,
  fatherPhone,
  nationality,
  fatherName,
  connectName,
  birthday,
  // paymentTime,
  // orderCreateTime
  refundReason
} from './Input.js'
import { paymentTime, orderCreateTime, enterTime, birthdayTime, refundTime, enterTimeOfRegister } from './DatePicker.js'

export const rules = {
  date,
  attendanceStatus,
  // 折让归属年月（有起始日期的月份）
  discountAttributionMonth: discountAttributionMonth,
  // 一级收款企业
  primaryEnterprise: primaryEnterprise,
  studentName,
  phone,
  grade,
  relation,
  parentName,
  studentID,
  studentContactType,
  payService,
  orderNumber,
  paymentTime,
  orderCreateTime,
  orderStatus,
  studentType,
  payloadStatus,
  paymentMethod,
  enterTime,
  nation,
  identityNumber,
  motherPhone,
  motherName,
  fatherPhone,
  nationality,
  fatherName,
  connectName,
  birthday,
  refundReason,
  singleGrade,
  businessStatus,
  sex,
  birthdayTime,
  refundTime,
  rangePaymentTime,
  rangeOrderCreateTime,
  enterParkTime,
  rangeDeleteTime,
  payloadStatusFourOptions,
  registerStatus,
  enterTimeOfRegister,
  expenseStandardType,
  leaveDate,
  leaveStatus
}
