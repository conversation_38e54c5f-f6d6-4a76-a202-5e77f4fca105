import { utils } from '../utils/formUtil.js'
import { formItemType } from '../enum.js'
import moment from 'moment'

export const discountAttributionMonth = {
  type: 'RangePicker',
  field: 'discountAttributionMonth',
  title: '折让归属年月',
  value: [],
  props: {
    placeholder: ['---年---月', '---年---月'],
    format: 'YYYY-MM',
    mode: ['month', 'month'],
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    outPutFormatFn: function (data) {
      return {
        begin: data && data.length > 0 ? utils.RangePicker.startMonth(data[0]) : null,
        end: data && data.length > 0 ? utils.RangePicker.startMonth(data[1]) : null
      }
    }
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['panelChange']
}

export const rangePaymentTime = {
  type: 'RangePicker',
  field: 'rangePaymentTime',
  title: '付款时间',
  wrap: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 }
  },
  value: [],
  props: {
    placeholder: ['年-月-日 时:分:秒', '年-月-日 时:分:秒'],
    showTime: { format: 'HH:mm:ss' },
    format: 'YYYY-MM-DD HH:mm:ss',
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    outPutFormatFn: function (data) {
      return {
        begin: data && data.length > 0 ? utils.RangePicker.dateFormat(data[0], 'YYYY-MM-DD HH:mm:ss') : null,
        end: data && data.length > 0 ? utils.RangePicker.dateFormat(data[1], 'YYYY-MM-DD HH:mm:ss') : null
      }
    }
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['panelChange']
}

export const rangeOrderCreateTime = {
  type: 'RangePicker',
  field: 'rangeOrderCreateTime',
  title: '订单创建时间',
  value: [],
  wrap: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 }
  },
  props: {
    placeholder: ['年-月-日 时:分:秒', '年-月-日 时:分:秒'],
    format: 'YYYY-MM-DD HH:mm:ss',
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    outPutFormatFn: function (data) {
      return {
        begin: data && data.length > 0 ? utils.RangePicker.dateFormat(data[0], 'YYYY-MM-DD HH:mm:ss') : null,
        end: data && data.length > 0 ? utils.RangePicker.dateFormat(data[1], 'YYYY-MM-DD HH:mm:ss') : null
      }
    }
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['panelChange']
}

export const enterParkTime = {
  type: 'RangePicker',
  field: 'enterParkTime',
  title: '入园时间',
  value: [],
  props: {
    placeholder: ['年-月-日', '年-月-日'],
    format: 'YYYY-MM-DD',
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    outPutFormatFn: function (data) {
      return {
        begin: data && data.length > 0 ? utils.RangePicker.dateFormat(data[0]) : null,
        end: data && data.length > 0 ? utils.RangePicker.dateFormat(data[1]) : null
      }
    }
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['panelChange']
}
export const leaveDate = {
  type: 'MonthPicker',
  field: 'dateStr',
  title: '请假时间',
  value: '',
  props: {
    placeholder: '年-月',
    format: 'YYYY-MM',
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    outPutFormatFn: function (data) {
      return data && data._isAMomentObject ? moment(data['_d']).format('YYYY-MM') : null
    }
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['panelChange']
}

export const rangeDeleteTime = {
  type: 'RangePicker',
  field: 'rangeDeleteTime',
  title: '作废时间',
  value: [],
  wrap: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 }
  },
  props: {
    placeholder: ['年-月-日 时:分:秒', '年-月-日 时:分:秒'],
    format: 'YYYY-MM-DD HH:mm:ss',
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    outPutFormatFn: function (data) {
      return {
        begin: data && data.length > 0 ? utils.RangePicker.dateFormat(data[0], 'YYYY-MM-DD HH:mm:ss') : null,
        end: data && data.length > 0 ? utils.RangePicker.dateFormat(data[1], 'YYYY-MM-DD HH:mm:ss') : null
      }
    }
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['panelChange']
}

export const date = {
  type: 'RangePicker',
  field: 'date',
  title: '日期',
  value: [],
  props: {
    placeholder: ['年-月-日', '年-月-日'],
    format: 'YYYY-MM-DD',
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    outPutFormatFn: function (data) {
      return {
        begin: data && data.length > 0 ? utils.RangePicker.dateFormat(data[0]) : null,
        end: data && data.length > 0 ? utils.RangePicker.dateFormat(data[1]) : null
      }
    }
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['panelChange']
}
