import { utils } from '../utils/formUtil.js'
import { formItemType } from '../enum.js'

export const paymentTime = {
  type: 'DatePicker',
  field: 'paymentTime',
  title: '付款时间',
  value: [],
  props: {
    placeholder: '年-月-日',
    format: 'YYYY-MM-DD',
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    outPutFormatFn: function (data) {
      return data && data.length > 0 ? data : null
    }
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['panelChange']
}

export const orderCreateTime = {
  type: 'DatePicker',
  field: 'orderCreateTime',
  title: '订单创建时间',
  value: [],
  props: {
    placeholder: '年-月-日',
    format: 'YYYY-MM-DD',
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    outPutFormatFn: function (data) {
      return data && data.length > 0 ? data : null
    }
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['panelChange']
}

export const enterTime = {
  type: 'DatePicker',
  field: 'enterTime',
  title: '入园时间',
  value: [],
  props: {
    placeholder: '年-月-日',
    format: 'YYYY-MM-DD',
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    validate: [
      // {
      //   trigger: 'change',
      //   mode: 'required',
      //   message: '入园时间必选！',
      //   required: true,
      //   type: 'object'
      // }
    ],
    outPutFormatFn: function (data) {
      return data && data.length > 0 ? data : null
    }
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['panelChange']
}

export const enterTimeOfRegister = {
  type: 'DatePicker',
  field: 'enterTimeOfRegister',
  title: '入园时间',
  value: [],
  props: {
    placeholder: '年-月-日',
    format: 'YYYY-MM-DD',
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    validate: [
      {
        trigger: 'change',
        mode: 'required',
        message: '入园时间必选！',
        required: true,
        type: 'object'
      }
    ],
    outPutFormatFn: function (data) {
      return data && data.length > 0 ? data : null
    }
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['panelChange']
}

export const birthdayTime = {
  type: 'DatePicker',
  field: 'birthdayTime',
  title: '生日',
  value: [],
  props: {
    placeholder: '年-月-日',
    format: 'YYYY-MM-DD',
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    validate: [
      {
        trigger: 'change',
        mode: 'required',
        message: '生日必选！',
        required: true,
        type: 'object'
      }
    ],
    outPutFormatFn: function (data) {
      console.log('data', data)
      return data && data.length > 0 ? data : null
    }
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['panelChange']
}

export const refundTime = {
  type: 'DatePicker',
  field: 'refundTime',
  title: '退款时间',
  value: [],
  props: {
    placeholder: '年-月-日',
    format: 'YYYY-MM-DD',
    inputReadOnly: true,
    getCalendarContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.RANGE_PICKER,
    validate: [
      {
        trigger: 'change',
        mode: 'required',
        message: '退款时间必选！',
        required: true,
        type: 'object'
      }
    ],
    outPutFormatFn: function (data) {
      return data && data.length > 0 ? data : null
    }
  },
  col: {
    md: 24,
    xs: 24
  },
  emit: ['panelChange']
}
