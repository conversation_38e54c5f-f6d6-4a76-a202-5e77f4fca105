import { filterOption } from '../utils/formUtil.js'
import { formItemType } from '../enum.js'
import { getGradeOptionAjax } from '../api/index.js'

export const orderStatus = {
  type: 'select',
  field: 'orderStatus',
  title: '订单状态',
  value: '',
  props: {
    allowClear: true,
    showSearch: true,
    filterOption: filterOption,
    getPopupContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.SINGLE_SELECT
    // apiFn: function () {
    //   return getOrderStatusAjax()
    // }
  },
  options: [
    { value: '', label: '全部' },
    { value: '0', label: '正常' },
    { value: '1', label: '作废' }
  ],
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change']
}

export const leaveStatus = {
  type: 'select',
  field: 'status',
  title: '请假状态',
  value: '',
  props: {
    allowClear: true,
    showSearch: true,
    filterOption: filterOption,
    getPopupContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.SINGLE_SELECT
    // apiFn: function () {
    //   return getOrderStatusAjax()
    // }
  },
  options: [
    { value: '', label: '全部' },
    { value: '0', label: '未开始' },
    { value: '1', label: '请假中' },
    { value: '2', label: '已结束' },
    { value: '3', label: '已销假' }
  ],
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change']
}

export const payloadStatus = {
  type: 'select',
  field: 'payloadStatus',
  title: '支付状态',
  value: '',
  props: {
    allowClear: true,
    showSearch: true,
    filterOption: filterOption,
    getPopupContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.SINGLE_SELECT
    // apiFn: function () {
    //   // return getPayloadStatusAjax()
    //   return getPayloadStatusAjax()
    // }
  },
  options: [
    { value: '', label: '全部' },
    { value: '0', label: '未支付' },
    { value: '1', label: '已支付' },
    { value: '3', label: '无需支付' }
  ],
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change']
}

export const payloadStatusFourOptions = {
  type: 'select',
  field: 'payloadStatusFourOptions',
  title: '支付状态',
  value: '',
  props: {
    allowClear: true,
    showSearch: true,
    filterOption: filterOption,
    getPopupContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.SINGLE_SELECT
    // apiFn: function () {
    //   return getPayloadStatusAjaxFour()
    // }
  },
  options: [
    { value: '', label: '全部' },
    { value: '0', label: '未支付' },
    { value: '1', label: '已支付' },
    { value: '2', label: '已退款' }
  ],
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change']
}

export const paymentMethod = {
  type: 'select',
  field: 'paymentMethod',
  title: '付款方式',
  value: '',
  props: {
    allowClear: true,
    showSearch: true,
    filterOption: filterOption,
    getPopupContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.SINGLE_SELECT
    // apiFn: function () {
    //   return getPayloadTypeAjax()
    // }
  },
  options: [
    { value: '', label: '全部' },
    { value: '0', label: '线上-微信' },
    { value: '1', label: '线下-微信' },
    { value: '2', label: '支付宝' },
    { value: '3', label: '现金' },
    { value: '4', label: '银行卡' },
    { value: '5', label: '余额' }
  ],
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change']
}

export const attendanceStatus = {
  type: 'select',
  field: 'attendanceStatus',
  title: '考勤状态',
  value: '',
  props: {
    allowClear: true,
    showSearch: true,
    filterOption: filterOption,
    getPopupContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.SINGLE_SELECT
  },
  options: [
    { value: '', label: '全部' },
    { value: '1', label: '出勤' },
    { value: '0', label: '缺勤' }
  ],
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change']
}

export const sex = {
  type: 'select',
  field: 'sex',
  title: '性别',
  value: '',
  props: {
    allowClear: true,
    showSearch: true,
    filterOption: filterOption,
    getPopupContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.SINGLE_SELECT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '性别必选！',
        required: true,
        type: 'string'
      }
    ]
    // apiFn: function () {
    //   return getSexOptionAjax()
    // }
  },
  options: [
    { value: '1', label: '男' },
    { value: '2', label: '女' }
  ],
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change']
}

export const singleGrade = {
  type: 'select',
  field: 'singleGrade',
  title: '班级',
  value: '',
  props: {
    allowClear: true,
    showSearch: true,
    filterOption: filterOption,
    getPopupContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.SINGLE_SELECT,
    apiFn: function () {
      return getGradeOptionAjax()
    }
  },
  options: [
    // { value: '0', label: '小一班' },
    // { value: '1', label: '中三班' },
    // { value: '2', label: '大五班' }
  ],
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change']
}

export const businessStatus = {
  type: 'select',
  field: 'businessStatus',
  title: '业态',
  value: '',
  props: {
    allowClear: true,
    showSearch: true,
    filterOption: filterOption,
    getPopupContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.SINGLE_SELECT,
    validate: [
      // {
      //   trigger: 'blur',
      //   mode: 'required',
      //   message: '业态必选！',
      //   required: true,
      //   type: 'string'
      // }
    ]
    // apiFn: function () {
    //   return businessStatusOptionAjax()
    // }
  },
  options: [{ value: '1', label: '幼儿园' }],
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change']
}

export const studentType = {
  type: 'select',
  field: 'studentType',
  title: '学生类型',
  value: '',
  props: {
    allowClear: true,
    showSearch: true,
    filterOption: filterOption,
    getPopupContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.SINGLE_SELECT
    // apiFn: function () {
    //   return getOrderStatusAjax()
    // }
  },
  options: [
    { value: '', label: '全部' },
    { value: '0', label: '未签约' },
    { value: '1', label: '已签约' }
  ],
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change']
}

export const registerStatus = {
  type: 'select',
  field: 'registerStatus',
  title: '学生状态',
  value: '',
  props: {
    allowClear: true,
    showSearch: true,
    filterOption: filterOption,
    getPopupContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.SINGLE_SELECT
    // apiFn: function () {
    //   return getOrderStatusAjax()
    // }
  },
  options: [
    { value: '', label: '全部' },
    { value: '0', label: '预报名' },
    { value: '1', label: '在园' },
    { value: '2', label: '退园' }
  ],
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change']
}

export const expenseStandardType = {
  type: 'select',
  field: 'expenseStandardType',
  title: '保育费标准类型',
  value: '',
  props: {
    allowClear: true,
    showSearch: true,
    filterOption: filterOption,
    getPopupContainer: triggerNode => triggerNode.parentNode
  },
  customFiled: {
    FormItemType: formItemType.SINGLE_SELECT
    // apiFn: function () {
    //   return getOrderStatusAjax()
    // }
  },
  options: [
    { value: '', label: '全部' },
    { value: '0', label: '默认值' },
    { value: '1', label: '自定义' }
  ],
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change']
}
