import { formItemType } from '../enum.js'

export const primaryEnterprise = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'primaryEnterprise',
  title: '一级收款企业',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写一级收款企业！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}
export const studentName = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'studentName',
  title: '学生姓名',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '学生姓名必填！',
        required: true,
        type: 'string'
      },

      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写学生姓名！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}

export const studentID = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'studentID',
  title: '学生学号',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '学生学号必填！',
        required: true,
        type: 'string'
      },

      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写学生学号！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['change', 'blur']
}

export const studentContactType = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'studentContactType',
  title: '学生联系方式',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写学生学号！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}

export const payService = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'payService',
  title: '收费项目',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写收费项目！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}

export const orderNumber = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'orderNumber',
  title: '订单编号',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写订单编号！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}

export const paymentTime = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'paymentTime',
  title: '付款时间',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写付款时间！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}

export const orderCreateTime = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'orderCreateTime',
  title: '订单创建时间',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写订单创建时间！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}

export const phone = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'phone',
  title: '联系手机号',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '联系手机号必填！',
        required: true,
        type: 'string'
      },
      {
        trigger: ['blur', 'change'],
        message: '联系手机号格式不正确！',
        type: 'string',
        pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}

export const parentName = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'parentName',
  title: '家长姓名',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写家长姓名！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}

export const nation = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'nation',
  title: '民族',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '民族必填！',
        required: true,
        type: 'string'
      },

      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写民族！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}
export const identityNumber = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'identityNumber',
  title: '身份证号',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '身份证号必填！',
        required: true,
        type: 'string'
      },
      {
        trigger: ['blur', 'change'],
        message: '身份证号输入不合法！',
        type: 'string',
        pattern:
          /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|30|31)|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  },
  emit: ['blur', 'change']
}
export const grade = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'grade',
  title: '班级',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '班级必填！',
        required: true,
        type: 'string'
      },
      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写班级！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}
export const birthday = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'birthday',
  title: '生日',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写生日！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}
export const connectName = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'connectName',
  title: '联系人姓名',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '联系人姓名必填！',
        required: true,
        type: 'string'
      },

      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写联系人姓名！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}
export const fatherName = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'fatherName',
  title: '父亲姓名',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      // {
      //   trigger: 'blur',
      //   mode: 'required',
      //   message: '父亲姓名必填！',
      //   required: true,
      //   type: 'string'
      // },
      // {
      //   trigger: 'blur',
      //   mode: 'required',
      //   message: '请填写父亲姓名！',
      //   required: true,
      //   type: 'string'
      // }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}
export const nationality = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'nationality',
  title: '国籍',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '国籍必填！',
        required: true,
        type: 'string'
      },

      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写国籍！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}
export const fatherPhone = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'fatherPhone',
  title: '父亲电话',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      // {
      //   trigger: 'blur',
      //   mode: 'required',
      //   message: '父亲电话必填！',
      //   required: true,
      //   type: 'string'
      // },

      {
        trigger: ['blur', 'change'],
        message: '父亲电话号格式不正确！',
        type: 'string',
        pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}
export const motherName = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'motherName',
  title: '母亲姓名',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      // {
      //   trigger: 'blur',
      //   mode: 'required',
      //   message: '母亲姓名必填！',
      //   required: true,
      //   type: 'string'
      // },
      // {
      //   trigger: 'blur',
      //   mode: 'required',
      //   message: '请填写母亲姓名！',
      //   required: true,
      //   type: 'string'
      // }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}
export const motherPhone = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'motherPhone',
  title: '母亲电话',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      // {
      //   trigger: 'blur',
      //   mode: 'required',
      //   message: '母亲电话必填！',
      //   required: true,
      //   type: 'string'
      // },

      {
        trigger: ['blur', 'change'],
        message: '母亲电话号格式不正确！',
        type: 'string',
        pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}

export const relation = {
  type: 'input',
  props: {
    placeholder: '请输入'
  },
  field: 'relation',
  title: '关系',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '关系必填！',
        required: true,
        type: 'string'
      },

      {
        trigger: 'blur',
        mode: 'required',
        message: '请填写关系！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 6,
    sm: 12,
    xs: 24
  }
}

export const refundReason = {
  type: 'textarea',
  props: {
    placeholder: '请输入'
  },
  field: 'refundReason',
  title: '退款原因',
  value: '',
  customFiled: {
    FormItemType: formItemType.INPUT,
    validate: [
      {
        trigger: 'blur',
        mode: 'required',
        message: '退款原因必填！',
        required: true,
        type: 'string'
      }
    ]
  },
  col: {
    md: 24,
    xs: 24
  }
}
