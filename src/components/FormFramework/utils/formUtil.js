import moment from 'moment'

/**
 * @param {*} fApi          FormCreate 实例
 * @param {*} toggleStatus  折叠撞开 true：展开；false：收起；
 * @param {*} num           折叠状态下，显示前面的多少个组件，默认4
 * @returns
 */
export function formToggle(fApi, toggleStatus, num = 4) {
  const fields = fApi.fields()
  fields.forEach((fieldName, index) => {
    let isHidden = !toggleStatus && index >= num
    fApi.display(!isHidden, fieldName)
  })
}
export function formatSelectModule(data) {
  if (data && data.length > 0)
    return data.map(item => {
      return {
        id: item.id,
        key: item.id,
        label: item.name,
        value: item.code
      }
    })
}
export function formatYear() {
  // 应产品经理(王宁需求)将options年份改为2022-2031年大区确认协同单
  let year = 2022
  let endYear = year + 10
  let yearArr = []
  for (let index = year; index < endYear; index++) {
    yearArr.push({
      id: index,
      key: index,
      label: index + '年',
      value: index
    })
  }
  return yearArr
}

export function formatMonth() {
  let monthArr = []
  for (let index = 1; index <= 12; index++) {
    monthArr.push({
      id: index,
      key: index,
      label: index + '月',
      value: index
    })
  }
  return monthArr
}

export function filterOption(input, option) {
  return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const date = {
  // 目前决定在converter内转换格式，所以直接return出data
  format: function(date) {
    return date
  },
  timeStamp: function(date) {
    return date && date._isAMomentObject ? moment(date['_d']).valueOf() : ''
  },
  startMonth: function(date, format = 'YYYY-MM-DD') {
    return date && date._isAMomentObject
      ? moment(date['_d'])
          .startOf('month')
          .format(format)
      : ''
  },
  endMonth: function(date, format = 'YYYY-MM-DD') {
    return date && date._isAMomentObject
      ? moment(date['_d'])
          .endOf('month')
          .format(format)
      : ''
  },
  dateFormat: function(date, format = 'YYYY-MM-DD') {
    return date && date._isAMomentObject ? moment(date['_d']).format(format) : ''
  },
  yearFormat: function(date, format = 'YYYY-MM-DD') {
    let Date = date && date._isAMomentObject ? moment(date['_d']).format(format) : ''
    return Date ? Date.split('-')[0] : ''
  },
  monthFormat: function(date, format = 'YYYY-MM-DD') {
    let Date = date && date._isAMomentObject ? moment(date['_d']).format(format) : ''
    return Date ? Date.split('-')[1] : ''
  }
}

const select = {
  init: function(data = []) {
    return data
  },
  // 目前决定在converter内把数组转化为字符串，所以直接return出data
  joinToString: function(data, symbol = ',') {
    return data
  },
  arrToString: function(data = [], symbol = ',') {
    return data.join(symbol)
  }
}

export const utils = {
  RangePicker: date,
  MonthPicker: date,
  select: select
}
