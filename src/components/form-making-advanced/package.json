{"name": "form-making-advanced", "description": "A designer and generator of form base on Vue.js, make form development simple and efficient.", "version": "1.2.20", "author": "<PERSON><PERSON><PERSON><PERSON>", "keywords": ["component", "vue", "form", "element-ui", "auto"], "main": "dist/FormMaking.common.js", "scripts": {"serve": "vue-cli-service serve --open", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "build-bundle": "vue-cli-service build --target lib --name FormMaking ./src/index.js", "editor-bundle": "vue-cli-service build --target lib --name Editor ./src/editorBundle.js", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs"}, "dependencies": {"@antv/g2": "^4.1.1", "ant-design-vue": "^1.4.10", "axios": "^0.18.0", "clipboard": "^2.0.1", "element-ui": "^2.13.2", "lodash": "^4.17.15", "multiparty": "^4.2.1", "normalize.css": "^8.0.0", "qiniu": "^7.2.1", "qiniu-js": "^2.5.1", "vant": "^2.11.1", "viewerjs": "^1.2.0", "vue": "^2.6.5", "vue-i18n": "^5.0.3", "vue-router": "^3.0.1", "vue2-editor": "^2.10.0", "vuedraggable": "^2.16.0"}, "devDependencies": {"@babel/core": "^7.0.1", "@types/ace": "0.0.42", "@vue/cli-plugin-babel": "^3.0.0", "@vue/cli-plugin-eslint": "^3.0.0", "@vue/cli-service": "^3.0.0", "@vuepress/plugin-back-to-top": "^1.2.0", "@vuepress/plugin-google-analytics": "^1.2.0", "babel-core": "^6.26.3", "babel-plugin-component": "^1.1.1", "babel-preset-es2015": "^6.24.1", "core-js": "^2.6.5", "node-sass": "^4.12.0", "rollup": "^0.57.1", "rollup-plugin-babel": "^3.0.7", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-uglify-es": "0.0.1", "rollup-plugin-vue": "^3.0.0", "sass-loader": "^8.0.2", "terser-webpack-plugin": "^1.2.4", "uglifyjs-webpack-plugin": "^2.0.1", "vue-template-compiler": "^2.6.5", "vuepress": "^1.2.0"}, "babel": {"presets": ["@vue/app", ["@babel/preset-env", {"useBuiltIns": "entry"}]]}, "eslintConfig": {"root": true, "extends": ["plugin:vue/essential"], "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}