<!--
 * @Author: mali
 * @Date: 2022-04-25 10:44:07
 * @Description: 表格筛选列
-->
<template>
  <div style="position: relative">
    <a-dropdown
      :trigger="['click']"
      style="margin-bottom: 10px"
      v-model="DropdownVisible"
      :get-popup-container="triggerNode => triggerNode.parentNode"
    >
      <a-menu slot="overlay">
        <a-menu-item v-for="(item, index) in selectColumns" :key="index">
          <a-checkbox
            :checked="item.show"
            @change="
              e => {
                columnsCheck(e.target.checked, item, index)
              }
            "
            :disabled="item.title == '序号'"
            >{{ item.title }}</a-checkbox
          >
        </a-menu-item>
      </a-menu>
      <a-button style="margin-left: 8px"> 筛选列 <a-icon type="down" /> </a-button>
    </a-dropdown>
  </div>
</template>
<script>
import _ from 'lodash'

export default {
  name: 'FilterColumn',
  data() {
    return {
      DropdownVisible: false,
      selectColumns: [],
      widthTable: 0,
      columnsMap: new Map()
    }
  },
  props: {
    filterColumns: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  watch: {
    filterColumns: {
      handler(newList) {
        // 兼容异步数据
        if (this.columnsMap.size === 0 || this.getColumnsLen() === 0) {
          // 未筛选时全部选中的初始表格列
          this.columnsMap.set('originColumns', _.cloneDeep(newList))
          this.setOriginColumnsShowStatus()
        }
        this.selectColumns = this.getOriginColumns()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    getColumnsLen() {
      return this.getOriginColumns().length
    },
    getOriginColumns() {
      return this.columnsMap.get('originColumns')
    },
    setOriginColumnsShowStatus() {
      this.getOriginColumns().forEach(item => {
        this.$set(item, 'show', true)
      })
    },
    // 计算列宽度
    computedColWidth(col) {
      this.widthTable = 0
      col.forEach(item => {
        if (item.width) {
          this.widthTable += parseInt(item.width)
        }
        let itemChildObj = item && item.children
        if (itemChildObj) {
          item.children.forEach(i => {
            this.widthTable += parseInt(i.width)
          })
        }
      })
    },
    // 获取选中后的列
    getSelectedColumns() {
      return this.selectColumns.reduce((cur, prev) => {
        let item = { ...prev }
        if (item.show && delete item.show) {
          cur.push(item)
        }
        return cur
      }, [])
    },
    columnsCheck(checked, data, index) {
      this.selectColumns[index].show = checked
      const newData = this.getSelectedColumns()
      this.computedColWidth(newData)
      this.$emit('render', newData, this.widthTable)
    }
  }
}
</script>
<style lang="less" scoped>
.ant-dropdown-menu {
  height: 250px;
  overflow-y: scroll;
}
</style>
