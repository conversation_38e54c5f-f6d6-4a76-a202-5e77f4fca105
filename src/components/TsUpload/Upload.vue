<template>
  <div class="clearfix">
    <a-upload
      action="#"
      list-type="picture-card"
      :class="editable ? '' : 'upload-readonly'"
      :file-list="list"
      :before-upload="beforeUpload"
      :accept="accept"
      @preview="handlePreview"
      :remove="handleRemove"
      v-decorator="[
        'upload',
        {
          valuePropName: 'list',
          getValueFromEvent: normFile,
          rules: [{ required: true, message: '图片不能为空' }]
        }
      ]"
    >
      <div v-if="list.length < this.maxNum && editable">
        <a-icon type="plus" />
        <div class="ant-upload-text">
          {{ description }}
        </div>
      </div>
    </a-upload>
    <div v-if="list.length == 0 && !editable" style="margin-top: -40px">无</div>
    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>
<script>
const { v4: uuidv4 } = require('uuid')
import { getSignature, getSignatureDownLoad, postUpload } from '@/api/upload-img'
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}
export default {
  name: 'UploadImg',
  data() {
    return {
      accept: '.png, .jpg, .jpeg, .gif',
      previewVisible: false,
      previewImage: '',
      // fileList: [],
      list: [], // 组件内使用文件列表
      signature: {}, // 签名信息
      urlDown: {},
      imgUrl: '', // 预览图地址
      isShowImg: false, // 是否展开蒙层
      previewSrcList: [], // 预览的文件列表
      initialIndex: 0 // 预览文件的顺序
    }
  },
  props: {
    maxNum: {
      type: Number,
      default: 2
    },
    fileList: {
      type: Array,
      default: () => {
        return []
      }
    },
    isShow: {
      // true--默认为大方格样式上传文件  false--为按钮形式
      type: Boolean,
      default: true
    },
    limit: {
      type: Number,
      default: 0
    },
    // 上传的提示文字信息
    description: {
      type: String,
      default: 'Upload'
    },
    demoUrlList: {
      type: Array,
      default: () => []
    },
    // 区分公桶还是私桶
    bucketKey: {
      type: String,
      default: 'dfw-media-public'
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  created() {
    this.getSignature()
  },
  watch: {
    fileList: {
      handler(newVal) {
        this.list = []
        if (newVal.length) {
          newVal.forEach((item, index) => {
            let nameArray = item.split('/')
            let imgName = nameArray[nameArray.length - 1]
            let obj = {
              uid: index + 1,
              name: imgName,
              url: item
            }
            this.list.push(obj)
          })
        } else {
          // this.list = newVal.map((item, index) => {
          //   return {
          //     contentName: item,
          //     contentUrl: item,
          //     contentOrder: index + 1
          //   }
          // })
          // console.log('-->watch:fileList = ' + this.list)
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取签名
    async getSignature() {
      let res = await getSignature({
        bucketKey: 'dfw-media-public'
      })
      if (res.resultCode === '0') {
        this.signature = res.data
      } else {
        this.$message.error(res.resultMsg)
      }
    },
    // 关闭图片预览
    closeViewer() {
      this.isShowImg = false
    },
    // 删除文件
    handleRemove(file) {
      const index = this.list.indexOf(file)
      const newFileList = this.list.slice()
      newFileList.splice(index, 1)
      this.list = newFileList
      // 图片地址
      let imgArr = []
      this.list.forEach(item => {
        imgArr.push(item.url)
      })
      this.$emit('removeUploadList', imgArr)
      return true
    },
    // 获取OSS文件临时链接
    getSignatureUrl(url) {
      let requestUrl =
        '/' +
        url
          .split('/')
          .splice(1)
          .join('/')
      return new Promise((resolve, reject) => {
        getSignatureDownLoad(this.api, requestUrl)
          .then(res => {
            if (res.resultCode === '0') {
              resolve(res.data)
            } else {
              this.$message.error(res.resultMsg)
              reject(res.resultMsg)
            }
          })
          .catch(err => {
            console.log(err)
            reject(err)
          })
      })
    },

    // 上传前调
    beforeUpload(files) {
      if (this.list.length >= this.maxNum) {
        this.$message.error('文件大小不能超过' + this.maxNum + '张')
        return false
      }
      // 上传文件的格式拦截
      let nameArray = files.name.split('.')
      // 文件名后缀
      let extName = nameArray[nameArray.length - 1]
      // 校验是否存在同名文件
      let repeat = false
      this.list.forEach(res => {
        if (res.contentName === files.name) {
          repeat = true
        }
      })
      // if (this.full && repeat) {
      if (repeat) {
        // 只有绑定值为对象形式才校验文件名重复
        this.$message.error('不能上传同名的文件！')
        return
      }
      // const loading = this.$loading({
      //   text: '上传中...'
      // })
      // 拷贝一份signature信息，避免当前操作影响下次上传
      let signatureCache = { ...this.signature }
      let formData = new FormData()
      let formDataArr = Object.keys(signatureCache)
      let url = ''
      let appKey = ''
      // 生成OSS上存储的文件名
      let randomFilename = `${uuidv4()}.${extName}`
      if (signatureCache['key']) {
        signatureCache['key'] = `${signatureCache['key']}${randomFilename}`
      }
      if (signatureCache['Content-Type']) {
        formData.append('Content-Type', files.type)
      }
      formDataArr.forEach(item => {
        console.log('formDataArr item ', item)
        if (item !== 'form-action') {
          formData.append(item, signatureCache[item])
        } else {
          appKey = signatureCache[item].match(/https:\/\/(\S*)\.i\.tasly/)[1]
          url = signatureCache[item]
        }
      })
      formData.append('file', files)
      postUpload(formData, url).then(res => {
        console.log('postUpload 开始 ')
        // // loading.close()
        let filesObj = {
          uid: this.list.length + 1,
          name: files.name,
          url: `https://api.i.tasly.com/swift/v1/${appKey}/${this.bucketKey}/${signatureCache['key']}`
        }
        this.list.push(filesObj)
        console.log('postUpload 结束 ', this.list)
        let arr = []
        this.list.forEach(item => {
          arr.push(item.url)
        })
        // 图片地址
        let imgArr = []
        this.list.forEach(item => {
          imgArr.push(item.url)
        })
        this.$emit('updateUploadList', imgArr)
      })
      return false
    },
    normFile(e) {
      // return e && e.fileList
    },
    handleCancel() {
      this.previewVisible = false
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj)
      }
      this.previewImage = file.url || file.preview
      this.previewVisible = true
    }
  }
}
</script>
<style>
/* you can make up upload button and sample style by using stylesheets */
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
.upload-readonly .anticon.anticon-delete {
  display: none;
}
</style>
