<template>
  <div>
    <a-popover>
      <template slot="content">
        <div style="cursor: pointer" @click="handleLogout"><a-icon type="logout" />{{ $t('menu.account.logout') }}</div>
      </template>
      <p class="username-wrap">
        <a-avatar :size="32" icon="user" />
        <span style="margin-left: 6px">{{ userName }}</span>
      </p>
    </a-popover>
    <!-- <a-avatar :size="32" icon="user" />
    {{ userName }}

     -->
  </div>
</template>
<script>
import { Modal } from 'ant-design-vue'
export default {
  name: 'TslLogout',
  computed: {
    userName() {
      return localStorage.getItem('message_userName')
    }
  },
  methods: {
    handleLogout() {
      const self = this

      Modal.confirm({
        title: this.$t('layouts.usermenu.dialog.title'),
        content: this.$t('layouts.usermenu.dialog.content'),
        onOk() {
          self.$emit('comfirm')
        },
        onCancel() {}
      })
    }
  }
}
</script>
<style scoped>
.username-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
