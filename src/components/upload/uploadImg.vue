<template>
  <div class="hello">
    <t-ui-uploadimg
      :file-list.sync="uploadList"
      description="图片限制最大 1M"
      @updateUploadList="updateUploadList"
      @removeUploadList="removeUploadList"
      :max-num="2"
      :show-remove-icon="true"
    ></t-ui-uploadimg>
  </div>
</template>

<script>
export default {
  name: 'UploadImgDemo',
  props: {
    msg: String
  },
  data() {
    return {
      uploadList: []
    }
  },
  methods: {
    updateUploadList(param) {
      this.uploadList = param
      console.log('this.uploadList', this.uploadList)
    },
    removeUploadList(param) {
      this.uploadList = param
      console.log('this.uploadList', this.uploadList)
    }
  }
}
</script>

<style scoped>
h3 {
  margin: 40px 0 0;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
</style>
