<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col
              :xs="queryColItemLayout.xs"
              :sm="queryColItemLayout.sm"
              :md="queryColItemLayout.md"
              :lg="queryColItemLayout.lg"
            >
              <a-form-item label="规则编号">
                <a-input v-model="queryParam.id" placeholder="" />
              </a-form-item>
            </a-col>
            <a-col
              :xs="queryColItemLayout.xs"
              :sm="queryColItemLayout.sm"
              :md="queryColItemLayout.md"
              :lg="queryColItemLayout.lg"
            >
              <a-form-item label="使用状态">
                <a-select v-model="queryParam.status" placeholder="请选择" default-value="0">
                  <a-select-option value="0">全部</a-select-option>
                  <a-select-option value="1">关闭</a-select-option>
                  <a-select-option value="2">运行中</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col
                :xs="queryColItemLayout.xs"
                :sm="queryColItemLayout.sm"
                :md="queryColItemLayout.md"
                :lg="queryColItemLayout.lg"
              >
                <a-form-item label="调用次数">
                  <a-input-number v-model="queryParam.callNo" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col
                :xs="queryColItemLayout.xs"
                :sm="queryColItemLayout.sm"
                :md="queryColItemLayout.md"
                :lg="queryColItemLayout.lg"
              >
                <a-form-item label="更新日期">
                  <a-date-picker v-model="queryParam.date" style="width: 100%" placeholder="请输入更新日期" />
                </a-form-item>
              </a-col>
              <a-col
                :xs="queryColItemLayout.xs"
                :sm="queryColItemLayout.sm"
                :md="queryColItemLayout.md"
                :lg="queryColItemLayout.lg"
              >
                <a-form-item label="使用状态">
                  <a-select v-model="queryParam.useStatus" placeholder="请选择" default-value="0">
                    <a-select-option value="0">全部</a-select-option>
                    <a-select-option value="1">关闭</a-select-option>
                    <a-select-option value="2">运行中</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :xs="queryColItemLayout.xs"
                :sm="queryColItemLayout.sm"
                :md="queryColItemLayout.md"
                :lg="queryColItemLayout.lg"
              >
                <a-form-item label="使用状态">
                  <a-select placeholder="请选择" default-value="0">
                    <a-select-option value="0">全部</a-select-option>
                    <a-select-option value="1">关闭</a-select-option>
                    <a-select-option value="2">运行中</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :xs="queryColItemLayout.xs"
                :sm="queryColItemLayout.sm"
                :md="queryColItemLayout.md"
                :lg="queryColItemLayout.lg"
              >
                <a-form-item label="日期范围">
                  <a-range-picker @change="handleDateRangeChange" />
                </a-form-item>
              </a-col>
            </template>
            <a-col
              :md="(!advanced && queryColItemLayout.md) || queryColItemLayout.xs"
              :sm="queryColItemLayout.xs"
              :lg="(!advanced && queryColItemLayout.lg) || queryColItemLayout.xs"
            >
              <span
                class="table-page-search-submitButtons"
                :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <a-button type="primary" @click="handleQuery">查询</a-button>
                <a-button style="margin-left: 8px" @click="() => (this.queryParam = {})">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button type="primary" icon="plus" @click="handleAdd">新建</a-button>
        <Authorized :authority="[1]">
          <a-button type="primary" icon="plus">管理员权限测试</a-button>
        </Authorized>
        <Authorized :authority="[2]">
          <a-button type="primary" icon="plus">普通用户权限测试</a-button>
        </Authorized>
        <a-button type="primary" icon="download" @click="handleExport">导出</a-button>
        <a-dropdown v-action:edit v-if="selectedRowKeys.length > 0">
          <a-menu slot="overlay">
            <a-menu-item key="1"> <a-icon type="delete" />删除 </a-menu-item>
            <!-- lock | unlock -->
            <a-menu-item key="2"> <a-icon type="lock" />锁定 </a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px"
            >批量操作
            <a-icon type="down" />
          </a-button>
        </a-dropdown>
      </div>
      <s-table
        ref="table"
        size="default"
        row-key="key"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :row-selection="rowSelection"
        show-pagination="auto"
      >
        <span slot="serial" slot-scope="text, record, index">{{ index + 1 }}</span>
        <span slot="status" slot-scope="text">
          <a-badge :status="text | statusTypeFilter" :text="text | statusFilter" />
        </span>
        <span slot="description" slot-scope="text">
          <ellipsis :length="4" tooltip>{{ text }}</ellipsis>
        </span>
        <span slot="action" slot-scope="text, record">
          <template>
            <a @click="handleEdit(record)">配置</a>
            <a-divider type="vertical" />
            <a @click="handleSub(record)">订阅报警</a>
          </template>
        </span>
        <span slot="action" slot-scope="text, record">
          <template>
            <router-link :to="{ name: 'BaseForm' }">编辑</router-link>
            <!-- <a @click="handleEdit(record)">编辑</a> -->
            <a-divider type="vertical" />
            <router-link :to="{ name: 'ProfileBasic' }">详情</router-link>
            <a-divider type="vertical" />
            <a @click="openDisableModal(record)" href="javascript:;">禁用</a>
            <a-divider v-if="$auth('table.delete')" type="vertical" />
            <a v-if="$auth('table.delete')" @click="openDeleteModal(record)" href="javascript:;">删除</a>
          </template>
          <!-- <a-dropdown>
            <a class="ant-dropdown-link"
              >更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a href="javascript:;">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a href="javascript:;">禁用</a>
              </a-menu-item>
              <a-menu-item v-if="$auth('table.delete')">
                <a href="javascript:;">删除</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown> -->
        </span>
      </s-table>
      <create-form
        ref="createModal"
        :visible="visible"
        :loading="confirmLoading"
        :model="mdl"
        @cancel="handleCancel"
        @ok="handleOk"
      />
      <step-by-step-modal ref="modal" @ok="handleOk" />
    </a-card>
    <a-modal
      title=""
      :visible="deleteModalVisible"
      :confirm-loading="confirmLoading"
      ok-text="确认"
      cancel-text="取消"
      @ok="handleDelete"
      @cancel="deleteModalVisible = false"
    >
      <p>您确定删除此条数据吗？</p>
    </a-modal>
    <a-modal
      title=""
      :visible="disableModalVisible"
      :confirm-loading="confirmLoading"
      ok-text="确认"
      cancel-text="取消"
      @ok="handleDisable"
      @cancel="disableModalVisible = false"
    >
      <p>您确定禁用此条数据吗？</p>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import {
  formItemLayout,
  queryFormItemLayout,
  queryColItemLayout,
  buttonColItemLayout,
  rowGutter
} from '../../config/itemLayoutConfig'
import moment from 'moment'
import { STable, Ellipsis } from '@/components'
import { getRoleList, getServiceList } from '@/api/manage'

import StepByStepModal from './modules/StepByStepModal'
import CreateForm from './modules/CreateForm'
import { tableColumns } from './columns'
import _ from 'lodash'

const statusMap = {
  0: {
    status: 'default',
    text: '关闭'
  },
  1: {
    status: 'processing',
    text: '运行中'
  },
  2: {
    status: 'success',
    text: '已上线'
  },
  3: {
    status: 'error',
    text: '异常'
  }
}

export default {
  name: 'TableList',
  components: {
    STable,
    Ellipsis,
    CreateForm,
    StepByStepModal
  },
  data() {
    // this.columns = columns
    return {
      columns: tableColumns,
      // layout
      formItemLayout,
      queryFormItemLayout,
      queryColItemLayout,
      buttonColItemLayout,
      rowGutter,
      // create model
      visible: false,
      confirmLoading: false,
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      queryBtnLoading: false,
      // 查询参数
      queryParam: {},
      // 真实查询参数，只有点击查询按钮时值才改变
      realQueryParam: {},
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.realQueryParam)
        console.log('loadData request parameters:', requestParameters)
        return getServiceList(requestParameters).then(res => {
          return res.result
        })
      },
      selectedRowKeys: [],
      selectedRows: [],

      deleteModalVisible: false,
      deleteRecode: null,

      disableModalVisible: false,
      disableRecode: null
    }
  },
  filters: {
    statusFilter(type) {
      return statusMap[type].text
    },
    statusTypeFilter(type) {
      return statusMap[type].status
    }
  },
  created() {
    getRoleList({ t: new Date() })
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  methods: {
    handleDateRangeChange(data, dataString) {},
    handleAdd() {
      this.mdl = null
      this.visible = true
    },
    handleEdit(record) {
      this.visible = true
      this.mdl = { ...record }
    },
    handleOk() {
      const form = this.$refs.createModal.form
      this.confirmLoading = true
      form.validateFields((errors, values) => {
        if (!errors) {
          console.log('values', values)
          if (values.id > 0) {
            // 修改 e.g.
            new Promise((resolve, reject) => {
              setTimeout(() => {
                resolve()
              }, 1000)
            }).then(res => {
              this.visible = false
              this.confirmLoading = false
              // 重置表单数据
              form.resetFields()
              // 刷新表格
              this.$refs.table.refresh()

              this.$message.info('修改成功')
            })
          } else {
            // 新增
            new Promise((resolve, reject) => {
              setTimeout(() => {
                resolve()
              }, 1000)
            }).then(res => {
              this.visible = false
              this.confirmLoading = false
              // 重置表单数据
              form.resetFields()
              // 刷新表格
              this.$refs.table.refresh()

              this.$message.info('新增成功')
            })
          }
        } else {
          this.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.visible = false

      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    handleSub(record) {
      if (record.status !== 0) {
        this.$message.info(`${record.no} 订阅成功`)
      } else {
        this.$message.error(`${record.no} 订阅失败，规则已关闭`)
      }
    },
    handleDelete() {
      this.deleteModalVisible = false
      this.$refs.table.refresh()
      this.$message.info('删除成功')
    },
    openDeleteModal(record) {
      this.deleteRecode = record
      this.deleteModalVisible = true
    },
    handleDisable() {
      this.disableModalVisible = false
      this.$refs.table.refresh()
      this.$message.info('禁用成功')
    },
    openDisableModal(record) {
      this.disableRecode = record
      this.disableModalVisible = true
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    resetSearchForm() {
      this.queryParam = {
        date: moment(new Date())
      }
      this.realQueryParam = { ...this.queryParam }
    },
    handleQuery: _.debounce(
      function() {
        this.realQueryParam = { ...this.queryParam }
        this.$refs.table.refresh(true)
      },
      500,
      {
        leading: true,
        trailing: false
      }
    ),
    handleExport: _.debounce(
      function() {
        // 发送下载请求
      },
      500,
      {
        leading: true,
        trailing: false
      }
    )
  }
}
</script>
<style>
.ant-calendar-picker {
  width: 100%;
}
@media (max-width: 991px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
  }
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > th,
  .ant-table-thead > tr > td,
  .ant-table-tbody > tr > td {
    white-space: pre;
  }
  .ant-table-thead > tr > th > span,
  .ant-table-tbody > tr > th > span,
  .ant-table-thead > tr > td > span,
  .ant-table-tbody > tr > td > span {
    display: block;
  }
}
</style>
