<template>
  <a-modal v-model="visibility" title="销假" :mask-closable="false" :footer="null" :closable="false">
    <a-spin :spinning="loading">
      <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="请假类型"> 长期病假 </a-form-model-item>
        <a-form-model-item label="学生姓名"> {{ info.studentName || '-' }} </a-form-model-item>
        <a-form-model-item label="学号"> {{ info.studentNo || '-' }} </a-form-model-item>
        <a-form-model-item label="开始日期"> {{ info.realStartDateStr || '-' }} </a-form-model-item>
        <a-form-model-item label="结束日期">
          <a-date-picker @change="onChange" value-format="YYYY-MM-DD" :value="defaultDate" />
        </a-form-model-item>
        <div class="text-right">
          <a-button type="default" v-debounce:[cancelHandler] style="margin-right: 10px">取消</a-button>
          <a-button type="primary" v-debounce:[saveHandler]>确定</a-button>
        </div>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { returnClass } from '@/request/api/leave-manage'

export default {
  props: {
    visibility: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    visibility(newVal) {
      if(!newVal) {
        console.log('newVal', newVal)
        this.form.endDate = ''
        this.defaultDate = ''
        console.log('defaultDate', this.defaultDate)
      }
    }
  },
  data() {
    return {
      loading: false,
      defaultDate: '',
      form: {
        endDate: ''
      },
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      fetching: false,
      students: [],
      searchName: '',
      selectedStudent: {}
    }
  },
  methods: {
    saveHandler() {
      if (this.validateForm()) {
        this.loading = true
        returnClass({
          id: this.info.id,
          returnEndDateStr: this.form.endDate
        })
          .then(res => {
            this.loading = false
            if (res.resultCode === 'SUCCESS') {
              this.$emit('update:visibility', false)
              this.$emit('success')
              this.$message.success('销假成功')
            } else {
              this.$message.error(res.data?.errorMessage || '销假失败，请稍后重试')
            }
          })
          .catch(() => {
            this.loading = false
            this.$message.error('销假失败，请稍后重试')
          })
      }
    },
    cancelHandler() {
      this.$emit('update:visibility', false)
    },
    onChange(val) {
      console.log(val)
      this.form.endDate = val
      this.defaultDate = val
    },
    handleChange(id) {
      this.selectedStudent = this.students.find(student => student.id === id)
      this.form.studentName = this.selectedStudent?.name
    },
    validateForm() {
      let isValid = true
      if (!this.form.endDate) {
        this.$message.error('请选择结束日期')
        isValid = false
      }

      return isValid
    }
  }
}
</script>
