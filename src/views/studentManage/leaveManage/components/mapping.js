import { utils } from '@/components/FormFramework/utils/formUtil.js'
function inputUtils(obj, key) {
  return obj[key] ? obj[key].value : ''
}
function timeUtils(resObj, key, index, type) {
  return resObj[key] ? utils[resObj[key].type].format(resObj[key].value[index], type) : ''
}
export function searchMapping(resObj) {
  console.log(resObj)
  return {
    studentName: inputUtils(resObj, 'studentName'), //学生姓名
    studentNo: inputUtils(resObj, 'studentID'), //学生学号
    dateStr: inputUtils(resObj, 'dateStr'), //请假时间
    status: inputUtils(resObj, 'status') //请假状态
  }
}
