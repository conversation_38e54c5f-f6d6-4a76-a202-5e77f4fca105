<template>
  <a-modal v-model="visibility" title="长期病假" :mask-closable="false" :footer="null" :closable="false">
    <a-spin :spinning="loading">
      <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="病假时间">
          <a-range-picker
            :placeholder="['开始年月', '结束年月']"
            :mode="['month', 'month']"
            :value="leaveDate"
            @panelChange="onChange"
            format="YYYY-MM"
          />
        </a-form-model-item>
        <a-form-model-item label="学生姓名">
          <a-select
            placeholder="输入学生姓名进行搜索"
            :value="form.studentName"
            :filter-option="false"
            :not-found-content="fetching ? undefined : null"
            :show-search="true"
            @search="fetchUser"
            @change="handleChange"
            @focus="fetchUser"
          >
            <a-spin v-if="fetching" slot="notFoundContent" size="small" />
            <a-select-option v-for="d in students" :key="d.id" :value="d.id">
              {{ d.studentName }} - {{ d.studentNo }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="学号"> {{ selectedStudent.studentNo || '-' }} </a-form-model-item>
        <a-form-model-item label="出生日期"> {{ selectedStudent.birthday || '-' }} </a-form-model-item>
        <div class="text-right">
          <a-button type="default" v-debounce:[cancelHandler] style="margin-right: 10px">取消</a-button>
          <a-button type="primary" v-debounce:[saveHandler]>确定</a-button>
        </div>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { getStudentsByName, askForLeave } from '@/request/api/leave-manage'

export default {
  props: {
    visibility: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      form: {
        studentName: undefined,
        startDate: '',
        endDate: ''
      },
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      fetching: false,
      students: [],
      searchName: '',
      selectedStudent: {},
      leaveDate: []
    }
  },
  methods: {
    saveHandler() {
      console.log('this.form', this.form)
      if (this.validateForm()) {
        this.loading = true
        askForLeave({
          studentId: this.selectedStudent.id,
          startDateStr: this.form.startDate,
          endDateStr: this.form.endDate
        })
          .then(res => {
            this.loading = false
            if (res.resultCode === 'SUCCESS') {
              this.cancelHandler()
              this.$emit('success')
              this.$message.success('请假成功')
            } else {
              this.$message.error(res.data?.errorMessage || '请假失败，请稍后重试')
            }
          })
          .catch(() => {
            this.$message('请假失败，请稍后重试')
            this.loading = false
          })
      }
    },
    cancelHandler() {
      this.$emit('update:visibility', false)
      this.form = {
        studentName: undefined,
        startDate: '',
        endDate: ''
      }
      this.selectedStudent = {}
      this.leaveDate = []
    },
    onChange(val) {
      console.log(val)
      this.leaveDate = val
      if (val.length > 0) {
        this.form.startDate = val[0].format('YYYY-MM')
        this.form.endDate = val[1].format('YYYY-MM')
        console.log(this.form)
      } else {
        this.form.startDate = ''
        this.form.endDate = ''
      }
    },
    fetchUser(name) {
      getStudentsByName(name).then(res => {
        console.log(res)
        this.students = res.data.slice(0, 100)
      })
    },
    handleChange(id) {
      this.selectedStudent = this.students.find(student => student.id === id)
      console.log('this.selectedStudent', this.selectedStudent)
      this.form.studentName = this.selectedStudent?.studentName
    },
    validateForm() {
      let isValid = true
      if (!this.form.startDate || !this.form.endDate) {
        this.$message.error('请选择病假时间')
        isValid = false
      } else if (!this.form.studentName) {
        this.$message.error('请选择请假学生')
        isValid = false
      }

      return isValid
    }
  }
}
</script>
