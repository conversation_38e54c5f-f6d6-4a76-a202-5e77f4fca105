<template>
  <div class="leave-manage">
    <Form ref="searchForm" @submitFn="submitFn" />
    <a-card style="margin-top: 12px">
      <a-table
        :loading="tableLoading"
        :row-key="record => record.id"
        :pagination="false"
        :columns="columns"
        :data-source="tableData"
        bordered
      >
        <template slot="title">
          <div class="table-header-wrap" style="display: flex; justify-content: flex-end">
            <div style="width: 100%; display: flex; justify-content: space-between">
              <a-button type="primary" @click="askLeave">请假</a-button>
            </div>
          </div>
        </template>
        <template slot="leaveDate" slot-scope="text, record">
          {{ record.startDateStr }} 至 {{ record.endDateStr }}
        </template>
        <template slot="actualLeaveDate" slot-scope="text, record">
          {{ record.realStartDateStr }} 至 {{ record.realEndDateStr }}
        </template>
        <template slot="operation" slot-scope="text, record">
          <div class="btn-group">
            <a-popconfirm v-if="record.status === 0" title="确定删除这条请假记录吗？" @confirm="deleteHandler(record)">
              <a-button type="link">删除</a-button>
            </a-popconfirm>
            <a-button type="link" v-if="record.status === 1" @click="returnToClass(record)">销假</a-button>
          </div>
        </template>
        <template slot="footer">
          <!-- 分页指示器 -->
          <a-pagination
            style="margin-top: 10px; text-align: right"
            :page-size.sync="pagination.pageSize"
            :current="pagination.current"
            show-size-changer
            @showSizeChange="onShowSizeChange"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total"
            :show-total="total => `共 ${total} 条`"
            @change="onPaginationChange"
            show-quick-jumper
          >
            <template slot="buildOptionText" slot-scope="props">
              <span v-if="props.value !== '50'">{{ props.value }}条/页</span>
            </template>
          </a-pagination>
        </template>
      </a-table>
    </a-card>
    <AskForLeaveModal :visibility.sync="askLeaveVisibility" @success="getLeaveList" />
    <ReturnClassModal
      :visibility.sync="returnClassModalVisibility"
      :info="selectedData"
      @success="returnClassSucceed"
    />
  </div>
</template>
<script>
import { searchMapping } from './components/mapping.js'
import Form from './components/form.vue'
import AskForLeaveModal from './components/askForLeaveModal.vue'
import ReturnClassModal from './components/returnClassModal.vue'
import { tableColumns } from './columns'
import { getLeaveList, deleteLeave } from '@/request/api/leave-manage'

export default {
  data() {
    return {
      msg: '这里是请假管理页面',
      tableLoading: false,
      columns: tableColumns,
      tableData: [],
      pagination: {
        pageSizeOptions: ['5', '10', '20', '40', '80', '100'],
        pageSize: 10,
        current: 1,
        total: 0
      },
      askLeaveVisibility: false,
      returnClassModalVisibility: false,
      searchData: {},
      selectedData: {}
    }
  },
  components: {
    Form,
    AskForLeaveModal,
    ReturnClassModal
  },
  mounted() {
    this.getLeaveList()
  },
  methods: {
    submitFn(searchData = {}) {
      this.searchData = searchMapping(searchData)
      this.pagination.current = 1
      this.getLeaveList()
    },
    getLeaveList() {
      this.tableLoading = true
      getLeaveList({
        filter: { ...this.searchData },
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.current
      })
        .then(res => {
          this.tableLoading = false
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    onShowSizeChange(current, size) {
      this.pagination.pageSize = size
      this.pagination.current = 1
      this.getLeaveList()
    },
    onPaginationChange(current) {
      this.pagination.current = current
      this.getLeaveList()
    },
    deleteHandler(item) {
      deleteLeave(item.id)
        .then(res => {
          if (res.resultCode === 'SUCCESS') {
            this.$message.success('删除成功')
            this.getLeaveList()
          } else {
            this.$message.error(res.data?.errorMessage || '删除失败，请稍后重试')
          }
        })
        .catch(res => {
          this.$message.error('删除失败，请稍后重试')
        })
    },
    returnToClass(item) {
      this.returnClassModalVisibility = true
      this.selectedData = { ...item }
    },
    returnClassSucceed() {
      this.getLeaveList()
    },
    askLeave() {
      this.askLeaveVisibility = true
    }
  }
}
</script>
<style lang="less" scoped></style>
