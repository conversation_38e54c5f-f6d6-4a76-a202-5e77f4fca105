<template>
  <div class="attendance-statistics">
    <Form ref="searchForm" @submitFn="submitFn" />
    <a-card style="margin-top: 12px">
      <a-table
        :loading="tableLoading"
        :row-key="record => record.key"
        :pagination="false"
        :columns="columns"
        :data-source="tableData"
        bordered
      >
        <template slot="footer">
          <!-- 分页指示器 -->
          <a-pagination
            style="margin-top: 10px; text-align: right"
            :page-size.sync="pagination.pageSize"
            :current="pagination.current"
            show-size-changer
            @showSizeChange="onShowSizeChange"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total"
            :show-total="total => `共 ${total} 条`"
            @change="onPaginationChange"
            show-quick-jumper
          >
            <template slot="buildOptionText" slot-scope="props">
              <span v-if="props.value !== '50'">{{ props.value }}条/页</span>
            </template>
          </a-pagination>
        </template>
      </a-table>
    </a-card>
  </div>
</template>
<script>
import Form from './component/search.vue'
import { columns } from './tableColumns.js'
import { searchMapping } from './component/searchMapping.js'
export default {
  components: {
    Form
  },
  data() {
    return {
      msg: '这里是考勤统计管理页面',
      searchObj: {},
      tableLoading: false,
      columns,
      tableData: [],
      pagination: {
        //分页对象
        pageSizeOptions: ['10', '20', '40', '80', '100'],
        pageSize: 10,
        current: 1,
        total: 0
      }
    }
  },
  mounted() {
    this.$refs.searchForm.submitFn()
  },
  methods: {
    getTableData() {
      this.tableLoading = true
      let { getTableData } = this.$api.attendanceStatisticsAjax
      this.tableData = []
      getTableData({
        filter: this.searchObj,
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(res => {
          let { data } = res
          this.tableData = data.records
          this.pagination.current = data.current
          this.pagination.pageSize = data.size
          this.pagination.total = data.total
        })
        .catch(err => {})
        .finally(() => {
          this.tableLoading = false
        })
    },
    submitFn(data) {
      this.pagination.current = 1
      this.searchObj = searchMapping(data)
      this.getTableData()
    },
    onShowSizeChange(current, pageSize) {
      this.pagination.current = 1
      this.pagination.pageSize = pageSize
      this.getTableData()
    },
    onPaginationChange(pageNo) {
      this.pagination.current = pageNo
      this.getTableData()
    }
  }
}
</script>
<style lang="less" scoped>
.attendance-statistics {
}
</style>
