import { utils } from '@/components/FormFramework/utils/formUtil.js'
function inputUtils(obj, key) {
  return obj[key] ? obj[key].value : ''
}
function timeUtils(resObj, key, index, type) {
  return resObj[key] ? utils[resObj[key].type].format(resObj[key].value[index], type) : ''
}
export function searchMapping(resObj) {
  return {
    studentName: inputUtils(resObj, 'studentName'), //学生姓名
    studentClass: inputUtils(resObj, 'singleGrade'), //学生班级
    startDateStr: timeUtils(resObj, 'date', 'begin', 'YYYY-MM-DD'), //订单创建时间
    endDateStr: timeUtils(resObj, 'date', 'end', 'YYYY-MM-DD'), //订单创建时间
    studentNo: inputUtils(resObj, 'studentID'), //学生学号
    status: inputUtils(resObj, 'attendanceStatus') //考勤状态
  }
}
