import { utils } from '@/components/FormFramework/utils/formUtil.js'
function inputUtils(obj, key) {
  return obj[key] ? obj[key].value : ''
}
function timeUtils(resObj, key, index, type) {
  return resObj[key] ? utils[resObj[key].type].format(resObj[key].value[index], type) : ''
}
export function searchMapping(resObj) {
  console.log(resObj)
  return {
    contactPhoneNumber: inputUtils(resObj, 'phone'), //订单状态
    parentName: inputUtils(resObj, 'parentName'), //支付状态
    studentClass: inputUtils(resObj, 'grade'), //付款方式
    studentName: inputUtils(resObj, 'studentName'), //学生姓名
    studentNo: inputUtils(resObj, 'studentID'), //学生学号
    startAdmissionDate: timeUtils(resObj, 'enterParkTime', 'begin', 'YYYY-MM-DD'), //订单创建时间
    endAdmissionDate: timeUtils(resObj, 'enterParkTime', 'end', 'YYYY-MM-DD'), //订单创建时间
    // studentType: inputUtils(resObj, 'studentType'), //学生类型
    registerStatus: inputUtils(resObj, 'registerStatus') //学生状态
  }
}
