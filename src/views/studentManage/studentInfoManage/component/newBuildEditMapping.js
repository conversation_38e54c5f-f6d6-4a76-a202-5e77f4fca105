import { utils } from '@/components/FormFramework/utils/formUtil.js'
function inputUtils(obj, key) {
  return obj[key] ? obj[key].value : ''
}
function timeUtils(resObj, key, index, type) {
  return resObj[key] ? utils[resObj[key].type].format(resObj[key].value[index], type) : ''
}
export function newBuildEditMapping(resObj) {
  console.log(resObj)
  return {
    studentName: inputUtils(resObj, 'studentName'), //学生姓名
    studentNo: inputUtils(resObj, 'studentID'), //学生学号
    studentClass: inputUtils(resObj, 'grade'), //班级
    ethnicGroup: inputUtils(resObj, 'nation'), //民族
    idNumber: inputUtils(resObj, 'identityNumber'), //身份证号
    gender: inputUtils(resObj, 'sex'), //性别
    birthday: inputUtils(resObj, 'birthdayTime'), //生日
    nationality: inputUtils(resObj, 'nationality'), //国籍
    contactName: inputUtils(resObj, 'connectName'), //联系人姓名
    contactPhoneNumber: inputUtils(resObj, 'phone'), //联系手机号
    relation: inputUtils(resObj, 'relation'), //关系
    fatherName: inputUtils(resObj, 'fatherName'), //父亲姓名
    fatherPhoneNumber: inputUtils(resObj, 'fatherPhone'), //父亲电话
    motherName: inputUtils(resObj, 'motherName'), //母亲姓名
    motherPhoneNumber: inputUtils(resObj, 'motherPhone'), //母亲电话
    admissionDate: inputUtils(resObj, 'enterTime'), //入园时间
    businessUnit: inputUtils(resObj, 'businessStatus') //业态
  }
}
