export const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
    width: 80,
    customRender: (text, record, index) => {
      return `${index + 1}`
    }
  },
  {
    title: '学生ID',
    dataIndex: 'id',
    align: 'center'
  },
  {
    title: '学生姓名',
    dataIndex: 'studentName',
    align: 'center'
  },
  {
    title: '学生学号',
    dataIndex: 'studentNo',
    align: 'center'
  },
  {
    title: '班级',
    dataIndex: 'studentClass',
    align: 'center'
  },
  {
    title: '民族',
    dataIndex: 'ethnicGroup',
    align: 'center'
  },
  {
    title: '身份证号',
    dataIndex: 'idNumber',
    align: 'center'
  },
  {
    title: '性别',
    dataIndex: 'genderStr',
    align: 'center'
  },
  {
    title: '生日',
    dataIndex: 'birthday',
    align: 'center'
  },
  {
    title: '国籍',
    dataIndex: 'nationality',
    align: 'center'
  },
  {
    title: '家庭住址',
    dataIndex: 'wholeHomeAddress',
    align: 'center'
  },
  {
    title: '联系人姓名',
    dataIndex: 'contactName',
    align: 'center'
  },
  {
    title: '联系电话',
    dataIndex: 'contactPhoneNumber',
    align: 'center'
  },
  {
    title: '关系',
    dataIndex: 'relation',
    align: 'center'
  },
  {
    title: '父亲姓名',
    dataIndex: 'fatherName',
    align: 'center'
  },
  {
    title: '父亲电话',
    dataIndex: 'fatherPhoneNumber',
    align: 'center'
  },
  {
    title: '母亲姓名',
    dataIndex: 'motherName',
    align: 'center'
  },
  {
    title: '母亲电话',
    dataIndex: 'motherPhoneNumber',
    align: 'center'
  },
  {
    title: '入园时间',
    dataIndex: 'admissionDateStr',
    align: 'center'
  },
  {
    title: '业态',
    dataIndex: 'businessUnitStr',
    align: 'center'
  },
  {
    title: '学生状态',
    dataIndex: 'registerStatusStr',
    align: 'center'
  },
  {
    title: '操作',
    align: 'center',
    dataIndex: 'operation',
    width: 140,
    fixed: 'right',
    scopedSlots: { customRender: 'operation' }
  }
]

export const columnsHistory = [
  {
    title: '修改记录编码',
    dataIndex: 'actionId',
    align: 'center',
    width: 200
  },
  {
    title: '操作人',
    dataIndex: 'updatePerson',
    align: 'center'
  },
  {
    title: '操作时间',
    dataIndex: 'updateTimeStr',
    // scopedSlots: { customRender: 'updateTime' },
    align: 'center'
  },
  {
    title: '编辑字段',
    dataIndex: 'propertyName',
    align: 'center'
  },
  {
    title: '新值',
    dataIndex: 'newVal',
    align: 'center'
  },
  {
    title: '旧值',
    dataIndex: 'oldVal',
    align: 'center'
  }
]
