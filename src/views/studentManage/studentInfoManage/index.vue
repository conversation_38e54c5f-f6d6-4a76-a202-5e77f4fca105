<template>
  <div class="student-info-manage">
    <Form ref="searchForm" @submitFn="submitFn" />
    <a-card style="margin-top: 12px">
      <a-table
        :scroll="{ x: 2600 }"
        :loading="tableLoading"
        :row-key="record => record.key"
        :pagination="false"
        :columns="columns"
        :data-source="tableData"
        bordered
      >
        <template slot="title">
          <div class="table-header-wrap" style="display: flex; justify-content: flex-end">
            <div style="width: 100%; display: flex; justify-content: flex-end">
              <!-- <a-button v-permission="['newbuild']" type="primary" @click="newBuild">新建</a-button> -->
              <a-button v-permission="['exportdata']" type="primary" v-debounce:[exportFile]> 导出 </a-button>
            </div>
          </div>
        </template>
        <template slot="operation" slot-scope="text, record">
          <div class="btn-group">
            <a-button v-permission="['edit']" type="link" @click="editRow(record)"> 编辑 </a-button>
            <a-button
              v-permission="['inthegarden']"
              v-if="record.registerStatus == 0"
              type="link"
              @click="registerRow(record)"
            >
              在园
            </a-button>
            <!-- <a-button v-if="record.registerStatus == 0" type="link" @click="registerRow(record)"> 在园 </a-button> -->
            <a-popconfirm title="确定要退园么?" ok-text="是" cancel-text="否" @confirm="exitRow(record)">
              <a-icon slot="icon" type="question-circle-o" style="color: red" />
              <a-button v-permission="['exit']" v-if="record.registerStatus == 1" type="link"> 退园 </a-button>
              <!-- <a-button v-if="record.registerStatus == 1" type="link"> 退园 </a-button> -->
            </a-popconfirm>
            <a-button v-permission="['watchoperationrecords']" type="link" @click="viewHistory(record)">
              查看操作记录
            </a-button>
          </div>
        </template>
        <template slot="footer">
          <!-- 分页指示器 -->
          <a-pagination
            style="margin-top: 10px; text-align: right"
            :page-size.sync="pagination.pageSize"
            :current="pagination.current"
            show-size-changer
            @showSizeChange="onShowSizeChange"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total"
            :show-total="total => `共 ${total} 条`"
            @change="onPaginationChange"
            show-quick-jumper
          >
            <template slot="buildOptionText" slot-scope="props">
              <span v-if="props.value !== '50'">{{ props.value }}条/页</span>
            </template>
          </a-pagination>
        </template>
      </a-table>
    </a-card>
    <a-modal
      :width="680"
      :closable="false"
      destroy-on-close
      :mask-closable="false"
      v-model="studentInfoVisible"
      title="学生信息"
      :footer="null"
    >
      <a-spin :spinning="spinning">
        <NewBuildEdit ref="modalForm" @disabledOk="disabledOk" @submitFn="submitFnoFNewBuildEdit" />
        <!-- <div style="margin-left: 32px; margin-top: -36px; position: relative"> -->
        <div style="margin-left: 44px; margin-top: -32px; position: relative">
          <!-- <span style="color: #f5222d; position: absolute; left: -11px; top: 11px">*</span> -->
          <span> 家庭地址：</span>
          <a-cascader
            style="width: 213px"
            v-model="defaultValueOptions"
            :options="cascaderOptions"
            placeholder="请选择"
            @change="cascaderOptionsOnChange"
          />
          <a-textarea
            v-model="addressDetail"
            style="margin-top: 6px; width: 40%; display: block; margin-left: 70px; width: 213px"
            placeholder="详细地址"
            auto-size
          />
          <div style="text-align: right">
            <a-button type="default" v-debounce:[handleCancelOfStudentInfo] style="margin-right: 10px">取消</a-button>
            <a-button type="primary" v-debounce:[handleOkOfStudentInfo]>确定</a-button>
          </div>
        </div>
      </a-spin>
    </a-modal>

    <!--在园 -->
    <a-modal
      :width="680"
      :closable="false"
      destroy-on-close
      :mask-closable="false"
      v-model="studentRegisterVisible"
      title="在园"
      :footer="null"
    >
      <a-spin :spinning="spinning">
        <Register ref="modalRegisterForm" @disabledOk="disabledOk" @submitFn="submitOfRegister" />
        <!-- <div style="margin-left: 32px; margin-top: -36px; position: relative"> -->
        <div style="margin-left: 44px; margin-top: -32px; position: relative">
          <!-- <span style="color: #f5222d; position: absolute; left: -11px; top: 11px">*</span> -->
          <span> 家庭地址：</span>
          <a-cascader
            style="width: 213px"
            v-model="defaultValueOptions"
            :options="cascaderOptions"
            placeholder="请选择"
            @change="cascaderOptionsOnChange"
          />
          <a-textarea
            v-model="addressDetail"
            style="margin-top: 6px; width: 40%; display: block; margin-left: 70px; width: 213px"
            placeholder="详细地址"
            auto-size
          />
          <div style="text-align: right">
            <a-button type="default" v-debounce:[handleCancelOfStudentRegister] style="margin-right: 10px"
              >取消</a-button
            >
            <a-button type="primary" v-debounce:[handleOkOfStudentRegister]>确定</a-button>
          </div>
        </div>
      </a-spin>
    </a-modal>
    <a-modal
      :width="1000"
      destroy-on-close
      :mask-closable="false"
      v-model="operateVisible"
      title="操作列表"
      :footer="null"
    >
      <a-table
        :scroll="{ x: 600 }"
        :loading="historyTableLoading"
        :row-key="record => record.key"
        :pagination="false"
        :columns="columnsHistory"
        :data-source="historyTableData"
        bordered
      >
        <!-- <template slot="updateTime" slot-scope="text">
          {{ text | dayjs }}
        </template> -->
      </a-table>
    </a-modal>
  </div>
</template>
<script>
import moment from 'moment'
import { downloadFileUseALabel } from '@/utils/util.js'
import Form from './component/search.vue'
import { searchMapping } from './component/searchMapping.js'
import NewBuildEdit from './component/newBuildEdit.vue'
import Register from './component/register.vue'
import { newBuildEditMapping } from './component/newBuildEditMapping.js'
import { newBuildRegisterMapping } from './component/newBuildRegisterMapping.js'
import { columns, columnsHistory } from './tableColumns.js'
export default {
  data() {
    return {
      msg: '这里是学生信息管理界面',
      defaultValueOptions: [],
      selectedOptions: [],
      cascaderOptions: [
        {
          value: '120000000000',
          label: '天津市',
          children: [
            {
              value: '120100000000',
              label: '市辖区',
              children: [
                {
                  value: '120101000000',
                  label: '和平区'
                },
                {
                  value: '120102000000',
                  label: '河东区'
                },
                {
                  value: '120103000000',
                  label: '河西区'
                },
                {
                  value: '120104000000',
                  label: '南开区'
                },
                {
                  value: '120105000000',
                  label: '河北区'
                },
                {
                  value: '120106000000',
                  label: '红桥区'
                },
                {
                  value: '120110000000',
                  label: '东丽区'
                },
                {
                  value: '120111000000',
                  label: '西青区'
                },
                {
                  value: '120112000000',
                  label: '津南区'
                },
                {
                  value: '120113000000',
                  label: '北辰区'
                },
                {
                  value: '120114000000',
                  label: '武清区'
                },
                {
                  value: '120115000000',
                  label: '宝坻区'
                },
                {
                  value: '120116000000',
                  label: '滨海新区'
                },
                {
                  value: '120117000000',
                  label: '宁河区'
                },
                {
                  value: '120118000000',
                  label: '静海区'
                },
                {
                  value: '120119000000',
                  label: '蓟州区'
                }
              ]
            }
          ]
        }
      ],
      searchObj: {},
      tableLoading: false,
      historyTableLoading: false,
      fileList: [], // 上传文件列表
      columns: columns,
      columnsHistory: columnsHistory,
      tableData: [],
      historyTableData: [],
      pagination: {
        pageSizeOptions: ['10', '20', '40', '80', '100'],
        pageSize: 10,
        current: 1,
        total: 0
      },
      modalFormObj: {},
      modalRegisterFormObj: {},
      rowsObj: {},
      studentRegisterRowsObj: {},
      studentInfoVisible: false,
      studentRegisterVisible: false,
      operateVisible: false,
      options: [],
      addressStr: '',
      addressArr: [],
      addressDetail: '',
      isShowCascader: false,
      okButtonPropsDisabled: false,
      spinning: false
    }
  },
  components: {
    Form,
    NewBuildEdit,
    Register
  },
  created() {},
  mounted() {
    this.$refs.searchForm.submitFn()
  },
  methods: {
    disabledOk(bool) {
      this.okButtonPropsDisabled = bool
    },
    cascaderOptionsOnChange(val, selectedOptions) {
      console.log(val, selectedOptions)
      this.addressArr = val
      this.defaultValueOptions = val
      this.selectedOptions = selectedOptions
    },
    inputFocus() {
      //地址输入框focus事件
      this.isShowCascader = true
    },
    getTableData() {
      //获取表格数据
      this.tableLoading = true
      let { getTableData } = this.$api.studentInfoManageAjax
      getTableData({
        filter: this.searchObj,
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(res => {
          let { data } = res
          this.tableData = data.records
          this.pagination.current = data.pageNum
          this.pagination.pageSize = data.pageSize
          this.pagination.total = data.total
        })
        .catch(err => {})
        .finally(() => {
          this.tableLoading = false
        })
    },
    submitFnoFNewBuildEdit(data) {
      //弹出框新建、编辑，确认按钮触发事件
      this.modalFormObj = newBuildEditMapping(data)
      this.modalFormObj.homeAddress = this.addressDetail
      console.log(this.modalFormObj)
      if (this.rowsObj.id) {
        this.modalFormObj.id = this.rowsObj.id
      }
      if (this.addressArr.length > 0) {
        this.modalFormObj.provinceCode = this.addressArr[0]
        this.modalFormObj.provinceName = this.selectedOptions[0].label
        this.modalFormObj.cityCode = this.addressArr[1]
        this.modalFormObj.cityName = this.selectedOptions[1].label
        this.modalFormObj.regionCode = this.addressArr[2]
        this.modalFormObj.regionName = this.selectedOptions[2].label
      } else {
        // this.modalFormObj.provinceCode = this.rowsObj.provinceCode
        // this.modalFormObj.provinceName = this.rowsObj.provinceName
        // this.modalFormObj.cityCode = this.rowsObj.cityCode
        // this.modalFormObj.cityName = this.rowsObj.cityName
        // this.modalFormObj.regionCode = this.rowsObj.regionCode
        // this.modalFormObj.regionName = this.rowsObj.regionName
      }

      // this.okButtonPropsDisabled = true
      this.spinning = true
      console.log('this.modalFormObj', this.modalFormObj)
      let { saveStudentsUrl } = this.$api.studentInfoManageAjax
      saveStudentsUrl(this.modalFormObj)
        .then(res => {
          this.studentInfoVisible = false
          this.$message.success(res.resultMsg || '操作成功')
        })
        .catch(err => {
          // this.$message.warning(err.resultMsg || '操作成功')
        })
        .finally(fin => {
          // this.rowsObj = {}
          this.spinning = false
          this.getTableData()
        })
    },
    submitOfRegister(data) {
      //弹出框
      this.modalRegisterFormObj = newBuildRegisterMapping(data)
      this.modalRegisterFormObj.homeAddress = this.addressDetail
      console.log(this.modalRegisterFormObj)
      console.log('studentRegisterRowsObj', this.studentRegisterRowsObj)
      if (this.studentRegisterRowsObj.id) {
        this.modalRegisterFormObj.id = this.studentRegisterRowsObj.id
      }
      if (this.addressArr.length > 0) {
        this.modalRegisterFormObj.provinceCode = this.addressArr[0]
        this.modalRegisterFormObj.provinceName = this.selectedOptions[0].label
        this.modalRegisterFormObj.cityCode = this.addressArr[1]
        this.modalRegisterFormObj.cityName = this.selectedOptions[1].label
        this.modalRegisterFormObj.regionCode = this.addressArr[2]
        this.modalRegisterFormObj.regionName = this.selectedOptions[2].label
      }

      // this.okButtonPropsDisabled = true
      this.spinning = true
      console.log('this.modalRegisterFormObj', this.modalRegisterFormObj)
      let { registerStudentsUrl } = this.$api.studentInfoManageAjax
      registerStudentsUrl(this.modalRegisterFormObj)
        .then(res => {
          this.studentRegisterVisible = false
          this.$message.success(res.resultMsg || '操作成功')
        })
        .catch(err => {
          // this.$message.warning(err.resultMsg || '操作成功')
        })
        .finally(fin => {
          // this.rowsObj = {}
          this.spinning = false
          this.getTableData()
        })
    },
    newBuild() {
      //新建按钮触发事件
      // this.okButtonPropsDisabled = false
      this.rowsObj = {}
      this.defaultValueOptions = []
      this.spinning = false
      this.addressStr = ''
      this.addressDetail = ''
      this.isShowCascader = true
      this.studentInfoVisible = true
      this.$nextTick(() => {
        this.$refs.modalForm.reset()
      })
    },
    exportFile() {
      // 导出按钮触发事件
      let { exportDataAjax } = this.$api.studentInfoManageAjax
      exportDataAjax({
        filter: this.searchObj,
        exporting: true
      })
        .then(res => {
          if (res.type === 'application/vnd.ms-excel') {
            let objectUrl = URL.createObjectURL(res)
            this.$message.success('导出成功')
            downloadFileUseALabel('学生基础信息.xlsx', objectUrl)
          } else {
            this.$message.warning('导出失败...')
          }
        })
        .catch(err => {})
    },
    editRow(rows) {
      this.rowsObj = rows
      let { editStudentsAjax } = this.$api.studentInfoManageAjax
      editStudentsAjax(rows.id).then(res => {
        console.log(res)
        // 编辑按钮触发事件
        // this.okButtonPropsDisabled = false
        this.spinning = false
        this.isShowCascader = false
        this.studentInfoVisible = true
        this.$nextTick(() => {
          this.$refs.modalForm.fApi.setValue({
            studentName: res.data.studentName,
            studentID: res.data.studentNo,
            grade: res.data.studentClass, //1
            nation: res.data.ethnicGroup,
            identityNumber: res.data.idNumber,
            sex: res.data.gender === null ? '' : res.data.gender + '',
            birthdayTime: res.data.birthday,
            nationality: res.data.nationality,
            connectName: res.data.contactName,
            relation: res.data.relation,
            phone: res.data.contactPhoneNumber,
            fatherName: res.data.fatherName,
            fatherPhone: res.data.fatherPhoneNumber,
            motherName: res.data.motherName,
            motherPhone: res.data.motherPhoneNumber,
            enterTime: res.data.admissionDateStr === null ? '' : res.data.admissionDateStr,
            businessStatus: res.data.businessUnit === null ? '' : res.data.businessUnit + ''
          })
          this.defaultValueOptions = [res.data.provinceCode, res.data.cityCode, res.data.regionCode]
          console.log(this.defaultValueOptions)
          this.addressDetail = res.data.homeAddress
          // this.$refs.modalForm.fApi.disabled(true, 'studentID')
        })
      })
    },

    // 在园
    registerRow(rows) {
      if (rows.registerStatus != 0 || rows.completePayment == false) {
        this.$message.warning('请先完成预报名流程！')
        return
      }
      this.studentRegisterRowsObj = rows
      let { editStudentsAjax } = this.$api.studentInfoManageAjax
      editStudentsAjax(rows.id).then(res => {
        console.log(res)
        // 编辑按钮触发事件
        // this.okButtonPropsDisabled = false
        this.spinning = false
        this.isShowCascader = false
        this.studentRegisterVisible = true
        this.$nextTick(() => {
          this.$refs.modalRegisterForm.fApi.setValue({
            studentName: res.data.studentName,
            studentID: res.data.studentNo,
            grade: res.data.studentClass, //1
            nation: res.data.ethnicGroup,
            identityNumber: res.data.idNumber,
            sex: res.data.gender === null ? '' : res.data.gender + '',
            birthdayTime: res.data.birthday,
            nationality: res.data.nationality,
            connectName: res.data.contactName,
            relation: res.data.relation,
            phone: res.data.contactPhoneNumber,
            fatherName: res.data.fatherName,
            fatherPhone: res.data.fatherPhoneNumber,
            motherName: res.data.motherName,
            motherPhone: res.data.motherPhoneNumber,
            enterTimeOfRegister: res.data.admissionDateStr === null ? '' : res.data.admissionDateStr,
            businessStatus: res.data.businessUnit === null ? '' : res.data.businessUnit + ''
          })
          this.defaultValueOptions = [res.data.provinceCode, res.data.cityCode, res.data.regionCode]
          console.log(this.defaultValueOptions)
          this.addressDetail = res.data.homeAddress
          // this.$refs.modalRegisterForm.fApi.disabled(true, 'studentID')
        })
      })
    },
    // 退园
    exitRow(rows) {
      let { exitAjax } = this.$api.studentInfoManageAjax
      exitAjax(rows.id)
        .then(
          res => {
            this.$message.success('退园成功')
          },
          rej => {
            this.$message.warning('退园失败')
          }
        )
        .catch(err => {})
        .finally(fin => {
          this.getTableData()
        })
    },
    viewHistory(rows) {
      this.operateVisible = true
      let { getTableData } = this.$api.actionListAjax
      getTableData({
        module: 'STUDENT',
        businessId: rows.id
      })
        .then(res => {
          this.historyTableData = res.data
        })
        .catch(err => {})
        .finally(fin => {})
    },
    submitFn(data) {
      console.log('submitFn data', data)
      // 查询按钮触发事件
      this.searchObj = searchMapping(data)
      this.pagination.current = 1
      this.getTableData()
    },
    handleOkOfStudentInfo() {
      // 弹出框提交按钮触发事件
      // console.log(this.addressDetail, this.defaultValueOptions)
      // let bool = this.defaultValueOptions.every(item => {
      //   return typeof item === 'string' && item.length > 0
      // })
      // if (
      //   this.addressDetail &&
      //   this.addressDetail.length > 0 &&
      //   this.defaultValueOptions &&
      //   this.defaultValueOptions.length > 0 &&
      //   bool
      // ) {
      this.$refs.modalForm.submitFn()
      // } else {
      //   this.$message.warning('您有未填信息，请填入！')
      // }
    },
    handleOkOfStudentRegister() {
      this.$refs.modalRegisterForm.submitFn()
      console.log('handleOkOfStudentRegister')
    },
    handleCancelOfStudentRegister() {
      // 弹出框取消按钮触发事件
      this.studentRegisterRowsObj = {}
      this.studentRegisterVisible = false
    },
    handleCancelOfStudentInfo() {
      // 弹出框取消按钮触发事件
      this.rowsObj = {}
      this.studentInfoVisible = false
    },
    onShowSizeChange(current, pageSize) {
      this.pagination.current = 1
      this.pagination.pageSize = pageSize
      this.getTableData()
    },
    onPaginationChange(pageNo) {
      this.pagination.current = pageNo
      this.getTableData()
    }
  }
}
</script>
<style lang="less" scoped>
.student-info-manage {
}
</style>
