@top: 19px;
@header_height: 64px;
@tab_height: 42px;
@main_margin: 24px;

.attendance-record {
  position: absolute;
  top: @top;
  left: -@main_margin;
  width: calc(100% + @main_margin * 2);
  min-height: calc(100vh - @header_height - @tab_height);
  background-color: #e2f1ff;
  padding: 34px;
  .bg {
    position: absolute;
    bottom: 0;
    right: 0;
    max-width: calc(50% - 15px);
    // max-height: 300px;
  }
  .text-wrapper {
    position: relative;
    border-radius: 4px;
    margin-bottom: 36px;
    cursor: pointer;
    .class-name {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
    &-bg {
      width: 100%;
    }
  }
}