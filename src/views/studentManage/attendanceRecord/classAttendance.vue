<template>
  <div class="attendance-record">
    <!-- <img class="bg" src="../images/bg1.png" /> -->
    <div class="content-wrapper">
      <a-tabs default-active-key="amAttendance" @change="tabChange">
        <a-tab-pane key="amAttendance" tab="上午"></a-tab-pane>
        <a-tab-pane key="noonAttendance" tab="中午"></a-tab-pane>
        <a-tab-pane key="pmAttendance" tab="下午"></a-tab-pane>
      </a-tabs>
      <div v-if="unusualAttendanceMessage" class="notFillTips">
        <a-icon type="info-circle" theme="twoTone" />{{ unusualAttendanceMessage }}
      </div>
      <a-row :gutter="30">
        <a-spin :spinning="submitLoading">
          <a-col :span="10" class="left-section">
            <div class="attendance-date custom-card">
              <a-space class="m-b-20px">
                <a-button type="primary" @click="allChangeStatusHandler(1)">全部出勤</a-button>
                <a-button @click="allChangeStatusHandler(0)">全部缺勤</a-button>
              </a-space>
              <label class="attendance-date-label">
                <a-row type="flex" align="middle">
                  <a-col flex="100px" class="text-center">考勤日期：</a-col>
                  <a-col flex="auto">
                    <a-date-picker
                      id="data-picker"
                      value-format="YYYY-MM-DD"
                      :value="date"
                      @change="dateChangeHandler"
                      :disabled-date="disabledDate"
                    />
                  </a-col>
                </a-row>
              </label>
              <!-- <div class="button-actions">
              <a-button :type="selectedTime === 'amAttendance' ? 'primary' : ''" @click="switchTime('amAttendance')"
                >上午</a-button
              >
              <a-button :type="selectedTime === 'noonAttendance' ? 'primary' : ''" @click="switchTime('noonAttendance')"
                >中午</a-button
              >
              <a-button :type="selectedTime === 'pmAttendance' ? 'primary' : ''" @click="switchTime('pmAttendance')"
                >下午</a-button
              >
            </div> -->
            </div>
            <div class="students-wrapper">
              <a-spin tip="Loading..." :spinning="loading">
                <div
                  class="student-item"
                  :class="{
                    'long-term-leave': student.longTerm,
                    leave: student[selectedTime] === 1 ? false : true,
                    active: student.isSelected === 1 ? true : false
                  }"
                  @click="selectStudent(student)"
                  v-for="student in students"
                  :key="student.no"
                >
                  <a-row type="flex" align="middle">
                    <a-col flex="50px">
                      <img v-if="student.gender === 1" class="photo" src="../images/boy.png" alt="studen photo" />
                      <img v-else class="photo" src="../images/girl.png" alt="studen photo" />
                    </a-col>
                    <a-col flex="60px">{{ student.studentName }}</a-col>
                    <a-col flex="140px">{{ student.studentNo }}</a-col>
                    <a-col>{{ student.genderDesc }}</a-col>
                  </a-row>
                  <div class="long-term-icon">长期病假</div>
                </div>
              </a-spin>
            </div>
          </a-col>
          <a-col :span="14" class="right-section">
            <div class="attendance-action custom-card text-center">
              <div
                v-if="selectedStudentLeaveStatus === 1 || selectedStudentLeaveStatus === ''"
                class="image-item"
                @click="changeStatusHandler(0)"
              >
                <img src="../images/absence.png" alt="absence icon" />
                <span>缺勤</span>
              </div>
              <div
                v-if="selectedStudentLeaveStatus === 0 || selectedStudentLeaveStatus === ''"
                class="image-item"
                @click="changeStatusHandler(1)"
              >
                <img src="../images/attendance.png" alt="attendance icon" />
                <span>取消缺勤</span>
              </div>
            </div>
            <div class="note">
              <a-textarea
                class="textarea-el"
                v-model="note"
                placeholder="请输入备注信息"
                :auto-size="{ minRows: 3, maxRows: 5 }"
                :disabled="selectedStudents.length !== 1"
                :max-length="100"
                @change="remarkChangeHandler"
              />
              <div class="tips">注：备注信息统一在此处填写</div>
            </div>
            <div class="actions text-right">
              <a-button @click="$router.back()">返回</a-button>
              <a-button type="primary" @click="submitHandler">提交</a-button>
            </div>
          </a-col>
        </a-spin>
      </a-row>
    </div>
  </div>
</template>
<script>
import { fetchStudents, saveLeaveStatus } from '@/request/api/attendanceRecord'
import moment from 'moment'
const selectedTimeArr = ['amAttendance', 'noonAttendance', 'pmAttendance']
export default {
  data() {
    return {
      // 上午 amAttendance， 中午 noonAttendance，下午 pmAttendance
      selectedTime: 'amAttendance',
      note: '',
      students: [],
      date: '',
      selectedStudents: [],
      selectedStudentLeaveStatus: '',
      loading: false,
      submitLoading: false,
      currentMonth: '',
      unusualAttendanceMessage: '测试' // 提示文本
    }
  },
  mounted() {
    this.getStudentList()
  },
  watch: {
    selectedStudents(newVal) {
      if (newVal.length === 1) {
        this.note = newVal[0].remark
      } else {
        this.note = ''
      }
      this.selectedStudentLeaveStatus = newVal[0] ? newVal[0][this.selectedTime] : ''
    },
    students() {
      this.collectSelectedStudents()
    },
    currentMonth(newVal) {}
  },
  methods: {
    disabledDate(currentDate) {
      if (this.currentMonthStr) return false
      const _end = moment(this.currentMonthStr).endOf('month')
      const _start = moment(this.currentMonthStr).startOf('month')

      return currentDate > _end || currentDate < _start
    },
    remarkChangeHandler() {
      console.log(this.note)
      const index = this.students.findIndex(student => student.studentNo === this.selectedStudents[0].studentNo)

      this.students.splice(index, 1, { ...this.students[index], remark: this.note })
      // this.selectedStudents = [{ ...this.students[index], remark: this.note }]
    },
    submitHandler() {
      const _data = []
      let _this = this
      this.students.forEach(student => {
        let _s = { ...student }
        // 非选中时段的值设为null
        selectedTimeArr.forEach(item => {
          if (item != _this.selectedTime) {
            _s[item] = null
          }
        })
        delete _s.isSelected
        _data.push(_s)
      })
      this.submitLoading = true
      saveLeaveStatus(_data)
        .then(res => {
          this.submitLoading = false
          if (res.resultCode === 'SUCCESS') {
            this.$message.success('提交成功')
            this.$router.back()
          } else {
            this.$message.error(res.resultMsg || '提交失败，请重试')
          }
        })
        .catch(() => {
          this.submitLoading = false
          this.$message.error('提交失败，请重试')
        })
    },
    changeStatusHandler(status) {
      if (this.selectedStudents.length > 0) {
        // let newSelectedStudents = []

        this.selectedStudents.forEach(student => {
          const _s = { ...student }

          _s[this.selectedTime] = status
          this.changeStudentLeaveStatus(student, status)
          // newSelectedStudents.push(_s)
        })
        // this.selectedStudents = newSelectedStudents
        this.clearSelected()
      } else {
        this.$message.error('请选择学生')
      }
    },
    // 批量操作（全部缺勤/全部出勤）状态： 1：出勤 0：缺勤
    allChangeStatusHandler(status) {
      let newSudents = this._.cloneDeep(this.students)
      newSudents.forEach(student => {
        student[this.selectedTime] = status
      })
      this.students = newSudents
      // this.submitHandler()
    },
    // 清除所有学生选中状态
    clearSelected() {
      this.students.forEach(student => {
        student.isSelected = 0
      })
      this.selectedStudents = []
    },
    changeStudentLeaveStatus(student, status) {
      const index = this.students.findIndex(_s => _s.studentNo === student.studentNo)
      const selectedIndex = this.selectedStudents.findIndex(_s => _s.studentNo === student.studentNo)
      const _student = { ...this.students[index] }

      _student[this.selectedTime] = status
      this.students.splice(index, 1, { ..._student })
      // this.selectedStudents.splice(selectedIndex, 1, { ..._student })
    },
    dateChangeHandler(val) {
      console.log('val', val)
      this.date = val
      this.getStudentList()
    },
    selectStudent(student) {
      const isSelectedIndex = this.students.findIndex(_s => _s.studentNo === student.studentNo)

      if (
        student.isSelected !== 1 &&
        this.selectedStudentLeaveStatus !== '' &&
        student[this.selectedTime] !== this.selectedStudentLeaveStatus
      ) {
        this.$message.warn('只能选择相同请假状态的学生')
        return
      }

      this.students.splice(isSelectedIndex, 1, { ...student, isSelected: student.isSelected === 1 ? 0 : 1 })
      // this.collectSelectedStudents()
    },
    collectSelectedStudents() {
      this.selectedStudents = this.students.filter(student => student.isSelected === 1)
    },
    // switchTime(time) {
    //   console.log(time)
    //   this.selectedTime = time
    //   this.students.forEach(student => {
    //     student.isSelected = 0
    //   })
    //   this.selectedStudents = []
    // },
    tabChange(key) {
      console.log(key)
      this.selectedTime = key
      this.clearSelected()
    },
    getStudentList() {
      this.loading = true
      fetchStudents(this.$route.query.name, this.date)
        .then(res => {
          this.loading = false
          if (res.resultCode === 'SUCCESS') {
            this.students = res.data
            this.unusualAttendanceMessage = res.data[0]?.unusualAttendanceMessage || ''
            this.date = res.data[0]?.currentDate
            this.currentMonth = res.data[0]?.currentMonthStr || ''
          } else {
            this.$message.error('获取学生列表失败，请刷新重试')
          }
        })
        .catch(() => {
          this.loading = false
          this.$message.error('获取学生列表失败，请刷新重试')
        })
    }
  }
}
</script>

<style lang="less" scoped>
@import '../css/bg.less';
.content-wrapper {
  background-color: #fff;
  border-radius: 5px;
  .left-section {
    border-right: 1px solid #e8e8e8;
  }
  /deep/ .ant-tabs-bar {
    margin-bottom: 0;
  }
  .notFillTips {
    background-color: #e6f7ff;
    padding: 10px 15px;
    box-sizing: border-box;
    width: calc(10 / 24 * (100% - 5px));
    i {
      margin-right: 10px;
    }
  }
  .m-b-20px {
    margin-bottom: 20px;
  }
  /deep/ .textarea-el {
    border: 1px solid #d9d9d9;
  }
}

.students-wrapper {
  overflow-y: auto;
  height: calc(100vh - 340px);
  padding-left: 28px;
  margin-bottom: 26px;
  .student-item {
    position: relative;
    padding: 14px 37px;
    cursor: pointer;
    overflow: hidden;
    // transition: border 0.2s ease, box-shandow 0.2s ease;
    transition: 0.1s ease;
    border-color: #dfdfdf;
    border-style: solid;
    border-width: 1px 1px 0 1px;
    &:last-child {
      border-bottom: 1px solid #d4cfcf;
    }
    .long-term-icon {
      display: none;
      position: absolute;
      right: -31px;
      top: 13px;
      background: #ef755f;
      color: #fff;
      padding: 2px 30px;
      font-size: 12px;
      transform: rotate(45deg);
    }

    &:hover {
      position: relative;
      z-index: 1;
      background: #ffe6e6;
      box-shadow: 0px 2px 12px 0px rgba(101, 109, 117, 0.16), 0px 4px 6px 2px #c87377,
        inset 0px 12px 22px 0px rgba(255, 255, 255, 0.5), inset 0px -12px 12px 0px #ffd3d3,
        inset 0px -2px 2px 0px rgba(255, 255, 255, 0.69);
      border: 1px solid rgba(151, 151, 151, 0.3);
    }
    &.active {
      position: relative;
      z-index: 1;
      background: #ffffff;
      box-shadow: 0px 2px 12px 0px rgba(101, 109, 117, 0.16), 0px 4px 6px 2px #dcdcdc,
        inset 0px 12px 22px 0px rgba(24, 144, 255, 0.2), inset 0px -8px 12px 0px rgba(24, 144, 255, 0.2);
      border: 1px solid rgba(151, 151, 151, 0.3);
    }
    &.leave {
      background: #ffe6e6;
    }
    &.long-term-leave {
      // background: #ffe6e6;
      .long-term-icon {
        display: block;
      }
    }
    &.leave:hover,
    &.leave.active
    // &.long-term-leave:hover,
    // &.long-term-leave.active
    {
      background-image: linear-gradient(#fff1f1, #ffdddd);
      box-shadow: 0px 2px 15px 0 #c5aaaa;
    }
  }
  .photo {
    width: 34px;
  }
}

.button-actions {
  display: flex;
  max-width: 260px;
  justify-content: space-around;
  margin: 18px auto 0;
  /deep/ .ant-btn {
    border-color: transparent;
  }
}
.custom-card {
  border-radius: 5px;
  padding: 26px 28px;
}
.attendance-action {
  display: flex;
  justify-content: center;
  padding-top: 5px;
  padding-bottom: 5px;
  .image-item {
    position: relative;
    color: #fff;
    span {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      cursor: pointer;
    }
    img {
      cursor: pointer;
      width: 122px;
    }
  }
}
.right-section {
  padding-right: 30px !important;
}
.note {
  margin-top: 30px;
  /deep/ textarea {
    border-radius: 5px;
    border-color: #fff;
  }
  .tips {
    margin-top: 10px;
  }
}
.actions {
  margin-top: 20px;
  > * {
    margin-left: 10px;
  }
}

@media screen and (max-width: 767px) {
  .attendance-record {
    padding: 10px;
  }
}
</style>
