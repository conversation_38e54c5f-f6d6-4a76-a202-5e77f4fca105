<template>
  <div class="attendance-record">
    <img class="bg" src="../images/bg.png" />
    <a-row :gutter="{ xxl: 36, xl: 36, lg: 36, md: 20, sm: 10, xs: 10 }" class="class-warpper">
      <a-col
        :xs="{ span: 12 }"
        :sm="{ span: 12 }"
        :md="{ span: 12 }"
        :lg="{ span: 8 }"
        :xl="{ span: 8 }"
        :xxl="{ span: 8 }"
        class="text-wrapper"
        v-for="_class in classes"
        :key="_class.className"
        @click="clickHandler(_class)"
      >
        <span class="finish" v-if="_class.status === 1">已完成</span>
        <span class="class-name">{{ _class.className }}</span>
        <img class="text-wrapper-bg" src="../images/text-bg.png" alt="class name background" />
      </a-col>
    </a-row>
  </div>
</template>
<script>
import { fetchClasses } from '@/request/api/attendanceRecord'

export default {
  data() {
    return {
      classes: [],
      bool: false,
      message: '备注信息统一在此处填写'
    }
  },
  mounted() {
    fetchClasses()
      .then(res => {
        if (res.resultCode === 'SUCCESS') {
          this.classes = res.data
        } else {
          this.$message.error('获取班级列表失败，请刷新重试')
        }
      })
      .catch(() => {
        this.$message.error('获取班级列表失败，请刷新重试')
      })
  },
  methods: {
    clickHandler(obj) {
      this.$router.push({
        name: 'class-attendance',
        query: {
          name: obj.className
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@top: 19px;
@header_height: 64px;
@tab_height: 42px;
@main_margin: 24px;

.attendance-record {
  position: absolute;
  top: @top;
  left: -@main_margin;
  width: calc(100% + @main_margin * 2);
  min-height: calc(100vh - @header_height - @tab_height);
  background-color: #e2f1ff;
  padding: 34px;
  .bg {
    position: absolute;
    bottom: 0;
    right: 0;
    max-width: 58%;
    max-height: 300px;
  }
  .text-wrapper {
    padding-right: 0 !important;
    overflow: hidden;
    position: relative;
    border-radius: 4px;
    margin-bottom: 36px;
    cursor: pointer;
    .finish {
      color: #fff;
      line-height: 26px;
      text-align: center;
      width: 50%;
      height: 26px;
      position: absolute;
      top: 10%;
      right: -16%;
      background-color: #ef755f;
      transform: rotate(42deg);
    }
    .class-name {
      position: absolute;
      left: 55%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
    &-bg {
      width: 100%;
    }
  }
  .active {
  }
}
@media screen and (max-width: 767px) {
  .attendance-record {
    padding: 10px;
  }
}
</style>
