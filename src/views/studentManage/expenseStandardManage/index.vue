<template>
  <div class="expense-standard-manage">
    <Form ref="searchForm" @submitFn="submitFn" />
    <a-card style="margin-top: 12px">
      <a-table
        :scroll="{ x: 2600 }"
        :loading="tableLoading"
        :row-key="record => record.key"
        :pagination="false"
        :columns="columns"
        :data-source="tableData"
        bordered
      >
        <template slot="operation" slot-scope="text, record">
          <div class="btn-group">
            <a-button v-permission="['editexpenses']" type="link" @click="editRow(record)"> 编辑 </a-button>
            <!-- <a-button type="link" @click="editRow(record)"> 编辑 </a-button> -->
          </div>
        </template>
        <template slot="footer">
          <!-- 分页指示器 -->
          <a-pagination
            style="margin-top: 10px; text-align: right"
            :page-size.sync="pagination.pageSize"
            :current="pagination.current"
            show-size-changer
            @showSizeChange="onShowSizeChange"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total"
            :show-total="total => `共 ${total} 条`"
            @change="onPaginationChange"
            show-quick-jumper
          >
            <template slot="buildOptionText" slot-scope="props">
              <span v-if="props.value !== '50'">{{ props.value }}条/页</span>
            </template>
          </a-pagination>
        </template>
      </a-table>
    </a-card>
    <!--修改保育费标准 -->
    <a-modal
      :width="680"
      :closable="false"
      destroy-on-close
      :mask-closable="false"
      v-model="editFeeVisible"
      title="修改保育费标准"
      :footer="null"
    >
      <a-spin :spinning="spinning">
        <a-form-model ref="ruleForm" :model="form" :rules="rules">
          <a-form-model-item :label-col="labelCol" :wrapper-col="wrapperCol" label="学生姓名">
            <a-input v-model="form.studentName" disabled />
          </a-form-model-item>
          <a-form-model-item :label-col="labelCol" :wrapper-col="wrapperCol" label="学生学号">
            <a-input v-model="form.studentId" disabled />
          </a-form-model-item>
          <a-form-model-item :label-col="labelCol" :wrapper-col="wrapperCol" label="保育费标准类型">
            <a-radio-group v-model="form.expenseStandardType" @change="handleFeeTypeChange">
              <a-radio v-for="item in expenseStandardTypeEnum" :value="item.key" :key="item.key">{{
                item.label
              }}</a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item :label-col="labelCol" :wrapper-col="wrapperCol" label="保育费标准" prop="expenseStandard">
            <a-input-number
              v-model="form.expenseStandard"
              :disabled="form.expenseStandardType === expenseStandardTypeEnum[0].key"
            />
          </a-form-model-item>
          <a-form-model-item prop="remark" :label-col="labelCol" :wrapper-col="wrapperCol" label="原因">
            <a-textarea autosize style="width: 100%" allow-clear v-model="form.remark" />
          </a-form-model-item>
          <a-form-model-item>
            <div style="text-align: right">
              <a-button style="margin-right: 8px" @click="handleCancel">取消</a-button>
              <a-button type="primary" @click="onSubmit">确定</a-button>
            </div>
          </a-form-model-item>
        </a-form-model>
      </a-spin>
    </a-modal>
  </div>
</template>
<script>
import Form from './component/search.vue'
import { searchMapping } from './component/searchMapping.js'
import { columns } from './tableColumns.js'
const BYFEE = 3300
export default {
  data() {
    return {
      searchObj: {},
      tableLoading: false,
      columns: columns,
      tableData: [],
      pagination: {
        pageSizeOptions: ['10', '20', '40', '80', '100'],
        pageSize: 10,
        current: 1,
        total: 0
      },
      rowsObj: {},
      editFeeVisible: false,
      expenseStandardTypeEnum: [
        { key: 0, label: '默认值' },
        { key: 1, label: '自定义' }
      ],
      spinning: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      form: {
        expenseStandardType: 0,
        expenseStandard: BYFEE,
        studentName: '',
        studentId: '',
        remark: ''
      },
      rules: {
        expenseStandard: [
          { required: true, message: '请输入金额' },
          { pattern: /^([1-9]\d*|0)(\.\d{1,2})?$/, message: '请输入正确的金额格式' }
        ],
        remark: [
          { type: 'string', message: '输入类型必须为字符串', trigger: 'change' },
          { required: true, message: '请输入原因', trigger: 'change' },
          {
            type: 'string',
            max: 100,
            message: '输入原因长度应该在0-100之间!',
            trigger: 'change'
          }
        ]
      }
    }
  },
  components: {
    Form
  },
  mounted() {
    this.$refs.searchForm.submitFn()
  },
  methods: {
    onSubmit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          let param = {
            id: this.rowsObj.id,
            expenseStandard: this.form.expenseStandard,
            expenseStandardType: this.form.expenseStandardType,
            remark: this.form.remark
          }
          this.spinning = true
          let { editExpenseStandard } = this.$api.expenseStandardManageAjax
          editExpenseStandard(param)
            .then(res => {
              this.editFeeVisible = false
              this.$message.success(res.resultMsg || '操作成功')
            })
            .catch(err => {})
            .finally(fin => {
              this.spinning = false
              this.getTableData()
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleCancel() {
      this.editFeeVisible = false
      this.$refs.ruleForm.resetFields()
    },
    handleFeeTypeChange(value) {
      if (value.target.value === 0) {
        this.form.expenseStandard = BYFEE
      } else {
        this.form.expenseStandard = ''
      }
    },

    submitFn(data) {
      // 查询按钮触发事件
      this.searchObj = searchMapping(data)
      this.pagination.current = 1
      this.getTableData()
    },
    getTableData() {
      //获取表格数据
      this.tableLoading = true
      let { getTableData } = this.$api.expenseStandardManageAjax
      getTableData({
        filter: this.searchObj,
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(res => {
          let { data } = res
          this.tableData = data.records
          this.pagination.current = data.pageNum
          this.pagination.pageSize = data.pageSize
          this.pagination.total = data.total
        })
        .catch(err => {})
        .finally(() => {
          this.tableLoading = false
        })
    },
    onShowSizeChange(current, pageSize) {
      this.pagination.current = 1
      this.pagination.pageSize = pageSize
      this.getTableData()
    },
    onPaginationChange(pageNo) {
      this.pagination.current = pageNo
      this.getTableData()
    },
    editRow(rows) {
      this.rowsObj = rows
      let { getExpenseStandardDetailAjax } = this.$api.expenseStandardManageAjax
      console.log(rows.id)
      getExpenseStandardDetailAjax(rows.id).then(res => {
        console.log(res)
        // 编辑按钮触发事件
        // this.okButtonPropsDisabled = false
        this.spinning = false
        this.editFeeVisible = true
        this.form.studentName = res.data.studentName
        this.form.studentId = res.data.studentNo
        this.form.remark = res.data.remark
        this.form.expenseStandardType = res.data.expenseStandardType
        this.form.expenseStandard = res.data.expenseStandard || BYFEE
      })
    },

    disabledOk(bool) {
      this.okButtonPropsDisabled = bool
    }
  }
}
</script>
<style lang="less" scoped>
.expense-standard-manage {
}
</style>
