export const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
    width: 80,
    customRender: (text, record, index) => {
      return `${index + 1}`
    }
  },
  {
    title: '学生姓名',
    dataIndex: 'studentName',
    align: 'center'
  },
  {
    title: '学号',
    dataIndex: 'studentNo',
    align: 'center'
  },
  {
    title: '身份证号',
    dataIndex: 'idNumber',
    align: 'center'
  },
  {
    title: '性别',
    dataIndex: 'genderStr',
    align: 'center'
  },
  {
    title: '入园时间',
    dataIndex: 'admissionDateStr',
    align: 'center'
  },
  {
    title: '业态',
    dataIndex: 'businessUnitStr',
    align: 'center'
  },
  {
    title: '保育费标准类型',
    dataIndex: 'expenseStandardTypeStr',
    align: 'center'
  },
  {
    title: '保育费标准',
    dataIndex: 'expenseStandardValue',
    align: 'center'
  },
  {
    title: '原因',
    dataIndex: 'remark',
    align: 'center'
  },
  {
    title: '操作',
    align: 'center',
    dataIndex: 'operation',
    width: 140,
    fixed: 'right',
    scopedSlots: { customRender: 'operation' }
  }
]
