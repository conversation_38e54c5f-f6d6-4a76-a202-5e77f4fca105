export const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
    width: 80,
    customRender: (text, record, index) => {
      return `${index + 1}`
    }
  },
  {
    title: '学生姓名',
    dataIndex: 'studentName',
    align: 'center'
  },
  {
    title: '学号',
    dataIndex: 'studentNo',
    align: 'center'
  },
  {
    title: '费用类型',
    dataIndex: 'costTypeDesc',
    align: 'center'
  },
  {
    title: '发生月份',
    dataIndex: 'derateMonth',
    align: 'center'
  },
  {
    title: '减免金额',
    dataIndex: 'derateAmount',
    align: 'center'
  },
  {
    title: '原因',
    dataIndex: 'remark',
    align: 'center'
  },
  {
    width: 80,
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    scopedSlots: { customRender: 'operation' }
  }
]
