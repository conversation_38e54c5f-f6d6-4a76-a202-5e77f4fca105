<template>
  <!--
    :closable="false" 不显示右上角关闭按钮
    :keyboard="false" 不支持键盘esc关闭
    destroy-on-close  关闭时销毁Modal里的元素
    :mask-closable="false" 点击蒙层不允许关闭
    :confirm-loading="confirmLoading" 确定按钮loading
    :cancel-button-props="{ props: { disabled: confirmLoading } }" 取消按钮禁用
  -->
  <a-modal
    width="460px"
    :closable="false"
    :keyboard="false"
    destroy-on-close
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    :cancel-button-props="{ props: { disabled: confirmLoading } }"
    :title="modalObj.title"
    v-model="modalObj.visible"
    @ok="modalHandleOk"
    @cancel="modalHandleOkCancel"
  >
    <a-form-model :model="form" :rules="rules" ref="form">
      <a-form-model-item prop="name" label="学生姓名" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- <a-input v-model="form.name" allow-clear /> -->
        <a-select
          allow-clear
          show-search
          :value="form.name"
          placeholder="选择学生"
          style="width: 100%"
          :filter-option="false"
          :not-found-content="selectOptionLoading ? undefined : null"
          @search="getSelectOptionsData"
          @change="handleChange"
        >
          <a-spin v-if="selectOptionLoading" slot="notFoundContent" size="small" />
          <a-select-option v-for="item in selectOptionsArr" :key="item.studentId" :item="item" :value="item.studentId">
            {{ item.studentName }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="学号" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- <a-input v-model="form.id" allow-clear /> -->
        {{ form.id }}
      </a-form-model-item>
      <a-form-model-item prop="date" label="减免月份" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-month-picker
          :disabled-date="disabledDate"
          placeholder="请选择减免月份"
          allow-clear
          v-model="form.date"
          format="YYYY-MM"
          value-format="YYYY-MM"
        />
      </a-form-model-item>
      <a-form-model-item prop="amount" label="减免金额" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-input-number :formatter="formatterAmount" style="width: 100%" allow-clear v-model="form.amount" />
      </a-form-model-item>
      <a-form-model-item prop="remark" label="原因" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-textarea autosize style="width: 100%" allow-clear v-model="form.remark" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import moment from 'moment'
export default {
  props: {
    modalObj: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      msg: '这里是减免管理新建按钮弹出框组件',
      selectOptionsArr: [],
      selectOptionLoading: false,
      confirmLoading: false,
      form: {
        name: '',
        id: '',
        date: '',
        amount: null,
        limitAmount: null,
        remark: ''
      },
      rules: {
        name: [
          { type: 'number', message: '输入类型必须为数字', trigger: 'change' },
          { required: true, message: '请选择学生姓名', trigger: 'change' }
        ],
        id: [{ required: true, message: '请输入学号', trigger: 'blur' }],
        date: [{ required: true, message: '请选择减免月份', trigger: 'change' }],
        amount: [
          {
            type: 'number',
            message: '输入选择值的类型为数字!',
            trigger: 'change'
          },
          { required: true, message: '请输入减免金额', trigger: 'change' },
          {
            type: 'number',
            min: -99999,
            max: 3000,
            message: '输入选择值的数字应该在-99999至3000之间',
            trigger: 'change'
          },
          { pattern: /^(-?[0-9]{1,13})(\.[0-9]{1,2})?$/, message: '小数之前13位,小数点后保留两位', trigger: 'change' }
        ],
        remark: [
          { type: 'string', message: '输入类型必须为字符串', trigger: 'change' },
          { required: true, message: '请输入原因', trigger: 'change' },
          {
            type: 'string',
            max: 100,
            message: '输入原因长度应该在0-100之间!',
            trigger: 'change'
          }
        ]
      }
    }
  },
  watch: {
    'form.limitAmount': {
      handler(val) {
        this.rules.amount[2].max = val
        this.rules.amount[2].message = `输入选择值的数字应该在-99999至${val}之间`
      }
    }
  },
  methods: {
    formatterAmount(amount) {
      if (amount) {
        return `${amount}元`
      } else {
        return ''
      }
    },
    disabledDate(current) {
      // return current && current < moment().endOf('day')
      return current && current < moment().startOf('month')
    },
    getSelectOptionsData(value) {
      this.selectOptionsArr = []
      this.selectOptionLoading = true
      let { getSelectOptionsDataAjax } = this.$api.mitigateManageAjax
      if (value) {
        getSelectOptionsDataAjax({
          studentName: value
        })
          .then(
            res => {
              this.selectOptionsArr = res.data
            },
            rej => {}
          )
          .catch(err => {})
          .finally(fin => {
            this.selectOptionLoading = false
          })
      }
    },
    handleChange(value, option) {
      if (value) {
        this.form.name = value
        this.form.id = option.data.attrs.item.studentNo
        this.form.limitAmount = parseFloat(option.data.attrs.item.childCareFeeStandard)
      } else {
        this.form.name = ''
        this.form.id = ''
        this.form.limitAmount = null
        this.$refs.form.validateField('name')
      }
    },
    modalHandleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          console.log('验证成功', this.form)
          let { name, date, amount, remark } = this.form
          let params = {
            studentId: name,
            derateMonth: date,
            derateAmount: amount,
            remark
          }
          this.addTableData(params)
        } else {
          console.log('error submit')
        }
      })
    },
    addTableData(params) {
      let { addTableDataAjax } = this.$api.mitigateManageAjax
      addTableDataAjax(params)
        .then(
          res => {
            this.$message.success('创建成功..')
          },
          rej => {}
        )
        .catch(err => {})
        .finally(fin => {
          this.modalHandleOkCancel()
        })
    },
    modalHandleOkCancel() {
      this.$refs.form.resetFields()
      this.form = {
        name: '',
        id: '',
        date: '',
        amount: null,
        limitAmount: null,
        remark: ''
      }
      this.$emit('modalHandleOkCancel')
    }
  }
}
</script>
<style lang="less" scoped></style>
