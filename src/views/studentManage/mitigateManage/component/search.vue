<template>
  <div>
    <a-card>
      <form-create :rule="rule" v-model="fApi" :option="options"> </form-create>
      <div class="searchBtn">
        <a-Button type="primary" plain @click="submitFn">查询</a-Button>
        <a-Button plain @click="reset()">重置</a-Button>
      </div>
    </a-card>
  </div>
</template>

<script>
import { formToggle } from '@/components/FormFramework/utils/formUtil.js'
import { FormContainer, formOptions } from '@/components/FormFramework/index.js'
import { rules } from '@/components/FormFramework/formRules/index.js'
import formCreate from '@form-create/ant-design-vue'
import _ from 'lodash'
export default {
  name: 'Form',
  data() {
    return {
      fApi: {},
      fc: null,
      options: formOptions,
      rule: []
    }
  },
  created() {
    this.fc = new FormContainer(formCreate, this.fApi)
    let configArr = _.cloneDeep([
      rules.studentName, //学生姓名
      rules.studentID //学生学号
    ])
    configArr.forEach(item => {
      item.col.md = 8
      item.col.sm = 8
      item.col.xs = 8
    })
    this.fc.setConfigArr(configArr)
    this.rule = this.fc.getFormRule()
  },
  mounted() {
    this.fc.build(this.fApi)
    this.fc.initDataSource()
  },
  methods: {
    submitFn() {
      this.fc.submitFormat(data => {
        this.$emit('submitFn', data)
      })
    },
    reset() {
      this.fc.reset()
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-form > .ant-row > .ant-col {
  height: 64px;
}
.searchBtn {
  text-align: right;
  .ant-btn {
    margin-left: 10px;
  }
}
</style>
