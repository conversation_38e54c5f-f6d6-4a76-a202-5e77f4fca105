<template>
  <div class="mitigate-manage">
    <Form ref="searchForm" @submitFn="submitFn" />
    <a-card style="margin-top: 12px">
      <a-table
        size="small"
        :loading="tableLoading"
        :row-key="record => record.id"
        :pagination="false"
        :columns="columns"
        :data-source="tableData"
        bordered
      >
        <template slot="title">
          <div class="btn-group">
            <a-button type="primary" v-permission="['create']" @click="createRow"> 创建 </a-button>
          </div>
        </template>
        <template slot="operation" slot-scope="text, record">
          <div class="btn-group">
            <!-- 只能对未使用的数据进行删除，如果已经发生了结余则不能删除 -->
            <!-- v-if="record.status == 'DISABLE'" -->
            <a-popconfirm
              v-permission="['del']"
              title="确定要删除/作废此条数据么?"
              ok-text="是"
              cancel-text="否"
              @confirm="delRow(text, record)"
            >
              <a-icon slot="icon" type="question-circle-o" style="color: red" />
              <a-button type="link"> 删除 </a-button>
            </a-popconfirm>
          </div>
        </template>
        <template slot="footer">
          <!-- 分页指示器 -->
          <a-pagination
            style="margin-top: 10px; text-align: right"
            :page-size.sync="pagination.pageSize"
            :current="pagination.current"
            show-size-changer
            @showSizeChange="onShowSizeChange"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total"
            :show-total="total => `共 ${total} 条`"
            @change="onPaginationChange"
            show-quick-jumper
          >
            <template slot="buildOptionText" slot-scope="props">
              <span v-if="props.value !== '50'">{{ props.value }}条/页</span>
            </template>
          </a-pagination>
        </template>
      </a-table>
    </a-card>
    <Create :modal-obj="modalObj" @modalHandleOkCancel="modalHandleOkCancel" />
  </div>
</template>
<script>
import Form from './component/search.vue'
import { columns } from './tableColumns.js'
import { searchMapping } from './component/searchMapping.js'
import Create from './component/create.vue'
export default {
  components: {
    Form,
    Create
  },
  data() {
    return {
      msg: '这里是减免管理页面',
      searchObj: {},
      tableLoading: false,
      columns,
      tableData: [],
      pagination: {
        //分页对象
        pageSizeOptions: ['10', '20', '40', '80', '100'],
        pageSize: 10,
        current: 1,
        total: 0
      },
      modalObj: {
        title: '创建减免数据',
        visible: false
      }
    }
  },
  mounted() {
    this.$refs.searchForm.submitFn()
  },
  methods: {
    getTableData() {
      this.tableLoading = true
      let { getTableDataAjax } = this.$api.mitigateManageAjax
      this.tableData = []
      getTableDataAjax({
        filter: this.searchObj,
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(res => {
          let { data } = res
          this.tableData = data.records
          this.pagination.current = data.current
          this.pagination.pageSize = data.size
          this.pagination.total = data.total
        })
        .catch(err => {})
        .finally(() => {
          this.tableLoading = false
        })
    },
    submitFn(data) {
      this.pagination.current = 1
      this.searchObj = searchMapping(data)
      this.getTableData()
    },
    createRow() {
      this.modalObj.visible = true
    },
    modalHandleOkCancel() {
      this.modalObj.visible = false
      this.getTableData()
    },
    delRow(text, record) {
      console.log(record)
      let { delRowAjax } = this.$api.mitigateManageAjax
      delRowAjax(record.id)
        .then(
          res => {
            this.$message.success('删除成功..')
          },
          rej => {}
        )
        .catch(err => {})
        .finally(fin => {
          this.pagination.current = 1
          this.getTableData()
        })
    },
    onShowSizeChange(current, pageSize) {
      this.pagination.current = 1
      this.pagination.pageSize = pageSize
      this.getTableData()
    },
    onPaginationChange(pageNo) {
      this.pagination.current = pageNo
      this.getTableData()
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .ant-table-thead > tr > th {
  padding: 16px !important;
}
/deep/ .ant-table-thead > tr {
  background-color: #fafafa !important;
}
.mitigate-manage {
  /deep/ .ant-table-body {
    margin: 0;
  }
}
</style>
