<template>
  <div class="login-content">
    <account-login
      ref="accountLogin"
      @submit="handleSubmit"
      account-placeholder="账户"
      pwd-placeholder="密码"
      :pwd-rules="pwdRules"
    />
  </div>
</template>

<script>
import AccountLogin from '@/components/TslLogin/AccountLogin'
// import OaLogin from '@/components/TslLogin/OALogin'
import DingLogin from '@/components/TslLogin/DingLogin'
import { timeFix } from '@/utils/util'
import { mapActions } from 'vuex'
import { rstr2b64 } from '@/utils/md5'

export default {
  name: 'TslLogin',
  components: {
    AccountLogin
    // DingLogin
    // OaLogin
  },
  data() {
    return {
      pwdRules: [{ required: true, message: '请输入密码' }],
      customActiveKey: 'name',
      // 1 手机号登录，2 扫码登录
      loginType: 1
    }
  },
  created() {},
  methods: {
    ...mapActions(['Login']),
    handleSubmit(loginParams) {
      const params = this.generateParams(loginParams)
      this.$store
        .dispatch('user/Login', params)
        .then(res => {
          this.handleSubmitSuccess(res)
        })
        .catch(err => this.handleSubmitFailed(err))
        .finally(() => {
          this.$refs.accountLogin.state.loginBtn = false
          // this.state.loginBtn = false
        })
    },
    generateParams(loginParams) {
      return {
        username: loginParams.username,
        // password: rstr2b64(loginParams.password),
        password: loginParams.password
        // code: loginParams.code,
        // loginType: 0,
        // uniqueid: 'image389c9902-5013-4b7a-b95a-417b39560f70',
        // loginSource: String(new Date().getTime() + parseInt(Math.random() * 10000)).slice(3),
        // checkCodeUniqueId: 'image389c9902-5013-4b7a-b95a-417b39560f70',
        // instanceId: 1,
        // tenantId: 1
      }
    },
    handleSubmitSuccess(res) {
      this.$router.push({ path: '/home' })
      // 延迟 1 秒显示欢迎信息
      setTimeout(() => {
        if (localStorage.getItem('resourcesError') === 'false') {
          this.$notification.success({
            message: '欢迎',
            description: `${timeFix()}，欢迎回来`
          })
        }
      }, 1000)
    },
    handleSubmitFailed(err) {
      // this.$notification['error']({
      //   message: '错误',
      //   description: ((err.response || {}).data || {}).message || '请求出现错误，请稍后再试',
      //   duration: 4
      // })
    },
    handleTabClick(key) {
      this.customActiveKey = key
      // this.form.resetFields()
    },
    switchLoginType() {
      if (this.loginType === 1) {
        this.loginType = 2
        this.$refs.dingLogin.initDing()
      } else {
        this.loginType = 1
      }
    }
  }
}
</script>
<style lang="less">
.login-content {
  margin-top: 60px;
  position: relative;
  height: 100%;
  h2 {
    position: relative;
    z-index: 1;
    text-align: center;
    font-size: 27px;
    img {
      width: 47px;
      margin-right: 17px;
    }
  }
  .login-type-switch {
    position: absolute;
    top: -25px;
    right: -40px;
    width: 50px;
    height: 50px;
    overflow: hidden;
    cursor: pointer;
    user-select: none;
    img {
      width: 100%;
    }
    &:before {
      content: ' ';
      position: absolute;
      top: 0;
      left: 0;
      display: inline-block;
      border: 25px solid #fff;
      border-top-color: transparent;
      border-right-color: transparent;
    }
  }

  // 如果有多个登录方式，请删除这个样式
  .ant-tabs-ink-bar {
    background-color: transparent;
  }

  .ding-login {
    position: relative;
    top: -60px;
  }
}
</style>
