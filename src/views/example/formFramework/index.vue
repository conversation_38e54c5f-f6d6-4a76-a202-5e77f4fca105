<template>
  <page-header-wrapper>
    <header>
      <Form @submitFn="submitFn" />
    </header>
    <div class="main">主体</div>
    <footer>尾部</footer>
  </page-header-wrapper>
</template>
<script>
import Form from './component/form.vue'
export default {
  name: 'FormFramework',
  data() {
    return {
      msg: '这里是FormFramework示例界面'
    }
  },
  components: {
    Form
  },
  mounted() {},
  methods: {
    submitFn(data) {
      console.log(data)
    }
  }
}
</script>
<style lang="less" scoped></style>
