<template>
  <div>
    <a-card>
      <form-create
        :rule="rule"
        v-model="fApi"
        :option="options"
        @discount-attribution-month-panel-change="change"
        @rebate-module-code-change="moduleChange"
      >
      </form-create>
      <div class="searchBtn">
        <a-Button type="primary" plain @click="submitFn">查询</a-Button>
        <a-Button plain @click="reset()">重置</a-Button>
        <a-Button plain @click="toggleForm">
          <a-icon v-show="!toggleStatus" type="down" />
          <a-icon v-show="toggleStatus" type="up" />
        </a-Button>
      </div>
    </a-card>
  </div>
</template>

<script>
import { formToggle } from '@/components/FormFramework/utils/formUtil.js'
import { FormContainer, formOptions } from '@/components/FormFramework/index.js'
import { rules } from '@/components/FormFramework/formRules/index.js'
import formCreate from '@form-create/ant-design-vue'
export default {
  name: 'Form',
  data() {
    return {
      fApi: {},
      fc: null,
      options: formOptions,
      rule: [],
      toggleStatus: false
    }
  },
  created() {
    this.fc = new FormContainer(formCreate, this.fApi)
    this.fc.setConfigArr([
      rules.ticket,
      rules.primaryEnterprise,
      rules.rebateModuleCode,
      rules.discountAttributionMonth,
      rules.subordinateMonth
    ])
    this.rule = this.fc.getFormRule()
  },
  mounted() {
    formToggle(this.fApi, this.toggleStatus)
    this.fc.build(this.fApi)
    this.fc.initDataSource()
  },
  methods: {
    // 年月日期组件
    change(value) {
      this.fc.setValueByField('discountAttributionMonth', value)
      // this.fApi.setValue('discountAttributionMonth', value)
    },
    // getfirstLevel(val = '') {
    //   getSelectDataSource(this.fApi, 'firstLevelAccount', { upperLevel: val })
    // },
    // getsecondary(val = '') {
    //   getSelectDataSource(this.fApi, 'secondaryAccount', { upperLevel: val })
    // },
    // 折让模块change
    moduleChange(value) {
      // this.fApi.setValue('firstLevelAccount', '')
      // this.fApi.setValue('secondaryAccount', '')
      // this.getfirstLevel(value)
      // let item = this.allRule.find(item => item.field == 'secondaryAccount')
      // this.fApi.set(item, 'options', [])
    },
    toggleForm() {
      this.toggleStatus = !this.toggleStatus
      formToggle(this.fApi, this.toggleStatus)
    },
    submitFn() {
      this.fc.submitFormat(data => {
        this.$emit('submitFn', data)
      })
      // this.fApi.submit((formData, fApi) => {
      //   let data = this.fc.submitFormat(formData)
      //   this.$emit('submitFn', data)
      // })
    },
    reset() {
      this.fc.reset()
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-form > .ant-row > .ant-col {
  height: 64px;
}
.searchBtn {
  text-align: right;
  .ant-btn {
    margin-left: 10px;
  }
}
</style>
