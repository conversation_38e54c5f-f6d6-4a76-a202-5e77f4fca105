<template>
  <page-header-wrapper>
    <div class="page-wrap">
      <h2 v-if="!$store.state.changeNetwork.network">
        <h3>我没网了</h3>
        <button @click="onRefresh">刷新</button>
      </h2>
      <button @click="getData">+</button>
      <!-- 图片上传 -->
      <!-- <upload-img
        :file-list.sync="formData.uploadList"
        description="图片限制最大 1M"
        @updateUploadList="updateUploadList"
        @removeUploadList="removeUploadList"
        :max-num="2"
        :show-remove-icon="true"
      ></upload-img> -->
      <!-- 文件上传 -->
      <!-- <upload-file
        :file-list.sync="formData.fileList"
        description="导入文件"
        @updateUploadList="updateFileList"
        @removeUploadList="removeFileList"
        :show-upload-list="false"
        :max-num="1"
        :show-remove-icon="true"
      >
      </upload-file> -->

      <!-- 标题 -->
      <div class="table-wrap">
        <!-- <div class="table-wrap-title"> -->
        <span class="table-wrap-title">订单占用明细</span>
        <span class="table-wrap-subtitle">当前查询结果合计：￥999.99</span>
        <!-- </div> -->
      </div>

      <!-- 按钮区域 -->
      <div class="button-wrap">
        <a-button type="primary" @click="() => download()"> 下载 </a-button>
        <a-button type="primary" @click="() => exportFile()"> 导出 </a-button>
        <a-button type="primary" @click="() => bulkOperation()"> 批量操作 </a-button>
      </div>
      <!-- 提示 -->
      <div class="notify-wrap">
        <a-alert :show-icon="true" style="margin-bottom: 16px">
          <template slot="message">
            <span>
              已选择: <a>{{ this.selectedRowKeys.length }}</a> 条
            </span>
          </template>
        </a-alert>
      </div>
      <!-- 表格筛选配置 -->
      <filter-column :filter-columns="filterColumns" @render="renderTableColumns"></filter-column>
      <a-table
        :loading="loading"
        :scroll="{ x: widthTable }"
        :row-key="record => record.key"
        :row-class-name="rowClassName"
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange,
          getCheckboxProps: getCheckboxProps
        }"
        :pagination="false"
        :columns="columns"
        :data-source="tableData"
        bordered
      >
        <!-- 编辑单元格 -->
        <template v-for="col in ['address']" :slot="col" slot-scope="text, record">
          <div :key="col" v-if="record.name !== totalName">
            <a-input
              v-if="record.editable"
              style="margin: -5px 0"
              :value="text"
              @change="e => handleChange(e.target.value, record.key, col)"
            />
            <template v-else>
              {{ text }}
            </template>
          </div>
        </template>
        <!-- 可变操作项 -->
        <template slot="operation" slot-scope="text, record">
          <div class="editable-row-operations" v-if="record.name !== totalName">
            <span v-if="record.editable">
              <a @click="() => save(record.key)">保存</a>
              <a-popconfirm title="确定取消吗?" @confirm="() => cancel(record.key)">
                <a>取消</a>
              </a-popconfirm>
            </span>
            <span v-else>
              <a :disabled="editingKey !== ''" @click="() => edit(record.key)">编辑</a>
            </span>
          </div>
        </template>
        <!-- 上传图片 -->
        <template slot="uploadImg" slot-scope="text, record, index">
          <div v-if="record.name !== totalName">
            <a @click="() => uploadImgRow(text, record, index)">上传</a>
            <a @click="() => detailRow(text, record, index)">查看</a>
          </div>
        </template>
        <!-- 页脚 -->
        <!-- <template slot="footer"> 合计 </template> -->
      </a-table>
      <!-- 分页指示器 -->
      <a-pagination
        style="margin-top: 10px; float: right"
        :page-size.sync="pagination.pageSize"
        :current="pagination.current"
        show-size-changer
        @showSizeChange="onShowSizeChange"
        :page-size-options="pagination.pageSizeOptions"
        :total="pagination.total"
        :show-total="total => `共 ${total} 条`"
        @change="onPaginationChange"
        show-quick-jumper
      >
        <template slot="buildOptionText" slot-scope="props">
          <span v-if="props.value !== '50'">{{ props.value }}条/页</span>
        </template>
      </a-pagination>
    </div>
  </page-header-wrapper>
</template>
<script>
// import UploadImg from './../components/uploadImg'
// import UploadFile from './../components/uploadFile'
import FilterColumn from '@/components/FilterColumn/index.js'
// import { getDemoList } from '@/api/demoList'
import { fixedColumns } from './columns'
import _ from 'lodash'

let dynamicColumns = []

export default {
  name: 'TableDemo',
  data() {
    return {
      totalName: '合计',
      // 表格配置
      tableData: [],
      columns: [],
      // 筛选列配置
      widthTable: 0,
      filterColumns: [],
      // 分页
      pagination: {
        pageSizeOptions: ['10', '20', '40', '80', '100'],
        pageSize: 10,
        current: 1,
        total: 0
      },
      // 编辑行配置
      editingKey: '',
      selectedRowKeys: [], //复选框
      formData: {
        uploadList: [], // 上传图片列表
        fileList: [] // 上传文件列表
      }, // 表单信息
      loading: false
    }
  },
  components: {
    // UploadImg,
    // UploadFile,
    FilterColumn
  },
  mounted() {
    // 生成表头
    this.generateColumns()
    // 请求接口获取列表数据
    this.searchData()
    console.log(this.$store)
  },
  methods: {
    getData() {
      this.$api.appAjax.getData().then(res => {
        console.log(res)
      })
    },
    handleClick() {
      this.searchData()
    },
    // 通过跳转一个空页面再返回的方式来实现刷新当前页面数据的目的
    onRefresh() {
      this.$router.replace('/example/refresh')
    },
    // 筛选列
    renderTableColumns(data, widthTable) {
      this.columns = data
      this.widthTable = widthTable
    },

    // ***************** 请求接口获取表格渲染数据 *************************
    async getTableData(params = {}) {
      console.log('---------params---------')
      console.log(params)
      let { getTableListAjax } = this.$api.appAjax
      this.loading = true
      try {
        let res = await getTableListAjax(params.params)
        console.log('--------- res ---------')
        console.log(res)
        let data = res.data
        console.log(data)
        const pagination = { ...this.pagination }
        pagination.total = data.total // 数据总数
        pagination.pageSize = data.pageSize // 每页条目数
        pagination.current = data.pageNum // 当前页
        this.pagination = pagination
        this.loading = false
        // this.tableData = data.content
        this.tableData = data.list
        // this.tableData = data.content && data.content.length > 0 ? [...data.content, data.totalNum] : []
        this.cacheData = this.tableData.map(item => ({ ...item }))
      } catch (error) {
        console.log(error)
      }
    },

    searchData() {
      this.getTableData({
        params: {
          pageNo: 1,
          // pageSize: this.pagination.pageSize,
          pageSize: this.pagination.pageSize
        }
      })
    },
    // pageSize 变化的回调
    onShowSizeChange(current, pageSize) {
      console.log(`[onShowSizeChange]current=${current},pageSize=${pageSize}`)
      this.getTableData({
        params: {
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize
        }
      })
    },
    // 翻页
    onPaginationChange(pageNo) {
      console.log(`[onPaginationChange]pageNo=${pageNo}`)
      this.getTableData({
        params: {
          pageSize: this.pagination.pageSize,
          pageNo: pageNo
        }
      })
    },
    // ***************** 表头配置 开始 *************************
    // 生成动态列
    generateColumns() {
      for (let i = 2; i >= 1; i--) {
        dynamicColumns.push({
          title: `${i} 月`,
          children: [
            {
              title: '扣减',
              dataIndex: `deduction${i}`,
              key: `deduction${i}`,
              width: 70
            },
            {
              title: '已下发',
              dataIndex: `send${i}`,
              key: `send${i}`,
              width: 70
            },
            {
              title: '确认',
              dataIndex: `affirm${i}`,
              key: `affirm${i}`,
              width: 70
            }
          ]
        })
      }
      this.columns = [...fixedColumns, ...dynamicColumns]
      console.log(this.columns)
      // 筛选列
      this.filterColumns = _.cloneDeep(this.columns)
    },

    // ***************** 表头配置 结束 *************************

    // ***************** 按钮组操作配置 开始 *************************
    bulkOperation() {
      console.log('批量操作数据', this.selectedRowKeys)
    },
    download() {
      this.$message.info('下载')
    },
    exportFile() {
      this.$message.info('导出')
    },
    // ***************** 按钮组操作配置 结束 *************************

    // ***************** checkbox配置 开始 *************************
    // 选择框的默认属性配置
    getCheckboxProps(record) {
      return {
        props: {
          name: record.name,
          disabled: record.name == this.totalName
        }
      }
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
      console.log('复选框选中元素keys', selectedRowKeys)
    },
    // ***************** checkbox配置 结束 *************************

    // ***************** 编辑行配置 开始 *************************
    handleChange(value, key, column) {
      const newData = [...this.tableData]
      const target = newData.filter(item => key === item.key)[0]
      if (target) {
        target[column] = value
        this.tableData = newData
      }
    },
    edit(key) {
      const newData = [...this.tableData]
      const target = newData.filter(item => key === item.key)[0]
      console.log('target', target)
      this.editingKey = key
      if (target) {
        target.editable = true
        this.tableData = newData
      }
    },
    save(key) {
      const newData = [...this.tableData]
      const newCacheData = [...this.cacheData]
      const target = newData.filter(item => key === item.key)[0]
      const targetCache = newCacheData.filter(item => key === item.key)[0]
      if (target && targetCache) {
        delete target.editable
        this.tableData = newData
        Object.assign(targetCache, target)
        this.cacheData = newCacheData
      }
      this.editingKey = ''
    },
    cancel(key) {
      const newData = [...this.tableData]
      const target = newData.filter(item => key === item.key)[0]
      this.editingKey = ''
      if (target) {
        Object.assign(target, this.cacheData.filter(item => key === item.key)[0])
        delete target.editable
        this.tableData = newData
      }
    },
    // ***************** 编辑行配置 结束 *************************

    // ***************** 上传图片 开始 *************************
    // 更新图片
    updateUploadList(param) {
      this.formData.uploadList = param
      console.log('this.formData.uploadList', this.formData.uploadList)
    },
    // 删除图片
    removeUploadList(param) {
      this.formData.uploadList = param
      console.log('this.formData.uploadList', this.formData.uploadList)
    },
    // 更新文件
    updateFileList(param) {
      this.formData.fileList = param
      console.log('this.formData.fileList', this.formData.fileList)
    },
    // 删除文件
    removeFileList(param) {
      this.formData.fileList = param
      console.log('this.formData.fileList', this.formData.fileList)
    },
    // ***************** 上传图片 结束 *************************

    // ***************** 行操作 开始 *************************
    // 设置行背景色
    rowClassName(record, index) {
      return 'table-white'
    },

    uploadImgRow(text, record, index) {
      console.log('[uploadImgRow]', text, record, index)
    },

    detailRow(text, record, index) {}
    // ***************** 行操作 结束 *************************
  }
}
</script>
<style lang="less" scoped>
.page-wrap {
  background-color: #fff;
  .table-wrap {
    padding: 20px 0;
    margin: 10px 10px 16px;
    border-bottom: 1px solid #e8e8e8;
    .table-wrap-title {
      padding-left: 15px;
      font-size: 16px;
      font-weight: bold;
    }
    .table-wrap-subtitle {
      margin-left: 30px;
    }
  }
  .notify-wrap {
    margin: 10px;
  }
  .button-wrap {
    text-align: right;
    margin: 10px;
  }
  .button-wrap button {
    margin-right: 10px;
  }
}
.editable-row-operations a {
  margin-right: 8px;
}
/deep/.table-white {
  background-color: #fff;
}
</style>
