const renderContent = {
  children: '',
  attrs: {
    rowSpan: 0
  }
}
const totalName = '合计'
const mergeCell = (record, rowVal) => {
  if (record.name == totalName) {
    return renderContent
  } else {
    return rowVal
  }
}
export const fixedColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
    width: 50,
    customRender: (text, record, index) => {
      // return mergeCell(record, `${index + 1}`)
      if (record.name == totalName) {
        return {
          children: totalName,
          attrs: {
            // colSpan: 4 //单元格合并
          }
        }
      } else {
        return `${index + 1}`
      }
    }
  },
  {
    title: '票折大区',
    dataIndex: 'name',
    width: 100
    // customRender: (text, record, index) => {
    // if (record.name == totalName) {
    //   return {
    //     children: '合计',
    //     attrs: {
    //       // colSpan: getMergeColCount() //单元格合并
    //     }
    //   }
    // } else {
    // return `${index + 1}`
    // }
    // }
  },
  {
    title: '一级收款企业',
    dataIndex: 'age',
    width: 100
    // show: true
    // customRender: (text, record) => {
    //   return mergeCell(record, record.age)
    // }
  },
  {
    title: '二级收款企业',
    dataIndex: 'address',
    width: 100,
    scopedSlots: { customRender: 'address' }
    // customRender: (text, record) => {
    //   return mergeCell(record, record.address)
    // }
  },
  {
    title: '折让金额',
    dataIndex: 'discount',
    width: 80,
    scopedSlots: { customRender: 'discount' }
  },
  {
    title: '支付金额',
    dataIndex: 'payment',
    width: 80,
    scopedSlots: { customRender: 'payment' }
  },
  {
    title: '凭证',
    dataIndex: 'uploadImg',
    width: 50,
    scopedSlots: { customRender: 'uploadImg' }
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 50,
    scopedSlots: { customRender: 'operation' }
  }
]
