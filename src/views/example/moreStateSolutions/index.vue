<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-06 13:19:38
 * @Description: 科目列表
-->
<template>
  <page-header-wrapper>
    <a-card>
      <a-space>
        <a-button type="primary" @click="add"> 新增 </a-button>
        <a-button type="primary" @click="edit"> 编辑 </a-button>
      </a-space>
    </a-card>
    <Modal :visible="visible" @cancel="cancel" @init="fetchList" :form-data="formData" />
  </page-header-wrapper>
</template>
<script>
import Modal from './components/modal.vue'
import * as Cfg from './utils/modal'

let fn = Cfg.getInstance()
export default {
  components: {
    Modal
  },
  data() {
    return {
      visible: false,
      formData: {}
    }
  },
  methods: {
    edit(item) {
      this.handleEdit(item.id)
    },
    handleEdit(id) {
      this.instance = fn('UPDATE')
      this.instance.detail(id, data => {
        console.log(data)
        this.formData = data
        this.visible = true
      })
    },
    add() {
      this.visible = true
    },
    cancel() {
      this.visible = false
      this.formData = {}
    },
    async fetchList() {
      try {
        // success
      } catch (err) {
        this.$message.error('请求错误')
      }
    }
  }
}
</script>
<style></style>
