/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-07 15:24:28
 * @Description:modal类
 */
import { getDetailAjax } from '@/api/manage'

class Common {
  save(obj, successCb, errorCb) {
    console.log('Common/save update', obj)
  }
}

class Create extends Common {
  constructor() {
    super()
  }
  save(obj, successCb, errorCb) {
    console.log('Create/save update', obj)
    successCb()
    // super.save(obj, successCb, errorCb)
  }
}
class Update extends Common {
  constructor() {
    super()
  }
  save(obj, successCb, errorCb) {
    console.log('Update/save update', obj)
    successCb()
  }
  // 获取详情
  async detail(id, successCb, errorCb) {
    getDetailAjax(id)
      .then(res => {
        console.log(res)
        if (res.resCode === 0) {
          successCb(res.data)
        }
      })
      .catch(e => {
        errorCb(e)
      })
  }
}

export function getInstance() {
  let instanceObj = {}
  return function (status) {
    console.log('status', status)
    if (!instanceObj[status]) {
      console.log('开始取了，但是没取到，初始化')
      instanceObj['CREATE'] = new Create()
      instanceObj['UPDATE'] = new Update()
    } else {
      console.log('从闭包里取到了')
    }
    return instanceObj[status]
  }
}
