<template>
  <div>
    <a-modal
      title="科目调整"
      :visible="visible"
      :width="950"
      :confirm-loading="confirmLoading"
      @ok="preSubmit"
      @cancel="handleCancel"
    >
      <ModalForm ref="modalForm" :form="form" :disabled="isView" />
    </a-modal>
  </div>
</template>
<script>
import ModalForm from './modalForm.vue'
import * as Cfg from '../utils/modal'

let fn = Cfg.getInstance()
let baseForm = {
  discountAttributionMonth: [],
  remark: undefined
}
export default {
  components: {
    ModalForm
  },
  data() {
    return {
      confirmLoading: false,
      wrapperCol: { span: 24 },
      certificate: '',
      remark: '',
      form: {
        discountAttributionMonth: [],
        remark: undefined
      },
      cancelModalVisible: false,
      confirmModalVisible: false,
      instance: null
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    isView() {
      const isNotPlainObject = Object.keys(this.formData).length > 0
      return isNotPlainObject
    }
  },
  watch: {
    visible(val) {
      if (val && this.formData && Object.keys(this.formData).length > 0) {
        Object.assign(this.form, this.formData)
      } else {
        Object.assign(this.form, this._.cloneDeep(baseForm))
      }
    }
  },
  created() {
    this.form = this._.cloneDeep(baseForm)
  },
  methods: {
    isFormValidate() {
      return this.$refs.modalForm.isValidate()
    },
    submit() {
      this.$message.success('新建成功！')
      this.confirmCancel()
    },
    handleCreate() {
      this.instance = fn('CREATE')
      this.instance.save(this.form, () => {
        this.submit()
      })
    },
    handleUpdate() {
      this.instance = fn('UPDATE')
      this.instance.save(this.form, () => {
        this.$message.success('修改成功！')
        this.confirmCancel()
      })
    },
    preSubmit(e) {
      if (!this.isView) {
        if (this.isFormValidate()) {
          this.handleCreate()
        }
      } else {
        if (this.isFormValidate()) {
          this.handleUpdate()
        }
      }
    },
    handleCancel(e) {
      // this.close()
      if (!this.isView) {
        this.confirmCancel()
      } else {
        this.confirmCancel()
      }
    },
    confirmCancel() {
      this.close()
    },
    close() {
      this.$emit('cancel', false)
      this.reset()
    },
    reset() {
      this.clearCertificate()
      this.clearForm()
    },
    clearCertificate() {
      this.certificate = ''
      this.remark = ''
    },
    clearForm() {
      this.$refs.modalForm.resetForm()
    }
  }
}
</script>
<style></style>
