<template>
  <a-form-model ref="modalRefs" layout="inline" :model="form" label-align="left" :rules="rules">
    <a-row>
      <a-col class="mb30" :span="24">
        <a-form-model-item
          label="备注"
          prop="remark"
          :label-col="{ span: 24 }"
          :wrapper-col="{ style: { width: '600px' } }"
        >
          <a-input placeholder="请输入备注" :disabled="disabled" v-model="form.remark" />
        </a-form-model-item>
      </a-col>
      <a-col :span="8">
        <a-form-model-item class="mt10" allow-clear :disabled="disabled" label="日期" prop="discountAttributionMonth">
          <a-range-picker v-model="form.discountAttributionMonth" @change="onChange"> </a-range-picker>
        </a-form-model-item>
      </a-col>
    </a-row>
  </a-form-model>
</template>
<script>
export default {
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: <PERSON><PERSON>an,
      default: false
    }
  },
  data() {
    return {
      timeout: null,
      rules: {}
    }
  },
  methods: {
    onChange() {
      console.log(this.form.discountAttributionMonth)
    },
    resetForm() {
      this.$refs.modalRefs.resetFields()
    },
    isValidate() {
      let result = false
      this.$refs.modalRefs.validate(valid => {
        result = valid
      })
      return result
    }
  }
}
</script>
<style></style>
