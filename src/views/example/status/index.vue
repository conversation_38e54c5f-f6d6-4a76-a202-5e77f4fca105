<template>
  <div>
    <h2>{{ msg }}</h2>
    <button @click="handleClickGet">get</button>
    <button @click="handleClickPost">post</button>
    <button @click="handleClick401">401</button>
    <button @click="handleClick403">403</button>
    <button @click="handleClick404">404</button>
    <button @click="handleClick500">500</button>
  </div>
</template>
<script>
export default {
  data() {
    return {
      msg: '这里是请求实例界面'
    }
  },
  methods: {
    handleClickGet() {
      let { getData } = this.$api.statusAjax
      getData()
        .then(res => {
          console.log(res)
        })
        .catch(err => {})
        .finally(fin => {})
    },
    handleClickPost() {
      let { postData } = this.$api.statusAjax
      postData()
        .then(res => {
          console.log(res)
        })
        .catch(err => {})
        .finally(fin => {})
    },
    handleClick401() {
      let { get401 } = this.$api.statusAjax
      get401()
        .then(res => {
          console.log(res)
        })
        .catch(err => {})
        .finally(fin => {})
    },
    handleClick403() {
      let { get403 } = this.$api.statusAjax
      get403()
        .then(res => {
          console.log(res)
        })
        .catch(err => {})
        .finally(fin => {})
    },
    handleClick404() {
      let { get404 } = this.$api.statusAjax
      get404()
        .then(res => {
          console.log(res)
        })
        .catch(err => {})
        .finally(fin => {})
    },
    handleClick500() {
      let { get500 } = this.$api.statusAjax
      get500()
        .then(res => {
          console.log(res)
        })
        .catch(err => {})
        .finally(fin => {})
    }
  }
}
</script>
