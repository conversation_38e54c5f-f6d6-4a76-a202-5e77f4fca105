<template>
  <div>
    <a-card>
      <form-create :rule="rule" v-model="fApi" :option="options"> </form-create>
      <!-- <div class="searchBtn">
        <a-Button type="primary" plain @click="submitFn">查询</a-Button>
        <a-Button plain @click="reset()">重置</a-Button>
      </div> -->
      <div style="text-align: right; margin-top: 12px">
        <a-button type="default" v-debounce:[abolishCancel] style="margin-right: 10px">取消</a-button>
        <a-button type="primary" v-debounce:[abolishOk]>确定</a-button>
      </div>
    </a-card>
  </div>
</template>

<script>
import { FormContainer, formOptions } from '@/components/FormFramework/index.js'
import { rules } from '@/components/FormFramework/formRules/index.js'
import formCreate from '@form-create/ant-design-vue'
import _ from 'lodash'
import moment from 'moment'
export default {
  name: 'Form',
  data() {
    return {
      fApi: {},
      fc: null,
      options: formOptions,
      rule: []
    }
  },
  created() {
    this.fc = new FormContainer(formCreate, this.fApi)
    let configArr = _.cloneDeep([
      rules.refundTime, // 退款日期
      rules.refundReason // 退款原因
    ])
    this.fc.setConfigArr(configArr)
    this.rule = this.fc.getFormRule()
  },
  mounted() {
    this.fc.build(this.fApi)
    this.fc.initDataSource()
    this.fc.initValidate([rules.refundTime, rules.refundReason])
    formOptions.form.labelCol.span = 7
    formOptions.form.wrapperCol.span = 17
  },
  methods: {
    abolishOk() {
      this.fc.submitFormat(data => {
        this.$emit('submitFn', data)
      })
    },
    // 取消
    abolishCancel() {
      this.$emit('refundCancel')
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-form > .ant-row > .ant-col {
  height: 64px;
}
.searchBtn {
  text-align: right;
  .ant-btn {
    margin-left: 10px;
  }
}
</style>
