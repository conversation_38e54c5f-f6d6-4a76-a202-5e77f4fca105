<template>
  <div class="conservation-fee">
    <Form ref="searchForm" @submitFn="submitFn" />
    <a-card style="margin-top: 12px">
      <a-table
        :scroll="{ x: 2600 }"
        :loading="tableLoading"
        :row-key="record => record.key"
        :pagination="false"
        :columns="columns"
        :data-source="tableData"
        bordered
      >
        <template slot="title">
          <div class="table-header-wrap" style="display: flex; justify-content: flex-end">
            <div style="width: 100%; display: flex; justify-content: flex-end">
              <a-button v-permission="['exportdata']" type="primary" v-debounce:[exportData]> 导出数据 </a-button>
              <a-button
                style="margin: 0 10px"
                v-permission="['exporttemplate']"
                type="primary"
                v-debounce:[exportTemplate]
              >
                下载模板
              </a-button>
              <upload-file
                v-permission="['importdata']"
                :file-list.sync="fileList"
                description="导入"
                @updateUploadList="updateFileList"
                :max-num="1"
                :show-upload-list="false"
                :show-remove-icon="true"
              >
              </upload-file>
            </div>
          </div>
        </template>
        <template slot="operation" slot-scope="text, record">
          <div class="btn-group">
            <a-button v-permission="['watch']" type="link" @click="watch(record)"> 查看 </a-button>
            <a-button
              v-permission="['abolish']"
              type="link"
              @click="handleClickAbolish(record)"
              v-if="record.status === 0 && record.paymentStatus === 0"
            >
              作废
            </a-button>
            <a-button
              v-permission="['payment']"
              type="link"
              @click="payment(record)"
              v-if="record.paymentStatus === 0 && record.status === 0"
            >
              付款
            </a-button>
            <a-button
              v-permission="['print']"
              v-if="record.paymentStatus === 1"
              type="link"
              @click="handleClickPrint(record)"
            >
              打印
            </a-button>
          </div>
        </template>
        <template slot="footer">
          <!-- 分页指示器 -->
          <a-pagination
            style="margin-top: 10px; text-align: right"
            :page-size.sync="pagination.pageSize"
            :current="pagination.current"
            show-size-changer
            @showSizeChange="onShowSizeChange"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total"
            :show-total="total => `共 ${total} 条`"
            @change="onPaginationChange"
            show-quick-jumper
          >
            <template slot="buildOptionText" slot-scope="props">
              <span v-if="props.value !== '50'">{{ props.value }}条/页</span>
            </template>
          </a-pagination>
        </template>
      </a-table>
    </a-card>
    <a-modal :width="1080" v-model="rowWatchVisible" title="订单明细" :footer="null">
      <a-spin :spinning="orderDetailLoading">
        <a-collapse v-model="activeKey">
          <a-collapse-panel key="studentInfo" header="学生信息" :show-arrow="false" disabled>
            <div style="width: 25%; display: inline-block">
              <span style="display: inline-block; width: 98px; text-align: right">学生姓名：</span>
              <strong>{{ rowWatchObj.studentName }}</strong>
            </div>
            <div style="width: 25%; display: inline-block">
              <span style="display: inline-block; width: 112px; text-align: right">学生学号：</span>
              <strong>{{ rowWatchObj.studentCode }}</strong>
            </div>
            <div style="width: 25%; display: inline-block">
              <span style="display: inline-block; width: 140px; text-align: right">班级：</span>
              <strong>{{ rowWatchObj.grade }}</strong>
            </div>
            <div style="width: 25%; display: inline-block">
              <span style="display: inline-block; width: 98px; text-align: right">联系方式：</span>
              <strong>{{ rowWatchObj.contactInfo }}</strong>
            </div>
          </a-collapse-panel>
          <a-collapse-panel key="orderInfo" header="订单信息" :show-arrow="false" disabled>
            <div style="width: 25%; display: inline-block; vertical-align: top">
              <span style="display: inline-block; width: 98px; text-align: right">收费项目：</span>
              <strong>{{ rowWatchObj.chargeItem }}</strong>
            </div>
            <div style="width: 25%; display: inline-block; vertical-align: top">
              <span style="display: inline-block; width: 112px; text-align: right">收费金额：</span>
              <strong>{{ rowWatchObj.chargeAmount }}元</strong>
            </div>
            <div style="width: 20%; display: inline-block; vertical-align: top">
              <span style="display: inline-block; width: 140px; text-align: right"> 订单状态：</span>
              <strong>{{ rowWatchObj.statusName }}</strong>
            </div>
            <div style="width: 30%; display: inline-block; vertical-align: top">
              <span style="display: inline-block; width: 139px; text-align: right"> 订单创建时间：</span>
              <strong>{{ rowWatchObj.createTimeStr }}</strong>
            </div>
            <div style="width: 45%; display: inline-block; margin: 6px 0">
              <span style="display: inline-block; width: 98px; text-align: right"> 订单编号：</span>
              <strong>{{ rowWatchObj.code }}</strong>
            </div>
          </a-collapse-panel>
          <a-collapse-panel key="paymentInfo" header="付款信息" :show-arrow="false" disabled>
            <div style="width: 50%; display: inline-block">
              <span style="display: inline-block; width: 98px; text-align: right"> 付款时间：</span>
              <strong>{{ rowWatchObj.paymentTimeStr }}</strong>
            </div>
            <div style="width: 50%; display: inline-block">
              <span style="display: inline-block; width: 140px; text-align: right"> 支付状态：</span>
              <strong>{{ rowWatchObj.paymentStatusName }}</strong>
            </div>
            <div style="width: 50%; display: inline-block; margin: 6px 0">
              <span style="display: inline-block; width: 98px; text-align: right"> 付款方式：</span>
              <strong>{{ rowWatchObj.paymentDetailsStr }}</strong>
            </div>
            <div v-show="rowWatchObj.paymentChannel" style="width: 50%; display: inline-block; margin: 6px 0">
              <span style="display: inline-block; width: 140px; text-align: right"> 收款人：</span>
              <strong>{{ rowWatchObj.collectionPerson }}</strong>
            </div>
          </a-collapse-panel>
          <a-collapse-panel key="remark" header="备注信息" :show-arrow="false" disabled>
            <div>{{ rowWatchObj.remark }}</div>
          </a-collapse-panel>
          <a-collapse-panel key="deleteInfo" header="作废信息" :show-arrow="false" disabled>
            <div style="width: 50%; display: inline-block">
              <span style="display: inline-block; width: 98px; text-align: right"> 作废时间：</span>
              <strong>{{ rowWatchObj.deleteTimeStr }}</strong>
            </div>
            <div style="width: 50%; display: inline-block">
              <span style="display: inline-block; width: 140px; text-align: right"> 作废人员：</span>
              <strong>{{ rowWatchObj.deletePerson }}</strong>
            </div>
            <div style="width: 50%; display: inline-block; margin: 6px 0">
              <span style="display: inline-block; width: 98px; text-align: right"> 作废原因：</span>
              <strong>{{ rowWatchObj.deleteReason }}</strong>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </a-spin>
    </a-modal>
    <a-modal
      :closable="false"
      destroy-on-close
      :mask-closable="false"
      :width="300"
      v-model="paymentVisible"
      title="付款"
      :footer="null"
    >
      <div>
        <h3>
          <span>订单总额:</span>
          {{ paymentTotalOrder }}元
        </h3>
        <ul style="padding: 0; margin: 0">
          <li style="display: flex; justify-content: space-between; margin-top: 12px">
            <strong>
              <em>付款方式</em>
            </strong>
            <strong>
              <em>付款额度</em>
            </strong>
          </li>
          <li
            v-for="(item, index) in paymentArray"
            :key="index"
            style="
              display: flex;
              justify-content: space-between;
              margin-top: 12px;
              padding-bottom: 6px;
              border-bottom: 1px solid #eee;
            "
          >
            <div>
              <a-checkbox @change="e => switchCheckbox(e, item.type)">
                {{ item.name }}
              </a-checkbox>
            </div>
            <a-input-number
              :precision="2"
              :step="0.01"
              :disabled="!item.checked"
              size="small"
              v-model="item.quota"
              :min="0"
            />
          </li>
          <li style="margin-top: 12px">
            <a-input v-model="posNumber" :disabled="posDisabled" addon-before="pos机号" />
          </li>
          <li style="margin-top: 12px">注：付款额度总额不能超过订单总额</li>
        </ul>
        <div style="text-align: right; margin-top: 12px">
          <a-button type="default" v-debounce:[handleCancelOfPayment] style="margin-right: 10px">取消</a-button>
          <a-button type="primary" v-debounce:[handleOkOfPayment]>确定</a-button>
        </div>
      </div>
    </a-modal>
    <!-- 文件导入 -->
    <a-modal
      :closable="false"
      destroy-on-close
      :mask-closable="false"
      :width="360"
      v-model="fileImportBool"
      title="文件导入"
      :footer="null"
    >
      <div>
        <p>文件已校验，点击确认完成导入</p>
        <p>
          本次导入excel条数为:
          <strong>{{ importedCount }}条</strong>
        </p>
        <p>
          本次导入汇总金额为:
          <strong>{{ chargedAmountSum }}元</strong>
        </p>
        <div style="text-align: right; margin-top: 12px">
          <a-button type="default" v-debounce:[fileImportCancel] style="margin-right: 10px">取消</a-button>
          <a-button type="primary" v-debounce:[fileImportOk]>确定</a-button>
        </div>
      </div>
    </a-modal>
    <a-modal
      :width="800"
      :closable="false"
      destroy-on-close
      :mask-closable="false"
      v-model="conservationFeeBool"
      title="幼儿园-代收费订单"
      :footer="null"
    >
      <div style="font-size: 12px">
        <div id="printOrder" style="position: relative">
          <!-- <img
            width="160"
            style="position: absolute; left: 50%; top: 0; transform: translateX(-50%)"
            src="../../../assets/home/<USER>"
            alt=""
          /> -->
          <h4 v-if="printOrderHeaderBool" style="text-align: center; margin: 0">
            <strong>华夏未来天人智慧幼儿园</strong>
            <div style="text-align: center">代收费订单</div>
          </h4>
          <div class="student-info">
            <p style="margin: 6px 0">
              <strong>学生信息</strong>
            </p>
            <div style="width: 42%; display: inline-block">
              <span style="display: inline-block; width: 102px; text-align: right">姓名：</span>
              {{ rowWatchObj.studentName }}
            </div>
            <div style="width: 35%; display: inline-block">
              <span style="display: inline-block; width: 112px; text-align: right">学号：</span>
              {{ rowWatchObj.studentCode }}
            </div>
          </div>
          <div class="order-info">
            <p style="margin: 6px 0">
              <strong>订单信息</strong>
            </p>
            <div style="width: 42%; display: inline-block">
              <span style="display: inline-block; width: 102px; text-align: right">订单编号：</span>
              {{ rowWatchObj.code }}
            </div>
            <div style="width: 35%; display: inline-block; vertical-align: top">
              <span style="display: inline-block; width: 112px; text-align: right">收费项目：</span>
              {{ rowWatchObj.chargeItem }}
            </div>
            <div style="width: 23%; display: inline-block">
              <span style="display: inline-block; width: 112px; text-align: right">收费金额：</span>
              {{ rowWatchObj.chargeAmount }}元
            </div>
          </div>
          <div class="payment-info">
            <p style="margin: 6px 0">
              <strong>支付信息</strong>
            </p>
            <div style="width: 42%; display: inline-block">
              <span style="display: inline-block; width: 102px; text-align: right">支付金额：</span>
              {{ rowWatchObj.payAmount }}元
            </div>
            <div style="width: 35%; display: inline-block">
              <span style="display: inline-block; width: 112px; text-align: right">支付时间：</span>
              {{ rowWatchObj.paymentTimeStr }}
            </div>
            <div style="width: 23%; display: inline-block">
              <span style="display: inline-block; width: 112px; text-align: right">支付状态：</span>
              {{ rowWatchObj.paymentStatusName }}
            </div>
            <div style="width: 100%; display: inline-block; margin-top: 6px">
              <span style="display: inline-block; width: 102px; text-align: right">支付方式：</span>
              {{ rowWatchObj.paymentDetailsStr }}
            </div>
            <div style="width: 42%; display: inline-block">历史收据作废，以本收据为准</div>
            <div style="width: 35%; display: inline-block">
              <span style="display: inline-block; width: 112px; text-align: right; margin-top: 6px">打印时间：</span>
              {{ moment().format('YYYY-MM-DD HH:mm:ss') }}
            </div>
            <div style="width: 23%; display: inline-block">
              <span style="display: inline-block; width: 112px; text-align: right; margin-top: 6px">经办人：</span>
            </div>
          </div>
        </div>
        <div style="text-align: center; margin-top: 12px">
          <a-button type="default" @click="printCancel">取消</a-button>
          <a-button style="margin-left: 10px" type="primary" @click="printOk">确定打印</a-button>
        </div>
      </div>
    </a-modal>
    <a-modal
      :closable="false"
      destroy-on-close
      :mask-closable="false"
      v-model="abolishBool"
      title="作废原因"
      :footer="null"
    >
      <div>
        <a-textarea v-model="abolishReason" :maxlength="255" placeholder="请输入作废原因(多行输入)" auto-size />
        <div style="text-align: right; margin-top: 12px">
          <a-button type="default" v-debounce:[abolishCancel] style="margin-right: 10px">取消</a-button>
          <a-button type="primary" v-debounce:[abolishOk]>确定</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script>
import moment from 'moment'
import { Print } from '@/utils/Print.js'
import UploadFile from '../../common/components/uploadFile.vue'
import * as math from 'mathjs'
import Form from '../conservationFee/component/search.vue'
import { searchMapping } from '../conservationFee/component/searchMapping.js'
import { columns } from './tableColumns.js'
import { downloadFileUseALabel } from '@/utils/util.js'
export default {
  name: 'ChargeBill',
  data() {
    return {
      moment: moment,
      abolishReason: '',
      abolishBool: false,
      conservationFeeBool: false,
      printOrderHeaderBool: false,
      fileImportBool: false,
      activeKey: ['studentInfo', 'orderInfo', 'paymentInfo', 'remark', 'deleteInfo'],
      fileList: [], // 上传文件列表
      msg: '这里是订单管理-代收费界面',
      searchObj: {}, //查询对象
      tableLoading: false, //表格加载loading
      columns: columns, //表格头
      tableData: [], //表格数据
      pagination: {
        //分页对象
        pageSizeOptions: ['10', '20', '40', '80', '100'],
        pageSize: 10,
        current: 1,
        total: 0
      },
      rowWatchObj: {}, //表格行对象
      rowWatchVisible: false, //订单明细弹出框是否展示
      orderDetailLoading: false, //订单明细加载loading
      paymentVisible: false, //付款弹出框是否展示
      paymentArray: [
        //付款方式数组
        {
          type: 'weixin', //类型
          name: '微信', //名称
          checked: false, //是否选择
          quota: '', //输入金额
          paymentMode: 1
        },
        {
          type: 'zhifubao',
          name: '支付宝',
          checked: false,
          quota: '',
          paymentMode: 2
        },
        {
          type: 'xianjin',
          name: '现金',
          checked: false,
          quota: '',
          paymentMode: 3
        },
        {
          type: 'shuaka',
          name: '刷卡',
          checked: false,
          quota: '',
          paymentMode: 4
        },
        {
          type: 'yue',
          name: '余额',
          checked: false,
          quota: '',
          paymentMode: 5
        }
      ],
      chargedAmountSum: '',
      importedCount: '',
      posDisabled: true, //pos机号 是否禁用
      posNumber: '', //输入的pos金额
      paymentTotalOrder: 0 //订单总额
    }
  },
  components: {
    Form,
    UploadFile
  },
  mounted() {
    this.$refs.searchForm.submitFn()
  },
  methods: {
    handleClickPrint(rows) {
      this.printOrderHeaderBool = false
      this.conservationFeeBool = true
      this.watch(rows, false)
    },
    printCancel() {
      this.conservationFeeBool = false
    },
    printOk() {
      this.conservationFeeBool = false
      this.printOrderHeaderBool = true
      this.$nextTick(() => {
        Print('#printOrder', {
          onStart: () => {
            console.log('onStart', new Date())
          },
          onEnd: () => {
            console.log('onEnd', new Date())
          }
        })
      })
    },
    getTableData() {
      this.tableLoading = true
      let { getTableData } = this.$api.conservationFeeAjax
      getTableData({
        filter: { ...this.searchObj, type: 3 },
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(res => {
          let { data } = res
          this.tableData = data.records
          this.pagination.current = data.pageNum
          this.pagination.pageSize = data.pageSize
          this.pagination.total = data.total
        })
        .catch(err => {})
        .finally(() => {
          this.tableLoading = false
        })
    },
    submitFn(data) {
      this.searchObj = searchMapping(data)
      this.pagination.current = 1
      this.getTableData()
    },
    exportData() {
      // 导出数据
      let { exportDataAjax } = this.$api.conservationFeeAjax
      exportDataAjax({
        filter: { ...this.searchObj, type: 3 },
        type: 'DSF'
      })
        .then(res => {
          if (res.type === 'application/vnd.ms-excel') {
            let objectUrl = URL.createObjectURL(res)
            this.$message.success('导出成功')
            downloadFileUseALabel('代收费订单.xlsx', objectUrl)
          } else {
            this.$message.warning('导出失败')
          }
        })
        .catch(err => {})
    },
    exportTemplate() {
      // 导出模板
      let { exportTemplateAjax } = this.$api.conservationFeeAjax
      exportTemplateAjax({
        filter: { ...this.searchObj, orderType: 3 },
        type: 'DSF'
      })
        .then(res => {
          if (res.type === 'text/xml') {
            let objectUrl = URL.createObjectURL(res)
            this.$message.success('导出成功')
            downloadFileUseALabel('代收费订单导入模板.xlsx', objectUrl)
          } else {
            this.$message.warning('导出失败...')
          }
        })
        .catch(err => {})
    },
    fileImportOk() {
      let { importAjax } = this.$api.conservationFeeAjax
      importAjax(
        {
          uri: this.fileList[0].fileUrl.split('/').find(item => {
            return item.includes('.xls')
          }),
          importFlag: 1,
          // uri: '7453f4cd-3e5b-4079-93d1-8ec4ad754e3c.xlsx',
          fileName: this.fileList[0].name
        },
        'DSF_ORDER'
      )
        .then(res => {
          if (res.type === 'application/vnd.ms-excel') {
            this.$message.warning('导入失败，错误信息请查看下载文件!')
            let objectUrl = URL.createObjectURL(res)
            // this.$message.success('导出成功')
            downloadFileUseALabel(this.fileList[0].name.split('.')[0] + '.xlsx', objectUrl)
          } else {
            res.text().then(res => {
              if (JSON.parse(res).resultCode === '0') {
                this.fileImportBool = false
              } else {
                this.$message.warning(
                  JSON.parse(res)?.data?.errorMessage ||
                    JSON.parse(res).resultMsg ||
                    '导入文件失败，请导出文件查看原因！'
                )
              }
            })
          }
        })
        .catch(err => {})
        .finally(() => {
          this.fileList = []
          this.getTableData()
        })
    },
    fileImportCancel() {
      this.fileImportBool = false
      this.fileList = []
      this.getTableData()
    },
    handleClickImport() {
      let { importAjax } = this.$api.conservationFeeAjax
      importAjax(
        {
          uri: this.fileList[0].fileUrl.split('/').find(item => {
            return item.includes('.xls')
          }),
          importFlag: 0,
          // uri: '7453f4cd-3e5b-4079-93d1-8ec4ad754e3c.xlsx',
          fileName: this.fileList[0].name
        },
        'DSF_ORDER'
      )
        .then(res => {
          if (res.type === 'application/vnd.ms-excel') {
            this.$message.warning('导入失败，错误信息请查看下载文件!')
            let objectUrl = URL.createObjectURL(res)
            // this.$message.success('导出成功')
            downloadFileUseALabel(this.fileList[0].name.split('.')[0] + '.xlsx', objectUrl)
            this.fileList = []
          } else {
            res.text().then(res => {
              if (JSON.parse(res).resultCode === '0') {
                // this.$message.success(
                //   `文件导入成功;本次导入excel条数为${JSON.parse(res).data.importedCount};本次导入汇总金额为${
                //     JSON.parse(res).data.chargedAmountSum
                //   }`
                // )
                this.importedCount = JSON.parse(res).data.importedCount
                this.chargedAmountSum = JSON.parse(res).data.chargedAmountSum
                this.fileImportBool = true
              } else {
                this.$message.warning(
                  JSON.parse(res)?.data?.errorMessage ||
                    JSON.parse(res).resultMsg ||
                    '导入文件失败，请导出文件查看原因！'
                )
                this.fileList = []
              }
            })
          }
        })
        .catch(err => {})
        .finally(() => {
          this.getTableData()
        })
    },
    updateFileList(param) {
      this.fileList = param
      this.handleClickImport()
      console.log('this.fileList', this.fileList)
    },
    watch(rows, bool = true) {
      //查看按钮触发事件
      if (bool) {
        this.rowWatchVisible = true
        this.orderDetailLoading = true
      }
      let { getOrderDetails } = this.$api.conservationFeeAjax
      getOrderDetails(rows.id)
        .then(res => {
          this.rowWatchObj = res.data
          this.rowWatchObj.paymentDetailsStr = res.data.paymentDetails
            .map(item => {
              return item.paymentModeName + '：' + item.payAmount + '元'
            })
            .join('；')
        })
        .catch(err => {})
        .finally(fin => {
          if (bool) {
            this.orderDetailLoading = false
          }
        })
    },
    handleClickAbolish(rows) {
      this.rowWatchObj = rows
      this.abolishBool = true
    },
    abolishOk() {
      if (this.abolishReason.length === 0) {
        this.$message.warning('请输入作废原因')
      } else {
        this.abolish(this.rowWatchObj)
      }
    },
    abolishCancel() {
      this.abolishReason = ''
      this.abolishBool = false
    },
    abolish(rows) {
      let { abolish } = this.$api.conservationFeeAjax
      abolish({
        id: rows.id,
        deleteReason: this.abolishReason
      })
        .then(res => {
          this.$message.success(res.resultMsg || '操作成功')
        })
        .catch(err => {})
        .finally(fin => {
          this.abolishCancel()
          this.getTableData()
        })
    },
    payment(rows) {
      this.rowWatchObj = rows
      this.paymentTotalOrder = Number(math.bignumber(rows.chargeAmount))
      this.posDisabled = true
      this.posNumber = ''
      this.paymentArray.forEach(item => {
        item.checked = false
        item.quota = ''
      })
      this.paymentVisible = true
    },
    switchCheckbox(e, type) {
      // if (type === 'shuaka' && e.target.checked === true) {
      //   this.posDisabled = false
      // }
      // if (type === 'shuaka' && e.target.checked === false) {
      //   this.posDisabled = true
      //   this.posNumber = ''
      // }

      let findObj = this.paymentArray.find(item => {
        return item.type == type
      })
      if (e.target.checked === true) {
        findObj.quota = 0
      } else {
        findObj.quota = ''
      }
      findObj.checked = e.target.checked

      let filterArr = this.paymentArray.filter(item => {
        return ['shuaka', 'zhifubao', 'weixin'].includes(item.type)
      })
      let bool = filterArr.every(item => {
        return item.checked === false
      })
      if (bool) {
        this.posDisabled = true
        this.posNumber = ''
      } else {
        this.posDisabled = false
      }
    },
    handleOkOfPayment() {
      let filterCheckedArr = this.paymentArray.filter(item => {
        return item.checked == true && Number(math.bignumber(item.quota)) > 0
      })
      console.log('filterCheckedArr', filterCheckedArr)
      let allPayment = filterCheckedArr.reduce((total, item) => {
        return Number(math.add(math.bignumber(total), math.bignumber(item.quota)))
      }, 0)
      console.log('allPayment', allPayment)
      if (filterCheckedArr.length === 0) {
        this.$message.warning('请选择付款方式/您选择的付款方式金额不能为0')
      } else if (allPayment !== this.paymentTotalOrder) {
        this.$message.warning(`录入的汇总付款额度应为${this.paymentTotalOrder}元`)
      } else if (this.posDisabled == false && this.posNumber === '') {
        this.$message.warning(`请输入pos机号`)
      } else {
        let paramsObj = {}
        paramsObj.id = this.rowWatchObj.id
        paramsObj.paymentDetails = filterCheckedArr.map(item => {
          let obj = {
            paymentMode: item.paymentMode,
            payAmount: item.quota
          }
          if (item.type === 'shuaka' || item.type === 'weixin' || item.type === 'zhifubao') {
            obj.pos = this.posNumber
          }
          return obj
        })
        let { paymentAjax } = this.$api.conservationFeeAjax
        paymentAjax(paramsObj)
          .then(res => {
            this.$message.success(res.resultMsg || '操作成功')
          })
          .catch(err => {})
          .finally(fin => {
            this.handleCancelOfPayment()
            this.getTableData()
          })
      }
    },
    handleCancelOfPayment() {
      this.paymentVisible = false
      this.posDisabled = true
      this.posNumber = ''
      this.paymentArray.forEach(item => {
        item.checked = false
        item.quota = ''
      })
    },
    onShowSizeChange(current, pageSize) {
      this.pagination.current = 1
      this.pagination.pageSize = pageSize
      this.getTableData()
    },
    onPaginationChange(pageNo) {
      this.pagination.current = pageNo
      this.getTableData()
    }
  }
}
</script>
<style lang="less" scoped>
.conservation-fee {
  .row-watch-wrap {
    background-color: red;
    li {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
