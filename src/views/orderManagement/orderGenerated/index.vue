<template>
  <div class="order-generated">
    <div class="wrap">
      <div class="header">
        <h2>生成月结及订单</h2>
        <div class="date-day-time">
          <div class="time">{{ time }}</div>
          <div class="date-day">
            <div class="date">{{ date }}</div>
            <div class="day" style="margin-left: 6px">{{ day }}</div>
          </div>
        </div>
      </div>
      <div class="body">
        <div class="body-header"></div>
        <div class="rows">
          <span>月结</span>
          <div>
            <a-popconfirm title="确认生成保育费月结?" @confirm="generateBaoYuFeiYueJie">
              <a-button v-permission="['baoyufeiyuejie']" type="primary"> 生成保育费月结 </a-button>
            </a-popconfirm>
            <a-popconfirm title="确认生成餐费月结?" @confirm="generateCanFeiYueJie">
              <a-button v-permission="['canfeiyuejie']" type="default" style="margin-left: 12px">
                生成餐费月结
              </a-button>
            </a-popconfirm>
          </div>
        </div>
        <div class="rows">
          <span>订单</span>
          <div>
            <a-popconfirm title="确认生成保育费订单（确保完成月结）?" @confirm="generateBaoYuFei">
              <a-button v-permission="['baoyufei']" type="primary"> 生成保育费订单 </a-button>
            </a-popconfirm>
            <a-popconfirm title="确认生成餐费订单（确保完成月结）?" @confirm="generateCanFei">
              <a-button v-permission="['canfei']" type="default" style="margin-left: 12px"> 生成餐费订单 </a-button>
            </a-popconfirm>
          </div>
        </div>
        <img src="../../../assets/order-generated/svg.png" alt="" />
      </div>
    </div>
    <!-- <a-popconfirm title="确认生成保育费月结?" @confirm="generateBaoYuFeiYueJie">
      <a-button v-permission="['baoyufeiyuejie']" type="link"> 保育费月结 </a-button>
    </a-popconfirm>
    <a-popconfirm title="确认生成餐费月结?" @confirm="generateCanFeiYueJie">
      <a-button v-permission="['canfeiyuejie']" type="link"> 餐费月结 </a-button>
    </a-popconfirm>
    <a-popconfirm title="确认生成保育费订单（确保完成月结）?" @confirm="generateBaoYuFei">
      <a-button v-permission="['baoyufei']" type="link"> 生成保育费订单 </a-button>
    </a-popconfirm>
    <a-popconfirm title="确认生成餐费订单（确保完成月结）?" @confirm="generateCanFei">
      <a-button v-permission="['canfei']" type="link"> 生成餐费订单 </a-button>
    </a-popconfirm> -->
  </div>
</template>
<script>
import moment from 'moment'
export default {
  data() {
    return {
      msg: '这里是月结和订单生成页面',
      day: moment().format('dddd'),
      time: moment().format('HH:mm'),
      date: `${moment().format('M')}月${moment().format('D')}日`
    }
  },
  computed: {},
  mounted() {
    this.getTime()
    this.getDate()
    this.getDay()
  },
  methods: {
    getDay() {
      setInterval(() => {
        this.day = moment().format('dddd')
      }, 1000)
    },
    getDate() {
      setInterval(() => {
        this.date = `${moment().format('M')}月${moment().format('D')}日`
      }, 1000)
    },
    getTime() {
      setInterval(() => {
        this.time = moment().format('HH:mm')
      }, 1000)
    },
    generateBaoYuFei() {
      this.$api.orderGeneratedAjax
        .shengChengBaoYueFeiDingDanAjax({
          studentIds: '',
          dateStr: ''
        })
        .then(
          res => {
            this.$message.success(res.resultMsg || '操作成功..')
          },
          rej => {
            console.log(rej)
          }
        )
        .catch(err => {})
    },
    generateCanFei() {
      this.$api.orderGeneratedAjax
        .shengChengCanFeiDingDanAjax({
          studentIds: '',
          dateStr: ''
        })
        .then(
          res => {
            this.$message.success(res.resultMsg || '操作成功..')
          },
          rej => {
            console.log(rej)
          }
        )
        .catch(err => {})
    },
    generateBaoYuFeiYueJie() {
      this.$api.orderGeneratedAjax
        .baoYuFeiYueJieAjax({
          studentIds: '',
          dateStr: ''
        })
        .then(
          res => {
            this.$message.success(res.resultMsg || '操作成功..')
          },
          rej => {
            console.log(rej)
          }
        )
        .catch(err => {})
    },
    generateCanFeiYueJie() {
      this.$api.orderGeneratedAjax
        .canFeiYueJieAjax({
          studentIds: '',
          dateStr: ''
        })
        .then(
          res => {
            this.$message.success(res.resultMsg || '操作成功..')
          },
          rej => {
            console.log(rej)
          }
        )
        .catch(err => {})
    }
  }
}
</script>
<style lang="less" scoped>
.order-generated {
  .wrap {
    width: 100%;
    margin: 0 auto;
    // height: 130px;
    .header {
      // padding-left: 407px;
      // padding-right: 88px;
      width: 100%;
      height: 130px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      // justify-content: space-between;
      // background-image: url(../../../assets/order-generated/bg.png);
      background-color: #20b097;
      h2 {
        font-size: 24px;
        font-weight: 500;
        color: #ffffff;
        line-height: 32px;
      }
      .date-day-time {
        position: absolute;
        right: 88px;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        padding: 12px 0;
        width: 134px;
        height: 93px;
        background: #268482;
        border-radius: 8px;
        text-align: center;
        .time {
          font-size: 30px;
          font-weight: 400;
          color: #ffffff;
          line-height: 28px;
        }
        .date-day {
          font-size: 14px;
          font-weight: 400;
          color: #ffffff;
          line-height: 19px;
          display: flex;
          justify-content: center;
        }
      }
    }
    .body {
      position: relative;
      margin: 0 auto;
      width: 794px;
      height: 423px;
      background-image: url('../../../assets/order-generated/border.png');
      .body-header {
        margin: 0 auto;
        width: 750px;
        height: 52px;
        background-image: url('../../../assets/order-generated/yinyingxian.png');
      }
      & > div.rows {
        height: 101px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e9e9e9;
        width: 710px;
        margin: 0 auto;
        & > span {
          font-size: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          line-height: 24px;
        }
      }
      & > img {
        position: absolute;
        bottom: 50px;
        right: 40px;
      }
    }
  }
}
</style>
