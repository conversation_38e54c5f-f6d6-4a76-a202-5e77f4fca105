import { utils } from '@/components/FormFramework/utils/formUtil.js'
function inputUtils(obj, key) {
  return obj[key] ? obj[key].value : ''
}
function timeUtils(resObj, key, index, type) {
  return resObj[key] ? utils[resObj[key].type].format(resObj[key].value[index], type) : ''
}
export function searchMapping(resObj) {
  return {
    status: inputUtils(resObj, 'orderStatus'), //订单状态
    paymentStatus: inputUtils(resObj, 'payloadStatus'), //支付状态
    paymentMode: inputUtils(resObj, 'paymentMethod'), //付款方式
    studentName: inputUtils(resObj, 'studentName'), //学生姓名
    studentCode: inputUtils(resObj, 'studentID'), //学生学号
    contactInfo: inputUtils(resObj, 'studentContactType'), //学生联系方式
    chargeItem: inputUtils(resObj, 'payService'), //收费项目
    code: inputUtils(resObj, 'orderNumber'), //订单编号
    startPaymentTime: timeUtils(resObj, 'rangePaymentTime', 'begin', 'YYYY-MM-DD'), //付款时间
    endPaymentTime: timeUtils(resObj, 'rangePaymentTime', 'end', 'YYYY-MM-DD'), //付款时间
    startCreateTime: timeUtils(resObj, 'rangeOrderCreateTime', 'begin', 'YYYY-MM-DD'), //订单创建时间
    endCreateTime: timeUtils(resObj, 'rangeOrderCreateTime', 'end', 'YYYY-MM-DD'), //订单创建时间
    startDeleteTime: timeUtils(resObj, 'rangeDeleteTime', 'begin', 'YYYY-MM-DD'), //作废时间
    endDeleteTime: timeUtils(resObj, 'rangeDeleteTime', 'end', 'YYYY-MM-DD') //作废时间
  }
}
