<template>
  <div>
    <a-card>
      <form-create :rule="rule" v-model="fApi" :option="options"> </form-create>
      <div class="searchBtn">
        <a-Button type="primary" plain @click="submitFn">查询</a-Button>
        <a-Button plain @click="reset()">重置</a-Button>
      </div>
    </a-card>
  </div>
</template>

<script>
import { formToggle } from '@/components/FormFramework/utils/formUtil.js'
import { FormContainer, formOptions } from '@/components/FormFramework/index.js'
import { rules } from '@/components/FormFramework/formRules/index.js'
import formCreate from '@form-create/ant-design-vue'
import _ from 'lodash'
import moment from 'moment'
export default {
  name: 'Form',
  data() {
    return {
      fApi: {},
      fc: null,
      options: formOptions,
      rule: []
    }
  },
  created() {
    this.fc = new FormContainer(formCreate, this.fApi)
    let configArr = _.cloneDeep([
      rules.studentName, //学生姓名
      // rules.studentID, //学生学号
      rules.studentContactType, //学生联系方式
      rules.payService, //收费项目
      rules.orderNumber, //订单编号
      rules.orderStatus, //订单状态
      rules.payloadStatus, //支付状态
      rules.paymentMethod, //付款方式
      rules.rangePaymentTime, //付款时间
      rules.rangeOrderCreateTime, //订单创建时间
      rules.rangeDeleteTime //订单作废时间
    ])
    configArr.forEach(item => {
      if (
        item.field === 'rangePaymentTime' ||
        item.field === 'rangeOrderCreateTime' ||
        item.field === 'rangeDeleteTime'
      ) {
        item.col.md = 12
        item.col.sm = 12
        item.col.xs = 12
      } else {
        item.col.md = 8
        item.col.sm = 8
        item.col.xs = 8
      }
    })
    this.fc.setConfigArr(configArr)
    this.rule = this.fc.getFormRule().map(item => {
      if (
        item.field === 'rangePaymentTime' ||
        item.field === 'rangeOrderCreateTime' ||
        item.field === 'rangeDeleteTime'
      ) {
        item.props.showTime = {
          defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')]
        }
      }
      return item
    })
  },
  mounted() {
    this.fc.build(this.fApi)
    this.fc.initDataSource()
  },
  methods: {
    toggleForm() {
      this.toggleStatus = !this.toggleStatus
      formToggle(this.fApi, this.toggleStatus)
    },
    submitFn() {
      this.fc.submitFormat(data => {
        this.$emit('submitFn', data)
      })
    },
    reset() {
      this.fc.reset()
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-form > .ant-row > .ant-col {
  height: 64px;
}
.searchBtn {
  text-align: right;
  .ant-btn {
    margin-left: 10px;
  }
}
</style>
