export const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
    width: 80,
    customRender: (text, record, index) => {
      return `${index + 1}`
    }
  },
  {
    title: '订单编号',
    dataIndex: 'code',
    align: 'center'
  },
  {
    title: '学生姓名',
    dataIndex: 'studentName',
    align: 'center'
  },
  {
    title: '生日',
    dataIndex: 'birthday',
    align: 'center'
  },
  {
    title: '学生联系方式',
    dataIndex: 'contactInfo',
    align: 'center'
  },
  {
    title: '收费项目',
    dataIndex: 'chargeItem',
    align: 'center'
  },
  {
    title: '收费金额',
    dataIndex: 'chargeAmount',
    align: 'center'
  },
  {
    title: '订单创建时间',
    dataIndex: 'createTimeStr',
    width: 200,
    align: 'center'
  },
  {
    title: '付款时间',
    dataIndex: 'paymentTimeStr',
    align: 'center'
  },
  {
    title: '付款方式',
    dataIndex: 'paymentModeName',
    align: 'center'
  },
  {
    title: '支付状态',
    dataIndex: 'paymentStatusName',
    align: 'center'
  },
  {
    title: '订单状态',
    dataIndex: 'statusName',
    align: 'center'
  },
  {
    title: '备注',
    dataIndex: 'remark',
    align: 'center'
  },
  {
    title: '作废原因',
    dataIndex: 'deleteReason',
    align: 'center'
  },
  {
    title: '作废时间',
    dataIndex: 'deleteTimeStr',
    align: 'center'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 260,
    fixed: 'right',
    align: 'center',
    scopedSlots: { customRender: 'operation' }
  }
]
