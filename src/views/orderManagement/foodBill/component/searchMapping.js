import { utils } from '@/components/FormFramework/utils/formUtil.js'
function inputUtils(obj, key) {
  return obj[key] ? obj[key].value : ''
}
function timeUtils(resObj, key, index, type) {
  return resObj[key] ? utils[resObj[key].type].format(resObj[key].value[index], type) : ''
}
export function searchMapping(resObj) {
  return {
    status: inputUtils(resObj, 'orderStatus'), //订单状态
    paymentStatus: inputUtils(resObj, 'payloadStatus'), //支付状态
    paymentMode: inputUtils(resObj, 'paymentMethod'), //付款方式
    studentName: inputUtils(resObj, 'studentName'), //学生姓名
    studentCode: inputUtils(resObj, 'studentID'), //学生学号
    contactInfo: inputUtils(resObj, 'studentContactType'), //学生联系方式
    chargeItem: inputUtils(resObj, 'payService'), //收费项目
    code: inputUtils(resObj, 'orderNumber'), //订单编号
    paymentTime: inputUtils(resObj, 'paymentTime'), //付款时间
    CREATETIME: inputUtils(resObj, 'orderCreateTime') //订单创建时间
  }
}
