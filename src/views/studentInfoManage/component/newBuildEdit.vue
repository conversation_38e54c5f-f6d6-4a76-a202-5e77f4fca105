<template>
  <div>
    <form-create
      @student-i-d-blur="studentIDBlur"
      @identity-number-blur="idSearch"
      :rule="rule"
      v-model="fApi"
      :option="options"
    >
    </form-create>
    <div class="searchBtn" style="visibility: hidden">
      <a-Button type="primary" plain @click="submitFn">查询</a-Button>
      <a-Button plain @click="reset()">重置</a-Button>
    </div>
  </div>
</template>

<script>
import { formToggle } from '@/components/FormFramework/utils/formUtil.js'
import { FormContainer, formOptions } from '@/components/FormFramework/index.js'
import { rules } from '@/components/FormFramework/formRules/index.js'
import formCreate from '@form-create/ant-design-vue'
import _ from 'lodash'
import moment from 'moment'
export default {
  name: 'Form',
  data() {
    return {
      fApi: {},
      fc: null,
      options: formOptions,
      rule: []
    }
  },
  created() {
    this.fc = new FormContainer(formCreate, this.fApi)
    let configArr = _.cloneDeep([
      rules.studentName,
      rules.studentID,
      rules.grade,
      rules.nation,
      rules.identityNumber,
      rules.sex,
      rules.birthdayTime,
      rules.nationality,
      rules.connectName,
      rules.phone,
      rules.relation,
      rules.fatherName,
      rules.fatherPhone,
      rules.motherName,
      rules.motherPhone,
      rules.enterTime,
      rules.businessStatus
    ])
    configArr.forEach(item => {
      item.col.md = 12
      item.col.sm = 12
      item.col.xs = 12
    })
    this.fc.setConfigArr(configArr)
    this.rule = this.fc.getFormRule()
  },
  mounted() {
    this.fc.build(this.fApi)
    this.fc.initDataSource()
    this.initValidate(this.fApi, [
      'studentID',
      'studentName',
      'identityNumber',
      'nationality',
      'grade',
      'nation',
      'phone',
      'connectName',
      'relation',
      'motherPhone',
      'motherName',
      'fatherPhone',
      'fatherName',
      'enterTime',
      'businessStatus',
      'sex',
      'birthdayTime'
    ])
  },
  methods: {
    studentIDBlur(val) {
      let studentID = this.fApi.getValue('studentID')
      console.log(studentID)
      if (studentID !== undefined) {
        let { countByStudentNoUrlAjax } = this.$api.studentInfoManageAjax
        countByStudentNoUrlAjax({
          studentNo: studentID
        }).then(res => {
          if (res.data !== 0) {
            this.$emit('disabledOk', true)
            // 123
            this.$message.warning('该学号已存在！')
          } else {
            this.$emit('disabledOk', false)
          }
        })
      }
    },
    idSearch(val) {
      let ID = this.fApi.getValue('identityNumber')
      var reg =
        /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|30|31)|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/
      var r = ID.match(reg)
      let birthday = `${ID.substring(ID.length - 8, ID.length - 12)}-${ID.substring(
        ID.length - 6,
        ID.length - 8
      )}-${ID.substring(ID.length - 4, ID.length - 6)}`
      console.log(birthday)
      if (r == null) {
        console.log('格式不正确')
      } else {
        this.fApi.setValue({
          sex: ID.substring(ID.length - 2, ID.length - 1) % 2 == 0 ? '2' : '1',
          birthdayTime: birthday
        })
      }
    },
    initValidate(context, fieldArr) {
      for (let i in fieldArr) {
        let item = context.rule.find(item => item.field == fieldArr[i])
        if (item.customFiled && item.customFiled.validate) {
          let validate = item.customFiled.validate
          context.set(item, 'validate', validate)
        }
      }
    },
    toggleForm() {
      this.toggleStatus = !this.toggleStatus
      formToggle(this.fApi, this.toggleStatus)
    },
    submitFn() {
      this.fc.submitFormat(data => {
        this.$emit('submitFn', data)
      })
    },
    reset() {
      this.fc.reset()
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-form > .ant-row > .ant-col {
  height: 64px;
}
.searchBtn {
  text-align: right;
  .ant-btn {
    margin-left: 10px;
  }
}
</style>
