<template>
  <!-- hidden PageHeaderWrapper title demo -->
  <page-header-wrapper :title="false" :content="$t('form.basic-form.basic.description')">
    <a-card :body-style="{ padding: '24px 32px' }" :bordered="false">
      <fm-generate-antd-form
        :data="jsonData"
        :remote="remoteFuncs"
        :value="editData"
        :remote-option="dynamicData"
        ref="generateForm"
      >
      </fm-generate-antd-form>
      <div style="text-align: center">
        <a-button type="primary" @click="handleSubmit">提交</a-button>
        <a-button style="margin-left: 8px" @click="handleCancel">重置</a-button>
      </div>
    </a-card>
  </page-header-wrapper>
</template>

<script>
export default {
  name: 'BaseForm',
  data() {
    return {
      jsonData: {
        list: [
          {
            type: 'input',
            icon: 'icon-input',
            options: {
              width: '100%',
              defaultValue: '',
              required: true,
              requiredMessage: '请输入折扣！',
              dataType: '',
              dataTypeCheck: false,
              dataTypeMessage: '',
              pattern: '/^([1-9]|10)$/',
              patternCheck: true,
              patternMessage: '请输入1-10的数字！',
              placeholder: '请输入折扣',
              customClass: '',
              disabled: false,
              labelWidth: 100,
              isLabelWidth: false,
              hidden: false,
              dataBind: true,
              showPassword: false,
              remoteFunc: 'func_1618811422053',
              remoteOption: 'option_1618811422053'
            },
            name: '折扣',
            key: '1618811422053',
            model: 'discount',
            rules: [
              { required: true, message: '请输入折扣！' },
              { pattern: '/^([1-9]|10)$/', message: '请输入1-10的数字！' }
            ]
          },
          {
            type: 'input',
            icon: 'icon-input',
            options: {
              width: '100%',
              defaultValue: '',
              required: true,
              requiredMessage: '请输入充值金额！',
              dataType: '',
              dataTypeCheck: false,
              dataTypeMessage: '',
              pattern: '/^0\\.([1-9]|\\d[1-9])$|^[1-9]\\d{0,}\\.\\d{0,2}$|^[1-9]\\d{0,}$/',
              patternCheck: true,
              patternMessage: '请输入最多2位小数！',
              placeholder: '请输入充值金额',
              customClass: '',
              disabled: false,
              labelWidth: 100,
              isLabelWidth: false,
              hidden: false,
              dataBind: true,
              showPassword: false,
              remoteFunc: 'func_1618812143381',
              remoteOption: 'option_1618812143381'
            },
            name: '充值金额',
            key: '1618812143381',
            model: 'price',
            rules: [
              { required: true, message: '请输入充值金额！' },
              {
                pattern: '/^0\\.([1-9]|\\d[1-9])$|^[1-9]\\d{0,}\\.\\d{0,2}$|^[1-9]\\d{0,}$/',
                message: '请输入最多2位小数！'
              }
            ]
          },
          {
            type: 'input',
            icon: 'icon-input',
            options: {
              width: '100%',
              defaultValue: '',
              required: false,
              requiredMessage: '',
              dataType: '',
              dataTypeCheck: false,
              dataTypeMessage: '',
              pattern: '',
              patternCheck: false,
              patternMessage: '',
              placeholder: '请输入赠送积分',
              customClass: '',
              disabled: false,
              labelWidth: 100,
              isLabelWidth: false,
              hidden: false,
              dataBind: true,
              showPassword: false,
              remoteFunc: 'func_1618811425114',
              remoteOption: 'option_1618811425114'
            },
            name: '赠送积分',
            key: '1618811425114',
            model: 'score',
            rules: []
          },
          {
            type: 'textarea',
            icon: 'icon-diy-com-textarea',
            options: {
              width: '100%',
              defaultValue: '',
              required: false,
              requiredMessage: '',
              disabled: false,
              pattern: '',
              patternMessage: '',
              placeholder: '请输入权益说明',
              customClass: '',
              labelWidth: 100,
              isLabelWidth: false,
              hidden: false,
              dataBind: true,
              remoteFunc: 'func_1618811426345',
              remoteOption: 'option_1618811426345'
            },
            name: '权益说明',
            key: '1618811426345',
            model: 'explain',
            rules: []
          }
        ],
        config: {
          labelWidth: 100,
          labelPosition: 'right',
          size: 'small',
          customClass: '',
          ui: 'antd',
          layout: 'horizontal',
          labelCol: 5,
          width: '100%',
          hideLabel: false,
          hideErrorMessage: false,
          dataSource: [
            {
              key: 'upload',
              name: 'Get Upload Token',
              url: 'http://tools-server.making.link/api/uptoken',
              method: 'GET',
              auto: true,
              responseFunc: 'return res.uptoken;'
            },
            {
              key: 'getoptions',
              name: 'Get Options',
              url: 'http://tools-server.making.link/api/new/options',
              method: 'GET',
              auto: true,
              responseFunc: 'return res.data;'
            }
          ]
        }
      },
      editData: {},
      remoteFuncs: {},
      dynamicData: {}
    }
  },
  methods: {
    // handler
    handleSubmit(e) {
      this.$refs.generateForm
        .getData()
        .then(data => {
          this.$message.success('提交成功！')
          console.log(data)
        })
        .catch(e => {})
    },
    handleCancel() {
      this.$refs.generateForm.setData({
        explain: '',
        score: '',
        discount: '',
        price: ''
      })
    }
  }
}
</script>
