<template>
  <!-- hidden PageHeaderWrapper title demo -->
  <page-header-wrapper :title="false" :content="$t('form.basic-form.basic.description')">
    <a-card :body-style="{ padding: '24px 32px' }" :bordered="false">
      <a-form @submit="handleSubmit" :form="form">
        <a-form-item
          :label="$t('form.basic-form.title.label')"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-input
            v-decorator="['name', { rules: [{ required: true, message: $t('form.basic-form.title.required') }] }]"
            name="name"
            :placeholder="$t('form.basic-form.title.placeholder')"
          />
        </a-form-item>
        <a-form-item
          :label="$t('form.basic-form.date.label')"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-range-picker
            name="buildTime"
            style="width: 100%"
            v-decorator="['buildTime', { rules: [{ required: true, message: $t('form.basic-form.date.required') }] }]"
          />
        </a-form-item>
        <a-form-item
          :label="$t('form.basic-form.goal.label')"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-textarea
            rows="4"
            :placeholder="$t('form.basic-form.goal.placeholder')"
            v-decorator="['description', { rules: [{ required: true, message: $t('form.basic-form.goal.required') }] }]"
          />
        </a-form-item>
        <a-form-item
          :label="$t('form.basic-form.standard.label')"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-textarea
            rows="4"
            :placeholder="$t('form.basic-form.standard.placeholder')"
            v-decorator="['type', { rules: [{ required: true, message: $t('form.basic-form.standard.required') }] }]"
          />
        </a-form-item>
        <a-form-item
          :label="$t('form.basic-form.client.label')"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-input
            :placeholder="$t('form.basic-form.client.placeholder')"
            v-decorator="['customer', { rules: [{ required: true, message: $t('form.basic-form.client.required') }] }]"
          />
        </a-form-item>
        <a-form-item
          :label="$t('form.basic-form.invites.label')"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
          :required="false"
        >
          <a-input :placeholder="$t('form.basic-form.invites.placeholder')" />
        </a-form-item>
        <a-form-item
          :label="$t('form.basic-form.weight.label')"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
          :required="false"
        >
          <a-input-number :min="0" :max="100" />
          <span>%</span>
        </a-form-item>
        <a-form-item
          :label="$t('form.basic-form.upload.label')"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
          :required="false"
        >
          <a-upload
            action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
            list-type="picture"
            :default-file-list="fileList"
          >
            <a-button> <a-icon type="upload" /> {{ $t('form.basic-form.upload.label') }} </a-button>
          </a-upload>
        </a-form-item>

        <a-form-item
          :label="'中台' + $t('form.basic-form.upload.label')"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
          :required="false"
        >
          <ts-upload />
        </a-form-item>
        <a-form-item
          :label="$t('form.basic-form.public.label')"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
          :required="false"
          :help="$t('form.basic-form.label.help')"
        >
          <a-radio-group v-decorator="['target', { initialValue: 1 }]">
            <a-radio :value="1">{{ $t('form.basic-form.radio.public') }}</a-radio>
            <a-radio :value="2">{{ $t('form.basic-form.radio.partially-public') }}</a-radio>
            <a-radio :value="3">{{ $t('form.basic-form.radio.private') }}</a-radio>
          </a-radio-group>
          <a-form-item v-show="form.getFieldValue('target') === 2">
            <a-select mode="multiple">
              <a-select-option value="4">{{ $t('form.basic-form.option.A') }}</a-select-option>
              <a-select-option value="5">{{ $t('form.basic-form.option.B') }}</a-select-option>
              <a-select-option value="6">{{ $t('form.basic-form.option.C') }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 24 }" style="text-align: center">
          <a-button html-type="submit" type="primary">{{ $t('form.basic-form.form.submit') }}</a-button>
          <a-button style="margin-left: 8px">{{ $t('form.basic-form.form.save') }}</a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import TsUpload from '@/components/TsUpload/Upload.vue'
export default {
  name: 'BaseForm',
  components: {
    TsUpload
  },
  data() {
    return {
      form: this.$form.createForm(this),
      fileList: []
    }
  },
  methods: {
    // handler
    handleSubmit(e) {
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log('Received values of form: ', values)
        }
      })
    }
  }
}
</script>
