<template>
  <div class="clearfix">
    <a-upload
      :accept="accept"
      action="#"
      list-type="text"
      :class="editable ? '' : 'upload-readonly'"
      :file-list="list"
      :before-upload="beforeUpload"
      :remove="handleRemove"
      :show-upload-list="showUploadList"
      v-decorator="[
        'upload',
        {
          valuePropName: 'list',
          getValueFromEvent: normFile,
          rules: [{ required: true, message: '文件不能为空' }]
        }
      ]"
    >
      <a-button v-if="list.length < this.maxNum && editable" :disabled="upLoading">
        <a-icon :type="upLoading ? 'loading' : 'upload'" />
        {{ description }}
      </a-button>
    </a-upload>
    <div></div>
    <div v-if="list.length == 0 && !editable" style="line-height: 0">无</div>
  </div>
</template>
<script>
const { v4: uuidv4 } = require('uuid')
import { getSignature, getSignatureDownLoad, postUpload } from '@/request/api/upload.js'
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}
export default {
  name: 'UploadFile',
  data() {
    return {
      accept: '.xls, .xlsx',
      list: [], // 组件内使用文件列表
      upLoading: false,
      signature: {} // 签名信息
    }
  },
  props: {
    maxNum: {
      required: true,
      type: Number
    },
    showUploadList: {
      // required: true,
      type: Boolean,
      default: () => {
        return true
      }
    },
    fileList: {
      type: Array,
      default: () => {
        return []
      }
    },
    isShow: {
      // true--默认为大方格样式上传文件  false--为按钮形式
      type: Boolean,
      default: true
    },
    limit: {
      type: Number,
      default: 0
    },
    // 上传的提示文字信息
    description: {
      type: String,
      default: 'Upload'
    },
    demoUrlList: {
      type: Array,
      default: () => []
    },
    // 组件绑定值是对象形式还是url形式，默认为对象形式
    // full: {
    //   type: Boolean,
    //   default: true
    // },
    // 区分公桶还是私桶
    bucketKey: {
      type: String,
      // default: 'middle-platform-public-test'
      default: window.privateConfig.PREVIEW
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  created() {
    this.getSignature()
  },
  watch: {
    fileList: {
      handler(newVal) {
        this.list = [] // if (this.full && newVal.length) {
        if (newVal.length) {
          newVal.forEach((item, index) => {
            let obj = {
              uid: index + 1,
              name: item.name,
              url: item.picUrl
            }
            this.list.push(obj)
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取签名
    async getSignature() {
      let res = await getSignature({
        bucketKey: `${this.bucketKey}`
      })
      if (res.resultCode === '0') {
        this.signature = res.data
      } else {
        this.$message.error(res.resultMsg)
      }
    },
    // 删除文件
    handleRemove(file) {
      const index = this.list.indexOf(file)
      const newFileList = this.list.slice()
      newFileList.splice(index, 1)
      this.list = newFileList
      // 文件地址
      let fileArr = []
      this.list.forEach(item => {
        fileArr.push({
          fileUrl: item.url,
          name: item.name
        })
      })
      this.$emit('removeUploadList', fileArr)
      return true
    },
    // 获取OSS文件临时链接
    getSignatureUrl(url) {
      console.log('getSignatureUrl', url)
      let requestUrl = '/' + url.split('/').splice(1).join('/')
      return new Promise((resolve, reject) => {
        getSignatureDownLoad(this.api, requestUrl)
          .then(res => {
            if (res.resultCode === '0') {
              resolve(res.data)
            } else {
              this.$message.error(res.resultMsg)
              reject(res.resultMsg)
            }
          })
          .catch(err => {
            console.log(err)
            reject(err)
          })
      })
    },

    // 上传前调
    beforeUpload(files) {
      this.upLoading = true
      // if (this.list.length >= this.maxNum) {
      //   this.$message.error('文件大小不能超过' + this.maxNum + '张')
      //   return false
      // }
      // 上传文件的格式拦截
      let nameArray = files.name.split('.')
      // 文件名后缀
      let extName = nameArray[nameArray.length - 1]
      // 校验是否存在同名文件
      let repeat = false
      this.list.forEach(res => {
        if (res.name === files.name) {
          repeat = true
        }
      })
      // if (this.full && repeat) {
      if (repeat) {
        // 只有绑定值为对象形式才校验文件名重复
        this.$message.error('不能上传同名的文件！')
        return
      }
      // const loading = this.$loading({
      //   text: '上传中...'
      // })
      // 拷贝一份signature信息，避免当前操作影响下次上传
      let signatureCache = { ...this.signature }
      console.log('signatureCache', signatureCache)
      let formData = new FormData()
      let formDataArr = Object.keys(signatureCache)
      let url = ''
      let appKey = ''
      // 生成OSS上存储的文件名
      let randomFilename = `${uuidv4()}.${extName}`
      if (signatureCache['key']) {
        signatureCache['key'] = `${signatureCache['key']}${randomFilename}`
      }
      if (signatureCache['Content-Type']) {
        formData.append('Content-Type', files.type)
      }
      formDataArr.forEach(item => {
        if (item !== 'form-action') {
          formData.append(item, signatureCache[item])
        } else {
          appKey = signatureCache[item].match(/https:\/\/(\S*)\.i\.tasly/)[1]
          url = signatureCache[item]
        }
      })
      formData.append('file', files)
      postUpload(formData, url).then(res => {
        this.upLoading = true
        console.log('postUpload 开始 ')
        // // loading.close()
        let filesObj = {
          uid: this.list.length + 1,
          name: files.name,
          url: `https://api.i.tasly.com/swift/v1/${appKey}/${this.bucketKey}/${signatureCache['key']}`
        }
        if (filesObj.name.endsWith('.xlsx') || filesObj.name.endsWith('.xls')) {
          this.list.push(filesObj)
        } else {
          this.$message.warning('只接受.xlsx、.xls类型的文件！')
          this.upLoading = false
          return false
        }
        console.log('postUpload 结束 ', this.list)
        this.upLoading = false
        let arr = []
        this.list.forEach(item => {
          arr.push({
            fileUrl: item.url,
            name: item.name
          })
        })
        // 文件地址
        let fileArr = []
        this.list.forEach(item => {
          fileArr.push({
            fileUrl: item.url,
            name: item.name
          })
        })
        this.$emit('updateUploadList', fileArr)
      })
      return false
    },
    normFile(e) {
      // return e && e.fileList
    }
  }
}
</script>
<style></style>
