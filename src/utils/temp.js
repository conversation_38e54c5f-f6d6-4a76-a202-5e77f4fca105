import axios from 'axios'

if (!window.privateConfig) {
  window.privateConfig = {
    BACKEND_BASE: process.env.VUE_APP_BACKEND_BASE,
    FRONT_END: process.env.VUE_APP_FRONT_END,
    PREVIEW: process.env.VUE_APP_PREVIEW,
    B2B_BACKEND_BASE: process.env.VUE_APP_B2B_BACKEND_BASE
  }
}

// 创建 axios 实例
const request = axios.create({
  // API 请求的默认前缀
  // baseURL: window.privateConfig.BACKEND_BASE,
  baseURL: '',
  timeout: 10000
})

// 异常拦截处理器
const errorHandler = error => {
  return Promise.reject(error)
}

// request interceptor
request.interceptors.request.use(config => {
  return config
}, errorHandler)

// response interceptor
request.interceptors.response.use(response => {
  return response.data
}, errorHandler)

export default request
