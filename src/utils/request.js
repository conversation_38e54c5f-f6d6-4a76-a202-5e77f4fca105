import axios from 'axios'
import store from '@/store'
import storage from 'store'
import notification from 'ant-design-vue/es/notification'
import { VueAxios } from './axios'
import { getToken } from '@/utils/auth'
import { ACCESS_TOKEN } from '@/store/mutation-types'
if (!window.privateConfig) {
  window.privateConfig = {
    BACKEND_BASE: process.env.VUE_APP_BACKEND_BASE,
    FRONT_END: process.env.VUE_APP_FRONT_END,
    PREVIEW: process.env.VUE_APP_PREVIEW,
    B2B_BACKEND_BASE: process.env.VUE_APP_B2B_BACKEND_BASE,
    VUE_APP_B2B_BACKEND_UPLOAD_IMG: process.env.VUE_APP_B2B_BACKEND_UPLOAD_IMG,
    APP_KEY: process.env.VUE_APP_KEY
  }
}

// 创建 axios 实例
const request = axios.create({
  // API 请求的默认前缀
  baseURL: window.privateConfig.BACKEND_BASE,
  timeout: 10000 // 请求超时时间
})

// 异常拦截处理器
const errorHandler = error => {
  if (error.response) {
    const data = error.response.data
    // 从 localstorage 获取 token
    const token = storage.get(ACCESS_TOKEN)
    if (error.response.status === 403) {
      notification.error({
        message: 'Forbidden',
        description: data.message
      })
    }
    if (error.response.status === 401 && !(data.result && data.result.isLogin)) {
      notification.error({
        message: '提示信息',
        description: data.resultMsg || data.errMsg || 'Authorization verification failed'
      })
      if (token) {
        store.dispatch('Logout').then(() => {
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        })
      }
    }
  }
  return Promise.reject(error)
}

// request interceptor
request.interceptors.request.use(config => {
  // if (config.url.split('/')[1] == 'cathayfuture') {
  //   config.baseURL = process.env.VUE_APP_BACKEND_BASE
  // } else {
  // java开发本地联调
  // config.baseURL = process.env.VUE_APP_BACKEND_CENTER
  config.baseURL = process.env.VUE_APP_BACKEND_BASE
  // }
  const token = getToken()
  // 如果 token 存在
  // 让每个请求携带自定义 token 请根据实际情况自行修改
  if (token && !config.notAccessToken) {
    config.headers['Access-Token'] = token
  }
  config.headers['Application-Key'] = window.privateConfig.APP_KEY
  return config
}, errorHandler)

// response interceptor
request.interceptors.response.use(response => {
  return response.data
}, errorHandler)

const installer = {
  vm: {},
  install(Vue) {
    Vue.use(VueAxios, request)
  }
}

export default request

export { installer as VueAxios, request as axios }
