// with polyfills
import 'core-js/stable'
import 'regenerator-runtime/runtime'

import Vue from 'vue'
import VueBus from './common/EventBus'
import App from './App.vue'
import router from './router'
import store from './store/'
import i18n from './locales'
import { VueAxios } from './utils/request'
import ProLayout, { PageHeaderWrapper } from '@ant-design-vue/pro-layout'
import themePluginConfig from '../config/themePluginConfig'
// import FormMaking from './components/form-making-advanced'
// import './components/form-making-advanced/dist/FormMaking.css'

import _ from 'lodash'
Vue.prototype._ = _
// mock
// WARNING: `mockjs` NOT SUPPORT `IE` PLEASE DO NOT USE IN `production` ENV.
// import './mock'
import '@/core/directives/debounce.js'
import request from '@/utils/temp.js'
import Uploadimg from 't-ui/dist/components/uploadImg' // 测试构建产物
import Uploadfile from 't-ui/dist/components/uploadFile' // 测试构建产物
import 't-ui/dist/components/uploadImg/index.css' // 组件样式
import 't-ui/dist/assets/iconfont.css' // 引入打包后的 iconfont css
console.log('Uploadimg', Uploadimg)
Vue.use(Uploadimg, request)
Vue.use(Uploadfile, request)

import bootstrap from './core/bootstrap'
import './core/lazy_use' // use lazy load components
import './permission' // permission control
import './utils/filter' // global filter
import './global.less' // global style
import '@/core/directives/permission.js'
import '@/core/directives/debounce.js'

// 权限组件
import Authorized from './components/Authorized'
import '@/request/http.js'
import api from './request/api/index.js'
Vue.prototype.$api = api
import './request/http.js'
import { FormModel } from 'ant-design-vue'
// import 'ant-design-vue/dist/antd.css';
Vue.use(FormModel)
import formCreate from '@form-create/ant-design-vue'
Vue.use(formCreate)

Vue.config.productionTip = false

// mount axios to `Vue.$http` and `this.$http`
Vue.use(VueAxios)
Vue.use(VueBus)
// Vue.use(FormMaking, { lang: 'zh-CN', i18n })
// use pro-layout components
Vue.component('pro-layout', ProLayout)
Vue.component('page-container', PageHeaderWrapper)
Vue.component('page-header-wrapper', PageHeaderWrapper)
Vue.component('Authorized', Authorized)
// 123
window.umi_plugin_ant_themeVar = themePluginConfig.theme
console.log('env-添加缓存配置项-version0.01', process.env.VUE_APP_BACKEND_BASE)
new Vue({
  router,
  store,
  i18n,
  // init localstorage, vuex
  created: bootstrap,
  render: h => h(App)
}).$mount('#app')
