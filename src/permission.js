import router from './router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import '@/components/NProgress/nprogress.less' // progress bar custom style
import notification from 'ant-design-vue/es/notification'
import { setDocumentTitle, domTitle } from '@/utils/domUtil'
import { i18nRender } from '@/locales'
import { getToken, resetLocalStorageInfo } from '@/utils/auth'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const allowList = ['login', 'register', 'registerResult'] // no redirect allowList
const loginRoutePath = '/user/login'
const defaultRoutePath = '/home'

router.beforeEach(async (to, from, next) => {
  NProgress.start() // start progress bar
  to.meta && typeof to.meta.title !== 'undefined' && setDocumentTitle(`${i18nRender(to.meta.title)} - ${domTitle}`)
  /* has token */
  if (getToken()) {
    if (to.path === loginRoutePath) {
      next({ path: defaultRoutePath })
      NProgress.done()
    } else {
      // check login user.roles is null
      if (store.getters.roles.length === 0) {
        // 获取路由、按钮权限
        try {
          const info = await store.dispatch('user/GetInfo')
          const { accessedRoutes, btnList, msg } = info
          if (msg !== 'error') {
            localStorage.setItem('resourcesError', 'false')
            // 根据路由和按钮权限生成菜单
            await store.dispatch('GenerateRoutes', { accessedRoutes, btnList })
            router.addRoutes(store.getters.addRouters)
            // 请求带有 redirect 重定向时，登录自动重定向到该地址
            const redirect = decodeURIComponent(from.query.redirect || to.path)
            if (to.path === redirect) {
              // set the replace: true so the navigation will not leave a history record
              next({ ...to, replace: true })
            } else {
              // 跳转到目的路由
              next({ path: redirect })
            }
          } else {
            notification.error({
              message: '错误',
              description: '请求用户信息失败，请重试'
            })
            // 失败时，获取用户信息失败时，调用登出，来清空历史保留信息
            store.dispatch('user/Logout').then(() => {
              next({ path: loginRoutePath, query: { redirect: to.fullPath } })
            })
          }
        } catch (err) {
          console.log('err', err)
          if (err.code && err.code === -1) {
            resetLocalStorageInfo()
            localStorage.setItem('resourcesError', 'true')
            notification.error({
              message: '错误',
              description: err.msg
            })
          }
          store.dispatch('user/ResetInfo')
          // store.dispatch('user/ResetInfo')
          next({ path: defaultRoutePath })
          NProgress.done()
        }
      } else {
        next()
      }
    }
  } else {
    if (allowList.includes(to.name)) {
      // 在免登录名单，直接进入
      next()
    } else {
      next({ path: loginRoutePath, query: { redirect: to.fullPath } })
      NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
    }
  }
})

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
