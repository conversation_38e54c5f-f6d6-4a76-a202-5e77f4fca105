// mock-订单管理-保育费订单
import Mock from 'mockjs2'
/**
 * builder组装返回值
 * getQueryParameters获取参数
 */
Mock.mock(/\/mock\/getPayloadType/, 'get', options => {
  return {
    data: [
      { code: '', name: '全部' },
      { code: '0', name: '线上-微信' },
      { code: '1', name: '线下-微信' },
      { code: '2', name: '支付宝' },
      { code: '3', name: '现金' },
      { code: '4', name: '银行卡' },
      { code: '5', name: '余额' }
    ],
    resultCode: '0',
    resultMsg: 'success'
  }
})
