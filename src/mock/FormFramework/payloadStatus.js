// mock-订单管理-保育费订单
import Mock from 'mockjs2'
/**
 * builder组装返回值
 * getQueryParameters获取参数
 */
Mock.mock(/\/mock\/getPayloadStatusTreeOptions/, 'get', options => {
  return {
    data: [
      { code: '', name: '全部' },
      { code: '0', name: '未支付' },
      { code: '1', name: '已支付' }
    ],
    resultCode: '0',
    resultMsg: 'success'
  }
})

Mock.mock(/\/mock\/getPayloadStatusFourOptions/, 'get', options => {
  return {
    data: [
      { code: '', name: '全部' },
      { code: '0', name: '未支付' },
      { code: '1', name: '已支付' },
      { code: '2', name: '已退款' }
    ],
    resultCode: '0',
    resultMsg: 'success'
  }
})
