import { isIE } from '@/utils/util'

// 判断环境不是 prod 或者 preview 是 true 时，加载 mock 服务
if (process.env.NODE_ENV !== 'production' || process.env.VUE_APP_PREVIEW === 'true') {
  if (isIE()) {
    console.error('[antd-pro] ERROR: `mockjs` NOT SUPPORT `IE` PLEASE DO NOT USE IN `production` ENV.')
  }
  // 使用同步加载依赖
  // 防止 vuex 中的 GetInfo 早于 mock 运行，导致无法 mock 请求返回结果
  console.log('[antd-pro] mock mounting')
  const Mock = require('mockjs2')
  require('./services/auth')
  require('./services/user')
  require('./services/manage')
  require('./services/other')
  require('./services/tagCloud')
  require('./services/article')
  require('./orderManagement/conservationFee.js')
  require('./FormFramework/orderStatus.js')
  require('./FormFramework/payloadStatus.js')
  require('./FormFramework/payloadType.js')
  require('./FormFramework/sex.js')
  require('./FormFramework/grade.js')
  require('./FormFramework/businessStatus.js')
  require('./studentInfoManage/studentInfoManage.js')
  require('./cascader/cascaderLevel1.js')
  require('./cascader/cascaderLevel2.js')
  require('./cascader/cascaderLevel3.js')
  Mock.setup({
    timeout: 800 // setter delay time
  })
  console.log('[antd-pro] mock mounted')
}
