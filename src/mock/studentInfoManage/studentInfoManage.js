// mock-订单管理-保育费订单
import Mock from 'mockjs2'
import { builder, getQueryParameters } from '../util.js'
/**
 * builder组装返回值
 * getQueryParameters获取参数
 */
Mock.mock(/\/mock\/studentInfoManageGetTableData/, 'get', options => {
  const parameters = getQueryParameters(options) // 获取params参数
  console.log('studentInfoManageGetTableData', parameters)
  const totalCount = 866 //数据总量
  const result = [] //列表数据
  const pageNum = parseInt(parameters.pageNum) //选中的页
  const pageSize = parseInt(parameters.pageSize) //选中的分页规模
  const totalPage = Math.ceil(totalCount / pageSize) //总页数
  let next = null
  if (pageNum * pageSize - totalCount > 0) {
    next = totalCount - (pageNum - 1) * pageSize
  } else if (pageNum * pageSize - totalCount <= 0) {
    next = pageSize
  }

  for (let i = 0; i < next; i++) {
    result.push({
      key: Mock.mock('@id'),
      ID: Mock.mock('@id'),
      studentCode: Mock.mock('@id'),
      contactInfo: '学生联系方式',
      chargingStandard: '保育费标准',
      birthday: '生日',
      nationality: '国籍',
      address: '家庭住址',
      connectName: '联系人姓名',
      connectPhone: '联系电话',
      businessStatus: '业状',
      fatherPhone: '父亲电话',
      fatherName: '父亲姓名',
      motherName: '母亲姓名',
      motherPhone: '母亲电话',
      studentName: Object.values(
        Mock.mock({
          'array|1': ['张三', '李四', '王五', '赵六', '河南', '湖北']
        })
      ).join(),
      grade: Object.values(
        Mock.mock({
          'array|1': ['二年一班', '三年五班', '一年一班', '六年二班', '四年二班', '五年三班']
        })
      ).join(),
      nation: Object.values(
        Mock.mock({
          'array|1': ['汉族', '满族', '彝族']
        })
      ).join(),
      enterTime: Mock.mock('@date("yyyy-MM-dd HH:mm:ss")'),
      sex: Object.values(
        Mock.mock({
          'array|1': ['男', '女']
        })
      ).join(),
      status: Object.values(
        Mock.mock({
          'array|1': ['支付成功', '支付失败']
        })
      ).join()
    })
  }

  // if (Math.ceil(Math.random() * 10) > 3) {
  if (Math.ceil(Math.random() * 10) > 0) {
    return {
      data: {
        list: result,
        pageNum: pageNum,
        pageSize: pageSize,
        total: totalCount,
        totalPage: totalPage
      },
      resultCode: '0',
      resultMsg: 'success'
    }
  } else {
    return {
      data: [],
      resultCode: '1',
      resultMsg: 'error'
    }
  }
})
