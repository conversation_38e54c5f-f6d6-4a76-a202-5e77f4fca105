// mock-订单管理-保育费订单
import Mock from 'mockjs2'
import { builder, getQueryParameters } from '../util.js'
/**
 * builder组装返回值
 * getQueryParameters获取参数
 */
Mock.mock(/\/mock\/getTableData/, 'get', options => {
  const parameters = getQueryParameters(options) // 获取params参数
  console.log(parameters)
  const totalCount = 866 //数据总量
  const result = [] //列表数据
  const pageNum = parseInt(parameters.pageNum) //选中的页
  const pageSize = parseInt(parameters.pageSize) //选中的分页规模
  const totalPage = Math.ceil(totalCount / pageSize) //总页数
  let next = null
  if (pageNum * pageSize - totalCount > 0) {
    next = totalCount - (pageNum - 1) * pageSize
  } else if (pageNum * pageSize - totalCount <= 0) {
    next = pageSize
  }

  for (let i = 0; i < next; i++) {
    result.push({
      key: Mock.mock('@id'),
      code: Mock.mock('@id'),
      studentCode: Mock.mock('@id'),
      contactInfo: '学生联系方式',
      chargingStandard: '保育费标准',
      chargeItem: '收费项目',
      previousRemainingSum: '上期预收保育费余额',
      previousAttendanceDays: '上期出勤天数',
      previousChargePercentage: '上期收费比例',
      feesPayable: 'chargeAmount',
      studentName: Object.values(
        Mock.mock({
          'array|1': ['张三', '李四', '王五', '赵六', '河南', '湖北']
        })
      ).join(),
      grade: Object.values(
        Mock.mock({
          'array|1': ['二年一班', '三年五班', '一年一班', '六年二班', '四年二班', '五年三班']
        })
      ).join(),
      paymentMode: Object.values(
        Mock.mock({
          'array|1': ['微信', '支付宝', '银行卡']
        })
      ).join(),
      CREATETIME: Mock.mock('@date("yyyy-MM-dd HH:mm:ss")'),
      paymentStatus: Object.values(
        Mock.mock({
          'array|1': ['已支付', '待支付', '支付中']
        })
      ).join(),
      status: Object.values(
        Mock.mock({
          'array|1': ['支付成功', '支付失败']
        })
      ).join()
    })
  }

  // if (Math.ceil(Math.random() * 10) > 3) {
  if (Math.ceil(Math.random() * 10) > 0) {
    return {
      data: {
        list: result,
        pageNum: pageNum,
        pageSize: pageSize,
        total: totalCount,
        totalPage: totalPage
      },
      resultCode: '0',
      resultMsg: 'success'
    }
  } else {
    return {
      data: [],
      resultCode: '1',
      resultMsg: 'error'
    }
  }
})
