-- 验证数据库中的用户数据
-- 确保测试用户存在且密码正确

-- 1. 查看所有用户
SELECT 
    id,
    username,
    user_code,
    real_name,
    oa_name,
    email,
    status,
    last_login_time,
    login_failure_count,
    account_locked_time,
    create_time
FROM sys_user 
WHERE dr = 0
ORDER BY id;

-- 2. 查看用户角色关联
SELECT 
    u.username,
    u.real_name,
    r.role_code,
    r.role_name
FROM sys_user u
INNER JOIN sys_user_role ur ON u.id = ur.user_id
INNER JOIN sys_role r ON ur.role_id = r.id
WHERE u.dr = 0 AND ur.dr = 0 AND r.dr = 0
ORDER BY u.username, r.role_code;

-- 3. 验证密码格式（应该是BCrypt格式，以$2a$开头）
SELECT 
    username,
    CASE 
        WHEN password LIKE '$2a$%' THEN '✅ BCrypt格式正确'
        WHEN password LIKE '$2b$%' THEN '✅ BCrypt格式正确'
        WHEN password LIKE '$2y$%' THEN '✅ BCrypt格式正确'
        ELSE '❌ 密码格式错误'
    END as password_format,
    LENGTH(password) as password_length
FROM sys_user 
WHERE dr = 0;

-- 4. 检查用户状态
SELECT 
    username,
    CASE status 
        WHEN 1 THEN '✅ 启用'
        WHEN 0 THEN '❌ 禁用'
        ELSE '❓ 未知状态'
    END as status_desc,
    CASE 
        WHEN account_locked_time IS NULL THEN '✅ 未锁定'
        WHEN account_locked_time > NOW() THEN '❌ 已锁定'
        ELSE '✅ 锁定已过期'
    END as lock_status
FROM sys_user 
WHERE dr = 0;

-- 5. 测试用的明文密码对照（仅用于验证）
-- admin123 的BCrypt: $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKTcn5Uy6Q7J8yNWNQynVf2.Ek/2
-- manager123 的BCrypt: $2a$10$8.UrQjQKvNa8/Oy.3.5.5eKQjKtKtKtKtKtKtKtKtKtKtKtKtKtKt

SELECT '测试用户密码信息:' as info;
SELECT 'admin用户密码: admin123' as admin_info;
SELECT 'manager用户密码: manager123' as manager_info;
