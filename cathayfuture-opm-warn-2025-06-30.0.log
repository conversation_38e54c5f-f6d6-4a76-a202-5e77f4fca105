2025-06-30 09:36:14,995 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-06-30 09:36:15,007 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-30 09:36:15,271 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-06-30 09:36:18,201 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-06-30 09:36:18,204 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-06-30 15:44:13,305 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-06-30 15:44:13,307 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-30 15:44:13,591 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-06-30 15:44:16,520 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-06-30 15:44:16,524 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-06-30 16:17:59,751 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-06-30 16:17:59,754 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-30 16:18:00,022 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.actuate.endpoint.EndpointId [L:135] - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-06-30 16:18:02,892 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
2025-06-30 16:18:02,895 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.n.c.sources.URLConfigurationSource [L:121] - No URLs will be polled as dynamic configuration sources.
