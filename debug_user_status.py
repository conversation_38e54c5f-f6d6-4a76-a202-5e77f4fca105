#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试用户状态问题
"""

import requests
import json
from datetime import datetime, timed<PERSON>ta

def get_detailed_user_info():
    """获取详细的用户信息"""
    print("=== 获取详细用户信息 ===")
    
    base_url = "http://localhost:8080"
    url = f"{base_url}/bms/system/user/enabled"
    
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            users = response.json()
            
            for user in users:
                print(f"用户: {user['username']}")
                print(f"  ID: {user['id']}")
                print(f"  状态: {user['status']}")
                print(f"  密码更新时间: {user.get('passwordUpdateTime', 'null')}")
                print(f"  账户锁定时间: {user.get('accountLockedTime', 'null')}")
                print(f"  登录失败次数: {user.get('loginFailureCount', 0)}")
                print(f"  最后登录时间: {user.get('lastLoginTime', 'null')}")
                
                # 分析状态
                analyze_user_status(user)
                print()
            
            return users
        else:
            print(f"❌ 获取用户信息失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 异常: {e}")
        return []

def analyze_user_status(user):
    """分析用户状态"""
    print("  状态分析:")
    
    # 检查用户是否启用
    if user['status'] == 1:
        print("    ✅ 用户已启用")
    else:
        print("    ❌ 用户已禁用")
    
    # 检查账户锁定
    account_locked_time = user.get('accountLockedTime')
    if account_locked_time:
        print(f"    ⚠️ 账户锁定时间: {account_locked_time}")
        # 这里应该检查是否还在锁定期内，但需要解析时间格式
    else:
        print("    ✅ 账户未锁定")
    
    # 检查密码过期
    password_update_time = user.get('passwordUpdateTime')
    if password_update_time:
        print(f"    密码更新时间: {password_update_time}")
        # 这里应该检查是否超过90天，但需要解析时间格式
    else:
        print("    ⚠️ 密码更新时间为空")
    
    # 检查登录失败次数
    failure_count = user.get('loginFailureCount', 0)
    if failure_count > 0:
        print(f"    ⚠️ 登录失败次数: {failure_count}")
    else:
        print("    ✅ 无登录失败记录")

def test_user_creation_and_login():
    """测试用户创建和登录的完整流程"""
    print("=== 测试用户创建和登录流程 ===")
    
    base_url = "http://localhost:8080"
    
    # 创建一个新的测试用户
    test_user_data = {
        "username": "debuguser",
        "userCode": "DEBUG001",
        "password": "debug123",
        "realName": "调试用户",
        "oaName": "调试用户OA",
        "email": "<EMAIL>",
        "status": 1
    }
    
    print("1. 创建测试用户")
    try:
        create_response = requests.post(
            f"{base_url}/bms/system/user",
            json=test_user_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if create_response.status_code == 200:
            print("  ✅ 用户创建成功")
            created_user = create_response.json()
            print(f"  用户ID: {created_user['id']}")
            print(f"  密码哈希: {created_user['password']}")
        else:
            print(f"  ❌ 用户创建失败: {create_response.status_code}")
            print(f"  响应: {create_response.text}")
            return
    except Exception as e:
        print(f"  ❌ 创建用户异常: {e}")
        return
    
    print()
    print("2. 立即测试登录")
    try:
        login_response = requests.post(
            f"{base_url}/bms/auth/login",
            json={"username": "debuguser", "password": "debug123"},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if login_response.status_code == 200:
            print("  ✅ 登录成功")
            login_data = login_response.json()
            if login_data.get("token"):
                print(f"  Token: {login_data['token'][:30]}...")
        else:
            print(f"  ❌ 登录失败: {login_response.status_code}")
            try:
                error_data = login_response.json()
                print(f"  错误: {error_data.get('error', '未知错误')}")
            except:
                print(f"  响应: {login_response.text}")
    except Exception as e:
        print(f"  ❌ 登录异常: {e}")

def test_password_update():
    """测试密码更新"""
    print("=== 测试密码更新 ===")
    
    base_url = "http://localhost:8080"
    
    # 尝试更新admin用户的密码
    print("尝试更新admin用户密码...")
    
    # 首先获取admin用户的ID
    users_response = requests.get(f"{base_url}/bms/system/user/enabled")
    if users_response.status_code == 200:
        users = users_response.json()
        admin_user = next((u for u in users if u['username'] == 'admin'), None)
        
        if admin_user:
            admin_id = admin_user['id']
            print(f"找到admin用户，ID: {admin_id}")
            
            # 尝试更新密码
            update_data = {
                "id": admin_id,
                "username": "admin",
                "userCode": "ADMIN001",
                "password": "admin123",  # 明文密码，应该会被加密
                "realName": "系统管理员",
                "oaName": "系统管理员",
                "status": 1
            }
            
            try:
                update_response = requests.put(
                    f"{base_url}/bms/system/user/{admin_id}",
                    json=update_data,
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
                
                if update_response.status_code == 200:
                    print("  ✅ 密码更新成功")
                    updated_user = update_response.json()
                    print(f"  新密码哈希: {updated_user.get('password', 'N/A')}")
                    
                    # 立即测试登录
                    print("  测试更新后的登录...")
                    login_response = requests.post(
                        f"{base_url}/bms/auth/login",
                        json={"username": "admin", "password": "admin123"},
                        headers={"Content-Type": "application/json"},
                        timeout=10
                    )
                    
                    if login_response.status_code == 200:
                        print("  ✅ 更新后登录成功！")
                    else:
                        print(f"  ❌ 更新后登录失败: {login_response.status_code}")
                else:
                    print(f"  ❌ 密码更新失败: {update_response.status_code}")
                    print(f"  响应: {update_response.text}")
            except Exception as e:
                print(f"  ❌ 更新异常: {e}")
        else:
            print("  ❌ 未找到admin用户")
    else:
        print("  ❌ 获取用户列表失败")

def main():
    """主函数"""
    print("用户状态调试")
    print("=" * 50)
    
    # 获取详细用户信息
    users = get_detailed_user_info()
    
    print()
    
    # 测试用户创建和登录
    test_user_creation_and_login()
    
    print()
    
    # 测试密码更新
    test_password_update()
    
    print()
    print("=" * 50)
    print("调试总结:")
    print("1. 检查用户状态字段")
    print("2. 验证密码哈希生成")
    print("3. 测试完整的认证流程")

if __name__ == "__main__":
    main()
