#!/bin/bash

# 测试登录并查看详细日志
echo "=== 测试登录并查看详细日志 ==="

SERVER_URL="http://localhost:8080"

echo "1. 检查服务器状态"
if curl -s --connect-timeout 5 "${SERVER_URL}/actuator/health" > /dev/null 2>&1; then
    echo "✅ 服务器正在运行"
else
    echo "❌ 服务器未运行，等待启动..."
    sleep 10
fi

echo ""
echo "2. 测试admin用户登录"
echo "发送登录请求..."

curl -v -X POST \
  "${SERVER_URL}/bms/auth/login" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }' 2>&1

echo ""
echo ""
echo "3. 测试testuser用户登录（已知可以工作的用户）"
echo "发送登录请求..."

curl -v -X POST \
  "${SERVER_URL}/bms/auth/login" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "username": "testuser",
    "password": "test123"
  }' 2>&1

echo ""
echo ""
echo "测试完成，请查看应用日志中的详细认证信息"
