<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-23 13:59:03
 * @Description:
-->

English | [简体中文](./README.zh-CN.md)

### 2022/05/23 更新内容

1. codebase 集成 idp 所需的`chart`目录，`.gitlab-ci.yml`文件以及`docker`目录，其中，每次我们部署 codebase 到 idp 时，需要将`chart`文件夹中所有`pc-vue-huaxiaweilai`替换为 idp 中项目实际名称。
2. 当 codebase 不应用于 idp 上时，可以删除以上三个文件或目录
3. 为了配合`.gitlab-ci.yml`脚本，`package.json`中添加了`clean`, `build:stage`,`build:prod`script 脚本，不需要可以删除

开发环境账号：wangrx 密码：Tasly123
