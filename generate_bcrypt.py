#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
生成正确的BCrypt密码哈希
"""

import bcrypt

def generate_bcrypt_hash(password):
    """生成BCrypt哈希"""
    # 生成salt并加密密码
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def verify_bcrypt_hash(password, hashed):
    """验证BCrypt哈希"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def main():
    """主函数"""
    print("=== BCrypt密码哈希生成器 ===")
    
    # 生成测试用户的密码哈希
    passwords = [
        ("admin", "admin123"),
        ("manager", "manager123")
    ]
    
    print("生成新的BCrypt哈希:")
    print()
    
    for username, password in passwords:
        # 生成哈希
        hashed = generate_bcrypt_hash(password)
        
        # 验证哈希
        is_valid = verify_bcrypt_hash(password, hashed)
        
        print(f"用户: {username}")
        print(f"密码: {password}")
        print(f"BCrypt哈希: {hashed}")
        print(f"验证结果: {'✅ 正确' if is_valid else '❌ 错误'}")
        print()
    
    print("=== SQL更新语句 ===")
    print()
    
    # 生成SQL更新语句
    admin_hash = generate_bcrypt_hash("admin123")
    manager_hash = generate_bcrypt_hash("manager123")
    
    print("-- 更新用户密码的SQL语句")
    print(f"UPDATE sys_user SET password = '{admin_hash}' WHERE username = 'admin';")
    print(f"UPDATE sys_user SET password = '{manager_hash}' WHERE username = 'manager';")
    print()
    
    print("=== 数据库初始化脚本 ===")
    print()
    print("-- 插入用户数据（使用正确的BCrypt哈希）")
    print("INSERT INTO sys_user (username, user_code, password, real_name, oa_name, status, password_update_time) VALUES")
    print(f"('admin', 'ADMIN001', '{admin_hash}', '系统管理员', '系统管理员', 1, NOW()),")
    print(f"('manager', 'MGR001', '{manager_hash}', '业务管理员', '业务管理员', 1, NOW());")

if __name__ == "__main__":
    main()
