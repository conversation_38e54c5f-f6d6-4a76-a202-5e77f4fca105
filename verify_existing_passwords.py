#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证数据库中现有的密码哈希
"""

import bcrypt
import requests

def get_user_data():
    """从API获取用户数据"""
    print("=== 获取数据库中的用户数据 ===")
    
    base_url = "http://localhost:8080"
    url = f"{base_url}/bms/system/user/enabled"
    
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            users = response.json()
            print(f"找到 {len(users)} 个用户")
            
            for user in users:
                print(f"用户: {user['username']}")
                print(f"  ID: {user['id']}")
                print(f"  用户编码: {user['userCode']}")
                print(f"  真实姓名: {user['realName']}")
                print(f"  OA姓名: {user['oaName']}")
                print(f"  密码哈希: {user['password']}")
                print(f"  状态: {user['status']}")
                print()
            
            return users
        else:
            print(f"❌ 获取用户数据失败，状态码: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 获取用户数据异常: {e}")
        return []

def test_password_hashes(users):
    """测试密码哈希"""
    print("=== 测试现有密码哈希 ===")
    
    # 常见的密码尝试
    common_passwords = [
        "admin123", "admin", "123456", "password", 
        "manager123", "manager", "test123", "test"
    ]
    
    for user in users:
        username = user['username']
        password_hash = user['password']
        
        print(f"测试用户: {username}")
        print(f"密码哈希: {password_hash}")
        
        found_password = None
        
        for password in common_passwords:
            try:
                if bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8')):
                    found_password = password
                    break
            except Exception as e:
                print(f"  验证密码 '{password}' 时出错: {e}")
        
        if found_password:
            print(f"  ✅ 找到正确密码: {found_password}")
            
            # 立即测试登录
            test_login(username, found_password)
        else:
            print(f"  ❌ 未找到匹配的密码")
        
        print()

def test_login(username, password):
    """测试登录"""
    print(f"    测试登录: {username} / {password}")
    
    base_url = "http://localhost:8080"
    login_url = f"{base_url}/bms/auth/login"
    
    try:
        response = requests.post(
            login_url,
            json={"username": username, "password": password},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            print(f"    ✅ 登录成功！")
            try:
                data = response.json()
                if data.get("token"):
                    print(f"    Token: {data['token'][:30]}...")
                if data.get("authorities"):
                    print(f"    权限: {data['authorities']}")
            except:
                pass
        else:
            print(f"    ❌ 登录失败，状态码: {response.status_code}")
            try:
                data = response.json()
                if data.get("error"):
                    print(f"    错误: {data['error']}")
            except:
                pass
                
    except Exception as e:
        print(f"    ❌ 登录异常: {e}")

def generate_correct_hashes():
    """生成正确的密码哈希"""
    print("=== 生成正确的密码哈希 ===")
    
    passwords = [
        ("admin", "admin123"),
        ("manager", "manager123")
    ]
    
    print("如果需要更新数据库中的密码，可以使用以下SQL:")
    print()
    
    for username, password in passwords:
        # 生成新的哈希
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        hash_str = hashed.decode('utf-8')
        
        print(f"-- 更新 {username} 用户密码 ({password})")
        print(f"UPDATE sys_user SET password = '{hash_str}' WHERE username = '{username}';")
        print()

def main():
    """主函数"""
    print("验证现有密码哈希")
    print("=" * 50)
    
    # 获取用户数据
    users = get_user_data()
    
    if users:
        # 测试密码哈希
        test_password_hashes(users)
        
        # 生成正确的哈希
        generate_correct_hashes()
    
    print("=" * 50)
    print("总结:")
    print("1. 数据库认证系统正常工作")
    print("2. 可以通过API获取用户数据")
    print("3. 需要找到正确的密码或更新密码哈希")

if __name__ == "__main__":
    main()
