#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复admin和manager用户密码
使用Spring Boot兼容的BCrypt格式
"""

import requests
import json

def fix_user_password(username, password):
    """修复用户密码"""
    print(f"=== 修复 {username} 用户密码 ===")
    
    base_url = "http://localhost:8080"
    
    # 1. 创建临时用户来获取正确的密码哈希
    temp_username = f"temp_{username}"
    temp_user = {
        "username": temp_username,
        "userCode": f"TEMP_{username.upper()}",
        "password": password,
        "realName": f"临时{username}",
        "oaName": f"临时{username}",
        "email": f"temp_{username}@example.com",
        "status": 1
    }
    
    try:
        # 创建临时用户
        create_response = requests.post(
            f"{base_url}/bms/system/user",
            json=temp_user,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if create_response.status_code != 200:
            print(f"❌ 创建临时用户失败: {create_response.status_code}")
            return False
        
        temp_created = create_response.json()
        correct_hash = temp_created['password']
        temp_id = temp_created['id']
        
        print(f"✅ 获取到正确的密码哈希: {correct_hash}")
        
        # 2. 获取目标用户信息
        users_response = requests.get(f"{base_url}/bms/system/user/enabled")
        if users_response.status_code != 200:
            print("❌ 获取用户列表失败")
            return False
        
        users = users_response.json()
        target_user = next((u for u in users if u['username'] == username), None)
        
        if not target_user:
            print(f"❌ 未找到用户: {username}")
            return False
        
        print(f"找到目标用户: {username} (ID: {target_user['id']})")
        
        # 3. 更新目标用户密码
        update_data = target_user.copy()
        update_data['password'] = password  # 使用明文密码，让Spring Boot重新加密
        
        update_response = requests.put(
            f"{base_url}/bms/system/user/{target_user['id']}",
            json=update_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if update_response.status_code != 200:
            print(f"❌ 更新用户密码失败: {update_response.status_code}")
            return False
        
        updated_user = update_response.json()
        new_hash = updated_user['password']
        print(f"✅ 密码更新成功，新哈希: {new_hash}")
        
        # 4. 测试登录
        print("测试登录...")
        login_response = requests.post(
            f"{base_url}/bms/auth/login",
            json={"username": username, "password": password},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if login_response.status_code == 200:
            print(f"🎉 {username} 登录成功！")
            login_data = login_response.json()
            if login_data.get("token"):
                print(f"Token: {login_data['token'][:30]}...")
            if login_data.get("authorities"):
                print(f"权限: {login_data['authorities']}")
            success = True
        else:
            print(f"❌ {username} 登录失败: {login_response.status_code}")
            try:
                error_data = login_response.json()
                print(f"错误: {error_data.get('error')}")
            except:
                pass
            success = False
        
        # 5. 清理临时用户
        print("清理临时用户...")
        try:
            delete_response = requests.delete(
                f"{base_url}/bms/system/user/{temp_id}",
                timeout=10
            )
            if delete_response.status_code == 200:
                print("✅ 临时用户已清理")
            else:
                print(f"⚠️ 临时用户清理失败: {delete_response.status_code}")
        except:
            print("⚠️ 临时用户清理异常")
        
        return success
        
    except Exception as e:
        print(f"❌ 修复过程异常: {e}")
        return False

def test_all_users():
    """测试所有用户登录"""
    print("=== 测试所有用户登录 ===")
    
    base_url = "http://localhost:8080"
    
    test_cases = [
        ("admin", "admin123"),
        ("manager", "manager123"),
        ("testuser", "test123"),
        ("debuguser", "debug123")
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for username, password in test_cases:
        print(f"测试 {username}...")
        
        try:
            login_response = requests.post(
                f"{base_url}/bms/auth/login",
                json={"username": username, "password": password},
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if login_response.status_code == 200:
                print(f"  ✅ 登录成功")
                success_count += 1
            else:
                print(f"  ❌ 登录失败: {login_response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 登录异常: {e}")
    
    print(f"\n登录成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    return success_count == total_count

def main():
    """主函数"""
    print("修复admin和manager用户密码")
    print("=" * 50)
    
    # 修复admin用户
    admin_success = fix_user_password("admin", "admin123")
    
    print("\n" + "=" * 50)
    
    # 修复manager用户
    manager_success = fix_user_password("manager", "manager123")
    
    print("\n" + "=" * 50)
    
    # 测试所有用户
    all_success = test_all_users()
    
    print("\n" + "=" * 50)
    print("修复总结:")
    print(f"Admin用户: {'✅ 成功' if admin_success else '❌ 失败'}")
    print(f"Manager用户: {'✅ 成功' if manager_success else '❌ 失败'}")
    print(f"所有用户测试: {'✅ 全部成功' if all_success else '❌ 部分失败'}")
    
    if admin_success and manager_success:
        print("\n🎉 数据库用户认证系统修复完成！")
        print("现在可以使用以下凭据登录:")
        print("- admin / admin123")
        print("- manager / manager123")
    else:
        print("\n⚠️ 部分用户修复失败，请检查日志")

if __name__ == "__main__":
    main()
