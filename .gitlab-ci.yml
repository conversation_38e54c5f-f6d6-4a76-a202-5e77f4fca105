image: registry.cn-hangzhou.aliyuncs.com/yx-repo/yunxicibase:0.3.0-debian

stages:
  - build
  - release
build:
  stage: build
  script:
    # - sonar-scanner -Dsonar.host.url=$SONAR_URL -Dsonar.login=$SONAR_LOGIN -Dsonar.gitlab.project_id=$CI_PROJECT_PATH -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA  -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME -Dsonar.analysis.serviceGroup=$GROUP_NAME -Dsonar.analysis.commitId=$CI_COMMIT_SHA -Dsonar.projectKey=${GROUP_NAME}:${PROJECT_NAME} -Dsonar.sources=.
    - npm run clean
    - npm i  --registry https://npm.tasly.com --sass-binary-site=http://npmmirror.com/mirrors/node-sass
    - chmod -R 777 node_modules
    - if [ "$CI_COMMIT_REF_NAME" == "prod" ]; then npm run build:prod; fi
    - if [ "$CI_COMMIT_REF_NAME" == "stage" ]; then npm run build:stage; fi
    - if [ "$CI_COMMIT_REF_NAME" == "uat" ]; then npm run build:uat; fi
    - if [ "$CI_COMMIT_REF_NAME" == "dev" ]; then npm run build:dev; fi
    - mkdir -p /cache/${CI_PROJECT_NAME}-${CI_PROJECT_ID}-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA}/
    - cp -r dist /cache/${CI_PROJECT_NAME}-${CI_PROJECT_ID}-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA}/
release:
  stage: release
  script:
    - cp -rf /cache/${CI_PROJECT_NAME}-${CI_PROJECT_ID}-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA}/dist/ docker/
    - kaniko --cache=true  --cache-dir=/cache/kaniko-cache  --skip-tls-verify=true -c $PWD/docker/ -f $PWD/docker/Dockerfile -d ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG}
    - chart_build
    #
.auto_devops: &auto_devops |
  http_status_code=`curl -o .auto_devops.sh -s -m 10 --connect-timeout 10 -w %{http_code} "${CHOERODON_URL}/devops/ci?token=${Token}&type=front"`
  if [ "$http_status_code" != "200" ]; then
    cat .auto_devops.sh
    exit 1
  fi
  source .auto_devops.sh

before_script:
  - *auto_devops
