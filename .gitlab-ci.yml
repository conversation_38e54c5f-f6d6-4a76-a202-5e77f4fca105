image: harbor.dtyunxi.cn/library/yunxicibase:0.3.0

stages:
  - sonar-scan
  - mvn-package
  - docker-build
  - release

#sonar:
#  stage: sonar-scan
#  script:
#    - sonar_scan
#  only:
#    - master
#    - /^feature.*$/

sonar-scan:
  stage: sonar-scan
  only:
    variables:
      - $ON_SCHEDULE == "1"
  script:
    - mvn -DskipTests=true --batch-mode  verify sonar:sonar  -Dsonar.host.url=$SONAR_URL -Dsonar.login=$SONAR_LOGIN -Dsonar.gitlab.project_id=$CI_PROJECT_PATH -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME -Dsonar.analysis.serviceGroup=$GROUP_NAME -Dsonar.analysis.commitId=$CI_COMMIT_SHA -Dsonar.projectKey=${GROUP_NAME}:${PROJECT_NAME}



maven-test-docker:
  stage: mvn-package
  script:
    # - update_pom_version
    - mvn package -U -DskipTests=true
    # - mvn clean package -Dmaven.test.skip=true -DskipTests=true -Pspringcloud -Dyunxi-dev
#    - mvn clean deploy org.jacoco:jacoco-maven-plugin:prepare-agent -Dmaven.test.failure.ignore=true -DskipTests=true -U -X
    # - echo $SONAR_URL
    # - echo $SONAR_LOGIN
    - mkdir -p /cache/${CI_PROJECT_NAME}-${CI_PROJECT_ID}-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA}
    - cp cathayfuture-opm-adapter/target/cathayfuture-opm.jar /cache/${CI_PROJECT_NAME}-${CI_PROJECT_ID}-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA}/app.jar
  only:
    refs:
      - tags
      - master
      - stage
      - uat
      - release
      - features
      - /^features.*$/
      - /^release-.*$/
      - /^hotfix-.*$/
#   except:
#     variables:
#       - $ON_SCHEDULE != "1"

docker-build:
  stage: docker-build
  script:
    - cp /cache/${CI_PROJECT_NAME}-${CI_PROJECT_ID}-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA}/app.jar ${1:-"cathayfuture-opm-adapter/src/main/docker"}/app.jar || true
    - kaniko --cache --cache-dir /cache/kaniko-cache --skip-tls-verify=true --skip-tls-verify-pull=true -c $PWD/cathayfuture-opm-adapter/src/main/docker/ -f $PWD/cathayfuture-opm-adapter/src/main/docker/Dockerfile -d ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG}
  only:
    refs:
      - tags
      - master
      - stage
      - uat
      - release
      - features
      - /^features.*$/
      - /^release-.*$/
      - /^hotfix-.*$/

release:
  stage: release
  script:
    - chart_build
  only:
    refs:
      - tags
      - master
      - stage
      - uat
      - release
      - features
      - /^features.*$/
      - /.*-cube-snapshot$/
      - /^release-.*$/
      - /^hotfix-.*$/

.auto_devops: &auto_devops |
    http_status_code=`curl -o .auto_devops.sh -s -m 10 --connect-timeout 10 -w %{http_code} "${CHOERODON_URL}/devops/ci?token=${Token}&type=microservice"`
    if [ "$http_status_code" != "200" ]; then
      cat .auto_devops.sh
      exit 1
    fi
    source .auto_devops.sh
    function docker_build(){
        cp /cache/${CI_PROJECT_NAME}-${CI_PROJECT_ID}-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA}/app.jar ${1:-"src/main/docker"}/app.jar || true
        docker login -u ${DOCKER_USER} -p ${DOCKER_PWD} ${DOCKER_REGISTRY}
        docker build --pull -t ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG} ${1:-"src/main/docker"}
        docker push ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG}
        rm -rf /cache/${CI_PROJECT_NAME}-${CI_PROJECT_ID}-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA}
    }

    function sonar_scan(){
        mvn --batch-mode  verify sonar:sonar  -Dsonar.host.url=$SONAR_URL -Dsonar.login=$SONAR_LOGIN -Dsonar.gitlab.project_id=$CI_PROJECT_PATH -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME -Dsonar.analysis.serviceGroup=$GROUP_NAME -Dsonar.analysis.commitId=$CI_COMMIT_SHA -Dsonar.projectKey=${GROUP_NAME}:${PROJECT_NAME} -X
        sleep 15s
        curl http://idp-sonar.tasly.com/api/qualitygates/project_status?projectKey=operation-tasly:tasly-center-contract >sonar_scan_result.json
        cat sonar_scan_result.json
        cat sonar_scan_result.json | grep  '"projectStatus":{"status":"OK"'
    }


before_script:
  - *auto_devops
