# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Cathayfuture OPM (Operations Management) is a Spring Boot-based multi-module Java application for educational institution management, built with DDD (Domain-Driven Design) architecture. The system manages students, orders, attendance, and payment processing for educational institutions.

## Architecture

The project follows a layered DDD architecture with 5 main modules:

- **cathayfuture-opm-domain**: Core domain entities, enums, and repository interfaces
- **cathayfuture-opm-infra**: Infrastructure layer implementing repositories and external services
- **cathayfuture-opm-app**: Application services and business logic orchestration  
- **cathayfuture-opm-client**: API contracts, DTOs, and exceptions
- **cathayfuture-opm-adapter**: Controllers, configuration, and external adapters (main executable module)

## Build and Run Commands

### Prerequisites
- Java 8 (JDK 1.8)
- Maven 3.x
- Nacos for service discovery and configuration
- MySQL database
- Redis for caching

### Build Commands
```bash
# Build entire project
mvn clean compile

# Package application
mvn clean package

# Install to local repository
mvn clean install

# Build without tests (no test directory exists)
mvn clean package -DskipTests
```

### Run Commands
```bash
# Run from adapter module
cd cathayfuture-opm-adapter
mvn spring-boot:run

# Or run packaged jar
java -jar cathayfuture-opm-adapter/target/cathayfuture-opm.jar
```

## Key Technologies

- **Spring Boot 2.1.4**: Main framework
- **Spring Cloud Greenwich.SR5**: Microservices stack
- **Nacos 2.1.2**: Service discovery and configuration management
- **MyBatis Plus 3.4.2**: ORM framework
- **MySQL 5.1.47**: Database
- **Redis**: Caching and distributed locking
- **WeChat Mini Program SDK**: Mobile integration
- **JWT**: Authentication tokens
- **Docker**: Containerization support

## Domain Models

### Core Business Domains
- **Student Management**: Student entities, classes, expense standards, and rate relief
- **Order Management**: Order processing, payment details, monthly statements, and charge records  
- **Attendance Management**: Attendance records, leave requests, and calendar management
- **Account Management**: User accounts and WeChat integration
- **Action Tracking**: Operation history and audit logs

### Key Entities
- `StudentEntity`: Student information and enrollment details
- `OrderEntity`: Order transactions and payment status
- `AttendanceRecordEntity`: Daily attendance tracking
- `PaymentDetailEntity`: Payment processing records
- `AccountEntity`: User account management

## API Structure

### BMS (Backend Management System) APIs
- `/bms/students/**` - Student management
- `/bms/orders/**` - Order management  
- `/bms/attendanceRecord/**` - Attendance management
- `/bms/actions/**` - Operation history

### Mobile APIs  
- `/mobile/account/**` - Mobile account management
- `/mobile/orders/**` - Mobile order operations
- `/mobile/students/**` - Mobile student information

## Configuration

- **Bootstrap Config**: `cathayfuture-opm-adapter/src/main/resources/bootstrap.yml`
- **Nacos Integration**: Uses shared config `yundt-cube-commons.yaml`
- **Service Name**: `cathayfuture-opm`
- **Main Class**: `cathayfuture.opm.App`

## Database

- Uses MyBatis Plus with automatic code generation
- All entities extend `BaseEntity` with common audit fields (tenant_id, create_time, etc.)
- Soft delete implemented via `@TableLogic` annotation
- Custom SQL injection for advanced queries via `ExtBaseMapper`

## Development Notes

- No test directory exists - consider adding unit/integration tests when implementing new features
- WeChat Mini Program integration for mobile access
- Multi-tenant architecture with tenant_id in base entity
- Distributed locking implemented via Redis
- Excel import/export functionality for bulk operations
- Comprehensive operation logging via ActionEntity

## External Integrations

- **Alibaba Cloud SMS**: SMS verification services
- **OSS**: Object storage for file uploads
- **WeChat API**: Mini program authentication and user management
- **Taslyware Excel Tools**: Custom Excel processing utilities